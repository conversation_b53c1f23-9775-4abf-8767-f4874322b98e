// components/orderList/orderList.js
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        info: {
            type: Object,
            value: {}
        },
    },

    /**
     * 组件的初始数据
     */
    data: {},

    /**
     * 组件的方法列表
     */
    methods: {
        checkDetail(e) {
            let item = e.currentTarget.dataset.item;
            wx.vibrateShort({
                type: "heavy",
                success: (res) => {
                },
            });
            wx.navigateTo({
                url: '/nm/pages/proportionTrakingDetail/proportionTrakingDetail?info=' + JSON.stringify(item),
            })
        }
    }
})
