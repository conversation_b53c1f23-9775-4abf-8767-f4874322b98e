/* pages/material/material.wxss */
.sub-button {
    margin-top: 80rpx;
    margin-bottom: 80rpx;
}

page {
    background: #f5f5f5;
}

.title {
    font-weight: bold;
}

.weui-form-preview__label {
    float: left;
    margin-right: 1em;
    min-width: 4em;
    color: var(--weui-FG-1);
    text-align: justify;
    text-align-last: justify;
}

.loadding {
    display: flex;
    flex-direction: row;
    justify-content: space-between
}

.tuzhi {
    padding-top: 20rpx;
    padding-left: 25rpx;
    padding-right: 25rpx;
    font-size: 28rpx;
    color: #737475;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.tld {
    width: 0.7rem;
    vertical-align: top;
    margin-top: 1px;
}

.check-box {
    display: flex;
    margin-top: 20rpx;
    margin-left: 360rpx
}

.sub-button {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 80rpx;
    margin-bottom: 80rpx;
}

.v-button {
    height: 68rpx;
    width: 300rpx;
}

.tld-class {
    display: flex;
    height: 60 rxp;
    flex-direction: row;
}

.tld-w {
    color: #646566;
    font-size: 29rpx;
    line-height: 60rpx;
    margin-left: 10rpx;
}

.input-class1 {
    width: 80rpx;
    line-height: 60rpx;
    margin-left: 370rpx;
    margin-right: 10rpx;
    padding: 6rpx;
    border: 1px solid rgb(214, 214, 217);
}

.input-class2 {
    line-height: 60rpx;
    margin-left: 10rpx;
    width: 80rpx;
    padding: 6rpx;
    border: 1px solid rgb(214, 214, 217)
}

.sup {
    color: red;
    vertical-align: text-top;
    margin-left: 14rpx;
    font-size: 27.5rpx;
}

.container {
    background-color: white;
    display: flex;
    padding: 25rpx;
    font-size: 28rpx;
    margin: 25rpx;
    border-radius: 20rpx;
    flex-direction: column;
}

.top {
    display: flex;
    font-size: 25rpx;
    flex-direction: row;
}

.top1 {
    color: grey;
    flex: 0.4
}

.top2 {
    flex: 1
}

.top3 {
    flex: 0.3;
    padding: 4rpx;
    border-radius: 25rpx;
    background-color: #f5f5f5;
    color: blue;
    text-align: center
}

.center-content1 {
    display: flex;
    margin-top: 20rpx;
    padding: 10rpx;
    border-radius: 20rpx;
    background-color: #f5f5f5;
    justify-content: space-evenly;
}

.center-content {
    margin-top: 20rpx;
}

.center-title {
    font-size: bold;
}

.center-content1-first {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
    flex-direction: column;
}

.center-first-title {
    color: grey;

}

.center-content1-third {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
    flex-direction: column;
}

.center-content1-second {
    display: flex;
    justify-content: space-evenly;
    text-align: center;
    flex-direction: column;
}

.center-content2 {
    margin-top: 20rpx;
}

.center-content3 {
    margin-top: 20rpx;
}

.center-content4 {
    margin-top: 20rpx;
}

.content {
    color: grey;
}

.content1 {
    display: flex;
    justify-content: space-evenly;
}

.bottom-content {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.bottom-content1 {
    color: grey;
    flex: 1
}

.bottom-content2 {
    flex: 1;
    color: blue;
}

.bottom-button1 {
    flex: 1;
    padding-left: 10rpx;

}

.bottom-button2 {
    margin-left: 60%;

}
