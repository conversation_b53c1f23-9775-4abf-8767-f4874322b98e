// components/showInfo.js
import {color} from "../../config";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    info: {
      type: Array,
      default: [],
    },
    activeNames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    color:color
  },
  pageLifetimes: {
    show: function() {
    },
    hide: function() {
      // 页面被隐藏
      //this.videoControl.pause()
    },
    resize: function(size) {
      // 页面尺寸变化
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onChange(event) {
      this.setData({
        activeNames: event.detail,
      });
    },
    selectContent(e){
      this.triggerEvent('navPage', e.currentTarget.dataset.id)
    },
  },
})
