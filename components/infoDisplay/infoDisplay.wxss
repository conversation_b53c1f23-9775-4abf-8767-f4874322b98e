/* pages/orderDetail/orderDetail.wxss */
.main{
    display: flex;
    margin: 30rpx;
    padding: 30rpx;
    font-size: 30rxp;
    background-color: white;
    border-radius: 30rpx;
    flex-direction: column;
}
.content{
    margin-bottom: 200rpx;
}
.title{
    font-weight: bold;
    padding-bottom: 20rpx;
    border-bottom-style:solid;
    border-bottom-width:1rpx;
    border-bottom-color: #f1f1f1;
}
.ele{
    margin-top: 10rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.sub-title{
    color: #8a8a8a;
}
page{
    background-color: #f1f1f1;
}
