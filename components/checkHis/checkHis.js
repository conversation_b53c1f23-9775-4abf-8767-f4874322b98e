// components/checkHis/checkHis.js
import { getLocCheckHisById } from "../../service/checkHis.js";
import {color} from "../../config";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    tel: {
      type: String,
      default: null,
    },
    activeNames: {
      type: Array,
      default: [],
    },
    info: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    show:false,
    color:color,
    controll: false,
    detailInfo: [],
    waterpic: [],
    maintainpic: [],
    proc:'get_locCheck_his_byid'
  },

  /**
   * 组件的方法列表
   */
  onLoad(){
    this.setData({
      activeNames: ['1'],
    });
  },
  methods: {
    onChange(event) {
      this.setData({
        activeNames: event.detail,
      });
    },
    toStrength(e){
      let info=JSON.stringify(e.currentTarget.dataset.info)
      wx.navigateTo({
        url: `/pages/skDetail/skDetail?info=${info}`,
      });
    },
    async get_unCheck_content(e) {
      let id = e.currentTarget.dataset.id;
      await this.getDetail(id);
    },
    async getDetail(id) {
      let res = await getLocCheckHisById(this.data.proc, this.data.tel, id,wx.getStorageSync('apiUrl'));
      res = res.data;
      let detailInfo=res.recordsets[0][0]
      detailInfo.seven=JSON.parse(detailInfo.seven)
      detailInfo.fourteen=JSON.parse(detailInfo.fourteen)
      detailInfo.twentyEighth=JSON.parse(detailInfo.twentyEighth)
      this.setData({
        detailInfo: detailInfo,
        waterpic: res.recordsets[1],
        maintainpic: res.recordsets[2],
      });
      console.log(this.data.detailInfo)
    },
    checkdetail(e){
      console.log(e)
      let info=e.currentTarget.dataset.info
      this.triggerEvent('myevent',info)
    },
    getImage(e) {
      wx.previewImage({
        urls: [e.currentTarget.dataset.img], //需要预览的图片http链接列表，注意是数组
        current: "", // 当前显示图片的http链接，默认是第一个
        success: function (res) {},
        fail: function (res) {},
        complete: function (res) {},
      });
    },
  },
});
