<wxs src="../../utils/formatUrl.wxs" module="utils" />
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl" />
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl" />
<view wx:for="{{info}}" wx:key="index">
  <van-collapse value="{{activeNames}}" class="e-block" bind:change="onChange">
      <view class="ele">
      <van-collapse-item name="{{index}}" is-link="{{controll}}">
      <view slot="title">
        <view bindtap="get_unCheck_content" data-id="{{item.id}}">
          <view class="text1">{{ item.orderid }}</view>
          <view class="title">
            <view class="items">
              <view class="ele text3">
                施工部位：{{ item.constructionSite }}
              </view>
              <view class="ele text6"> 提交时间:{{ item.insert_date }} </view>
            </view>
          </view>
        </view>
      </view>
      <van-cell
        title="1-混凝土卸料浇注时是否有擅自加水等违规操作"
        style="font-weight: bold"
      />
      <van-radio-group
        readonly
        value="{{detailInfo.isWaterViolation}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        {{detailInfo.isWaterViolation}}
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.isWaterViolation==='1'}}">
        <van-row wx:for="{{waterpic}}" wx:key="item">
          <van-col span="8">
            <van-image
              use-error-slot
              use-loading-slot
              bindtap="getImage"
              data-img="{{item.url}}"
              lazy-load
              width="100"
              height="100"
              src="{{item.url}}"
            >
              <text slot="error">加载失败</text>
              <van-loading slot="loading" type="spinner" size="20" vertical />
            </van-image>
          </van-col>
        </van-row>
      </view>
      <van-cell
        title="2-混凝土浇注完毕的养护过程是否有违规操作"
        style="font-weight: bold"
      />
      <van-radio-group
        readonly
        value="{{detailInfo.isMaintainViolation}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.isMaintainViolation.toString()==='1'}}">
        <van-row wx:for="{{maintainpic}}" wx:key="item">
          <van-col span="8">
            <van-image
              use-error-slot
              use-loading-slot
              bindtap="getImage"
              data-img="{{item.url}}"
              lazy-load
              width="100"
              height="100"
              src="{{item.url}}"
            >
              <text slot="error">加载失败</text>
              <van-loading slot="loading" type="spinner" size="20" vertical />
            </van-image>
          </van-col>
        </van-row>
      </view>
      <van-cell title="A-----7天检测" style="font-weight: bold" />
      <van-cell title="a）试块强度是否检测" />
      <van-radio-group
        readonly
        value="{{detailInfo.seven.sk.is}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.seven.sk.is === '1'}}">
        <van-cell title="是否记录每次检测结果" />
        <van-radio-group
          readonly
          value="{{detailInfo.seven.sk.isexmp}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view
          class="upload-view"
          wx:if="{{detailInfo.seven.sk.isexmp === '2'}}"
        >
          <van-cell-group border="{{controll}}" class="m-field">
            <van-field
              readonly
              type="digit"
              value="{{detailInfo.seven.sk.datanum}}"
              label="数据标本数"
              placeholder="请输入数据标本数"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.seven.sk.max}}"
              label="最大值(MPa)"
              placeholder="请输入最大值"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.seven.sk.min}}"
              label="最小值(MPa)"
              placeholder="请输入最小值"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.seven.sk.avg}}"
              label="平均值(MPa)"
              placeholder="请输入平均值"
              input-align="right"
            />
          </van-cell-group>
        </view>
        <view
          class="upload-view"
          wx:if="{{detailInfo.seven.sk.isexmp === '1'}}"
        >
          <van-cell-group border="{{controll}}" class="m-field">
            <van-field
              readonly
              value="{{detailInfo.seven.sk.avgall.value}}"
              label="平均强度值"
              input-align="right"
            />
          </van-cell-group>
          <van-button
            class="pos-button"
            custom-class="button"
            type="primary"
            color="{{ color }}"
            data-info="{{detailInfo.seven.sk.sknum}}"
            bindtap="checkdetail"
            >点击查看每次记录</van-button
          >
        </view>
        <van-cell title="是否拍照" border="{{controll}}" />
        <van-radio-group
          readonly
          value="{{detailInfo.seven.sk.ispic}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view class="upload-view" wx:if="{{detailInfo.seven.sk.ispic === '1'}}">
          <view class="tupload">上传图片(最多3张)</view>
          <van-row wx:for="{{detailInfo.seven.sk.fileStrengths}}" wx:key="item">
            <van-col span="8">
              <van-image
                use-error-slot
                use-loading-slot
                bindtap="getImage"
                data-img="{{item.url}}"
                lazy-load
                width="100"
                height="100"
                src="{{item.url}}"
              >
                <text slot="error">加载失败</text>
                <van-loading slot="loading" type="spinner" size="20" vertical />
              </van-image>
            </van-col>
          </van-row>
        </view>
      </view>
      <van-cell title="b）回弹强度是否检测" />
      <van-radio-group
        readonly
        value="{{detailInfo.seven.ht.is}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.seven.ht.is === '1'}}">
        <van-field
                readonly="{{true}}"
                value="{{detailInfo.seven.ht.htInfo.strength}}"
                label="强度值"
                placeholder=""
                data-info="{{detailInfo.seven.ht.htInfo}}"
                bindtap="toStrength"
                input-align="right"
        />
<!--        <van-cell title="是否记录每次检测结果" />-->
<!--        <van-radio-group-->
<!--          readonly-->
<!--          value="{{detailInfo.seven.ht.isexmp}}"-->
<!--          direction="horizontal"-->
<!--          class="m-select"-->
<!--        >-->
<!--          <van-radio name="1" checked-color="{{color}}">是</van-radio>-->
<!--          <van-radio name="2" class="no" checked-color="{{color}}"-->
<!--            >否</van-radio-->
<!--          >-->
<!--        </van-radio-group>-->
<!--        <view-->
<!--          class="upload-view"-->
<!--          wx:if="{{detailInfo.seven.ht.isexmp === '2'}}"-->
<!--        >-->
<!--          <van-cell-group border="{{controll}}" class="m-field">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="digit"-->
<!--              value="{{detailInfo.seven.ht.datanum}}"-->
<!--              label="数据标本数"-->
<!--              placeholder="请输入数据标本数"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.seven.ht.max}}"-->
<!--              label="最大值(MPa)"-->
<!--              placeholder="请输入最大值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.seven.ht.min}}"-->
<!--              label="最小值(MPa)"-->
<!--              placeholder="请输入最小值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.seven.ht.avg}}"-->
<!--              label="平均值(MPa)"-->
<!--              placeholder="请输入平均值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--        </view>-->
<!--        <view-->
<!--          class="upload-view"-->
<!--          wx:if="{{detailInfo.seven.ht.isexmp === '1'}}"-->
<!--        >-->
<!--          <van-cell-group border="{{controll}}" class="m-field">-->
<!--            <van-field-->
<!--              readonly-->
<!--              value="{{detailInfo.seven.ht.avgall.value}}"-->
<!--              label="平均强度值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-button-->
<!--            class="pos-button"-->
<!--            custom-class="button"-->
<!--            color="{{ color }}"-->
<!--            type="primary"-->
<!--            data-info="{{detailInfo.seven.ht.sknum}}"-->
<!--            bindtap="checkdetail"-->
<!--            >点击查看每次记录</van-button-->
<!--          >-->
<!--        </view>-->
        <van-cell title="是否拍照" border="{{controll}}" />
        <van-radio-group
          readonly
          value="{{detailInfo.seven.ht.ispic}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view class="upload-view" wx:if="{{detailInfo.seven.ht.ispic === '1'}}">
          <view class="tupload">上传图片(最多3张)</view>
          <van-row wx:for="{{detailInfo.seven.ht.fileStrengths}}" wx:key="item">
            <van-col span="8">
              <van-image
                use-error-slot
                use-loading-slot
                bindtap="getImage"
                data-img="{{item.url}}"
                lazy-load
                width="100"
                height="100"
                src="{{item.url}}"
              >
                <text slot="error">加载失败</text>
                <van-loading slot="loading" type="spinner" size="20" vertical />
              </van-image>
            </van-col>
          </van-row>
        </view>
      </view>
      <van-cell title="B-----14天检测" style="font-weight: bold" />
      <van-cell title="a）试块强度是否检测" />
      <van-radio-group
        readonly
        value="{{detailInfo.fourteen.sk.is}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.fourteen.sk.is === '1'}}">
        <van-cell title="是否记录每次检测结果" />
        <van-radio-group
          readonly
          value="{{detailInfo.fourteen.sk.isexmp}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view
          class="upload-view"
          wx:if="{{detailInfo.fourteen.sk.isexmp === '2'}}"
        >
          <van-cell-group border="{{controll}}" class="m-field">
            <van-field
              readonly
              type="digit"
              value="{{detailInfo.fourteen.sk.datanum}}"
              label="数据标本数"
              placeholder="请输入数据标本数"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.fourteen.sk.max}}"
              label="最大值(MPa)"
              placeholder="请输入最大值"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.fourteen.sk.min}}"
              label="最小值(MPa)"
              placeholder="请输入最小值"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.fourteen.sk.avg}}"
              label="平均值(MPa)"
              placeholder="请输入平均值"
              input-align="right"
            />
          </van-cell-group>
        </view>
        <view
          class="upload-view"
          wx:if="{{detailInfo.fourteen.sk.isexmp === '1'}}"
        >
          <van-cell-group border="{{controll}}" class="m-field">
            <van-field
              readonly
              value="{{detailInfo.fourteen.sk.avgall.value}}"
              label="平均强度值"
              input-align="right"
            />
          </van-cell-group>
          <van-button
            class="pos-button"
            custom-class="button"
            type="primary"
            color="{{ color }}"
            data-info="{{detailInfo.fourteen.sk.sknum}}"
            bindtap="checkdetail"
            >点击查看每次记录</van-button
          >
        </view>
        <van-cell title="是否拍照" border="{{controll}}" />
        <van-radio-group
          readonly
          value="{{detailInfo.fourteen.sk.ispic}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view
          class="upload-view"
          wx:if="{{detailInfo.fourteen.sk.ispic === '1'}}"
        >
          <view class="tupload">上传图片(最多3张)</view>
          <van-row
            wx:for="{{detailInfo.fourteen.sk.fileStrengths}}"
            wx:key="item"
          >
            <van-col span="8">
              <van-image
                use-error-slot
                use-loading-slot
                bindtap="getImage"
                data-img="{{item.url}}"
                lazy-load
                width="100"
                height="100"
                src="{{item.url}}"
              >
                <text slot="error">加载失败</text>
                <van-loading slot="loading" type="spinner" size="20" vertical />
              </van-image>
            </van-col>
          </van-row>
        </view>
      </view>
      <van-cell title="b）回弹强度是否检测" />
      <van-radio-group
        readonly
        value="{{detailInfo.fourteen.ht.is}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.fourteen.ht.is === '1'}}">
        <van-field
                readonly="{{true}}"
                value="{{detailInfo.fourteen.ht.htInfo.strength}}"
                label="强度值"
                placeholder=""
                data-info="{{detailInfo.fourteen.ht.htInfo}}"
                bindtap="toStrength"
                input-align="right"
        />
<!--        <van-cell title="是否记录每次检测结果" />-->
<!--        <van-radio-group-->
<!--          readonly-->
<!--          value="{{detailInfo.fourteen.ht.isexmp}}"-->
<!--          direction="horizontal"-->
<!--          class="m-select"-->
<!--        >-->
<!--          <van-radio name="1" checked-color="{{color}}">是</van-radio>-->
<!--          <van-radio name="2" class="no" checked-color="{{color}}"-->
<!--            >否</van-radio-->
<!--          >-->
<!--        </van-radio-group>-->
<!--        <view-->
<!--          class="upload-view"-->
<!--          wx:if="{{detailInfo.fourteen.ht.isexmp === '2'}}"-->
<!--        >-->
<!--          <van-cell-group border="{{controll}}" class="m-field">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="digit"-->
<!--              value="{{detailInfo.fourteen.ht.datanum}}"-->
<!--              label="数据标本数"-->
<!--              placeholder="请输入数据标本数"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.fourteen.ht.max}}"-->
<!--              label="最大值(MPa)"-->
<!--              placeholder="请输入最大值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.fourteen.ht.min}}"-->
<!--              label="最小值(MPa)"-->
<!--              placeholder="请输入最小值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.fourteen.ht.avg}}"-->
<!--              label="平均值(MPa)"-->
<!--              placeholder="请输入平均值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--        </view>-->
<!--        <view-->
<!--          class="upload-view"-->
<!--          wx:if="{{detailInfo.fourteen.ht.isexmp === '1'}}"-->
<!--        >-->
<!--          <van-cell-group border="{{controll}}" class="m-field">-->
<!--            <van-field-->
<!--              readonly-->
<!--              value="{{detailInfo.fourteen.ht.avgall.value}}"-->
<!--              label="平均强度值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-button-->
<!--            type="primary"-->
<!--            class="pos-button"-->
<!--            custom-class="button"-->
<!--            color="{{ color }}"-->
<!--            data-info="{{detailInfo.fourteen.ht.sknum}}"-->
<!--            bindtap="checkdetail"-->
<!--            >点击查看每次记录</van-button-->
<!--          >-->
<!--        </view>-->
        <van-cell title="是否拍照" border="{{controll}}" />
        <van-radio-group
          readonly
          value="{{detailInfo.fourteen.ht.ispic}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view
          class="upload-view"
          wx:if="{{detailInfo.fourteen.ht.ispic === '1'}}"
        >
          <view class="tupload">上传图片(最多3张)</view>
          <van-row
            wx:for="{{detailInfo.fourteen.ht.fileStrengths}}"
            wx:key="item"
          >
            <van-col span="8">
              <van-image
                use-error-slot
                use-loading-slot
                bindtap="getImage"
                data-img="{{item.url}}"
                lazy-load
                width="100"
                height="100"
                src="{{item.url}}"
              >
                <text slot="error">加载失败</text>
                <van-loading slot="loading" type="spinner" size="20" vertical />
              </van-image>
            </van-col>
          </van-row>
        </view>
      </view>
      <van-cell title="C-----28天检测" style="font-weight: bold" />
      <van-cell title="a）试块强度是否检测" />
      <van-radio-group
        readonly
        value="{{detailInfo.twentyEighth.sk.is}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.twentyEighth.sk.is === '1'}}">
        <van-cell title="是否记录每次检测结果" />
        <van-radio-group
          readonly
          value="{{detailInfo.twentyEighth.sk.isexmp}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view
          class="upload-view"
          wx:if="{{detailInfo.twentyEighth.sk.isexmp === '2'}}"
        >
          <van-cell-group border="{{controll}}" class="m-field">
            <van-field
              readonly
              type="digit"
              value="{{detailInfo.twentyEighth.sk.datanum}}"
              label="数据标本数"
              placeholder="请输入数据标本数"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.twentyEighth.sk.max}}"
              label="最大值(MPa)"
              placeholder="请输入最大值"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.twentyEighth.sk.min}}"
              label="最小值(MPa)"
              placeholder="请输入最小值"
              input-align="right"
            />
          </van-cell-group>
          <van-cell-group border="{{controll}}">
            <van-field
              readonly
              type="number"
              value="{{detailInfo.twentyEighth.sk.avg}}"
              label="平均值(MPa)"
              placeholder="请输入平均值"
              input-align="right"
            />
          </van-cell-group>
        </view>
        <view
          class="upload-view"
          wx:if="{{detailInfo.twentyEighth.sk.isexmp === '1'}}"
        >
          <van-cell-group border="{{controll}}" class="m-field">
            <van-field
              readonly
              value="{{detailInfo.twentyEighth.sk.avgall.value}}"
              label="平均强度值"
              input-align="right"
            />
          </van-cell-group>
          <van-button
            class="pos-button"
            custom-class="button"
            type="primary"
            color="{{ color }}"
            data-info="{{detailInfo.twentyEighth.sk.sknum}}"
            bindtap="checkdetail"
            >点击查看每次记录</van-button
          >
        </view>
        <van-cell title="是否拍照" border="{{controll}}" />
        <van-radio-group
          readonly
          value="{{detailInfo.twentyEighth.sk.ispic}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view
          class="upload-view"
          wx:if="{{ detailInfo.twentyEighth.sk.ispic === '1'}}"
        >
          <view class="tupload">上传图片(最多3张)</view>
          <van-row
            wx:for="{{detailInfo.twentyEighth.sk.fileStrengths}}"
            wx:key="item"
          >
            <van-col span="8">
              <van-image
                use-error-slot
                use-loading-slot
                bindtap="getImage"
                data-img="{{item.url}}"
                lazy-load
                width="100"
                height="100"
                src="{{item.url}}"
              >
                <text slot="error">加载失败</text>
                <van-loading slot="loading" type="spinner" size="20" vertical />
              </van-image>
            </van-col>
          </van-row>
        </view>
      </view>
      <van-cell title="b）回弹强度是否检测" />
      <van-radio-group
        readonly
        value="{{detailInfo.twentyEighth.ht.is}}"
        direction="horizontal"
        class="m-select"
      >
        <van-radio name="1" checked-color="{{color}}">是</van-radio>
        <van-radio name="2" class="no" checked-color="{{color}}">否</van-radio>
      </van-radio-group>
      <view wx:if="{{detailInfo.twentyEighth.ht.is === '1'}}">
        <van-field
                readonly="{{true}}"
                value="{{detailInfo.twentyEighth.ht.htInfo.strength}}"
                label="强度值"
                data-info="{{detailInfo.twentyEighth.ht.htInfo}}"
                bindtap="toStrength"
                placeholder=""
                input-align="right"
        />
<!--        <van-cell title="是否记录每次检测结果" />-->
<!--        <van-radio-group-->
<!--          readonly-->
<!--          value="{{detailInfo.twentyEighth.ht.isexmp}}"-->
<!--          direction="horizontal"-->
<!--          class="m-select"-->
<!--        >-->
<!--          <van-radio name="1" checked-color="{{color}}">是</van-radio>-->
<!--          <van-radio name="2" class="no" checked-color="{{color}}"-->
<!--            >否</van-radio-->
<!--          >-->
<!--        </van-radio-group>-->
<!--        <view-->
<!--          class="upload-view"-->
<!--          wx:if="{{detailInfo.twentyEighth.ht.isexmp === '2'}}"-->
<!--        >-->
<!--          <van-cell-group border="{{controll}}" class="m-field">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="digit"-->
<!--              value="{{detailInfo.twentyEighth.ht.datanum}}"-->
<!--              label="数据标本数"-->
<!--              placeholder="请输入数据标本数"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.twentyEighth.ht.max}}"-->
<!--              label="最大值(MPa)"-->
<!--              placeholder="请输入最大值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.twentyEighth.ht.min}}"-->
<!--              label="最小值(MPa)"-->
<!--              placeholder="请输入最小值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-cell-group border="{{controll}}">-->
<!--            <van-field-->
<!--              readonly-->
<!--              type="number"-->
<!--              value="{{detailInfo.twentyEighth.ht.avg}}"-->
<!--              label="平均值(MPa)"-->
<!--              placeholder="请输入平均值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--        </view>-->
<!--        <view-->
<!--          class="upload-view"-->
<!--          wx:if="{{detailInfo.twentyEighth.ht.isexmp === '1'}}"-->
<!--        >-->
<!--          <van-cell-group border="{{controll}}" class="m-field">-->
<!--            <van-field-->
<!--              readonly-->
<!--              value="{{detailInfo.twentyEighth.ht.avgall.value}}"-->
<!--              label="平均强度值"-->
<!--              input-align="right"-->
<!--            />-->
<!--          </van-cell-group>-->
<!--          <van-button-->
<!--            class="pos-button"-->
<!--            custom-class="button"-->
<!--            type="primary"-->
<!--            color="{{ color }}"-->
<!--            data-info="{{detailInfo.twentyEighth.ht.sknum}}"-->
<!--            bindtap="checkdetail"-->
<!--            >点击查看每次记录</van-button-->
<!--          >-->
<!--        </view>-->
        <van-cell title="是否拍照" border="{{controll}}" />
        <van-radio-group
          readonly
          value="{{detailInfo.twentyEighth.ht.ispic}}"
          direction="horizontal"
          class="m-select"
        >
          <van-radio name="1" checked-color="{{color}}">是</van-radio>
          <van-radio name="2" class="no" checked-color="{{color}}"
            >否</van-radio
          >
        </van-radio-group>
        <view
          class="upload-view"
          wx:if="{{ detailInfo.twentyEighth.ht.ispic === '1'}}"
        >
          <view class="tupload">上传图片(最多3张)</view>
          <van-row
            wx:for="{{detailInfo.twentyEighth.ht.fileStrengths}}"
            wx:key="item"
          >
            <van-col span="8">
              <van-image
                use-error-slot
                use-loading-slot
                bindtap="getImage"
                data-img="{{item.url}}"
                lazy-load
                width="100"
                height="100"
                src="{{item.url}}"
              >
                <text slot="error">加载失败</text>
                <van-loading slot="loading" type="spinner" size="20" vertical />
              </van-image>
            </van-col>
          </van-row>
        </view>
      </view>
    </van-collapse-item>
      </view>
  </van-collapse>
</view>
