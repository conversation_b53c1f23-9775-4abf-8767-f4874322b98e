.base-title {
    font-weight: bold;
}
.base-info {
    margin-top: 3%;
}

.mtitle {
    font-weight: bold;
}

.back {
    margin-top: -1.5%;
}
.top {
    display: flex;
    justify-content: space-between;
    padding-top: 4%;
    padding-right: 2%;
    font-size: 0.3rem;
    line-height: 0.3rem;
    padding-bottom: 4%;
    background-color: #3c62bc;
    color: white;
}
.full-page {
    background-color: #fafafa;
}
.bottom-button {
    margin-top: 15%;
    text-align: center;
    margin-bottom: 3rem;
}
.upload {
    margin-top: 2%;
    margin-left: 3%;
    margin-bottom: 2%;
}
.tupload {
    margin-left: 3%;
    margin-bottom: 2%;
    padding-top: 3%;
    padding-bottom: 2%;
}
.upload-div {
    background-color: white;
}
.m-select {
    display: flex;
    flex-direction: row;
    margin-top: 0.5rem;
    margin-left: 0.5rem;
}
.no{
    margin-left:20rpx;
}
.m-group {
    background-color: white;
}
.upload-div {
    padding-top: 0.5rem;
}
.van-button--info {
    background-color: #3c62bc;
    border: 1px solid #3c62bc;
}
.van-button {
    width: 3.5rem;
}
.m-field {
    padding-top: 0.3rem;
}
.me-select {
    margin-top: 0.5rem;
    margin-left: 0.5rem;
    padding-bottom: 0.3rem;
}
.footer {
    height: 100px;
    background-color: #fafafa;
}
.e-block {
    background-color: white;
    border-bottom: 0.2px solid #f7f8fa;
    border-radius: 0.2rem;
    padding: 1% 1% 1%;
    margin: 2.5% 2% 2%;
}
.qd {
    margin-left: 0.3rem;
    margin-top: 0.5rem;
    font-weight: bold;
}
.ac {
    margin-left: 2rem;
}
.check-button{
    margin-left: 2rem;
}
.button{
    height: 68rpx;
    width: 340rpx;
}
.pos-button{
    margin-left:200rpx
}
.ele{
    padding-left:20rpx;
    padding-right: 20rpx;
    padding-bottom: 28rpx;
}