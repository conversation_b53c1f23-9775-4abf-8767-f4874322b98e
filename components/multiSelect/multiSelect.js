// components/multiSelect/multiSelect.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    info: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
  },

  /**
   * 组件的方法列表
   */
  methods: {
    chooseItem(e) {
      let item = e.currentTarget.dataset.item;
      let index = e.currentTarget.dataset.index;
      this.setData({
        [`info[${index}].isSelect`]: !this.data.info[index].isSelect,
      });

      this.triggerEvent("factory", this.data.info);
    },
  },
});
