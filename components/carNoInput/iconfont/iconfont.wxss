@font-face {
    font-family: "iconfont";
    src: url('iconfont.eot?t=1548061553140'); /* IE9 */
    src: url('iconfont.eot?t=1548061553140#iefix') format('embedded-opentype'), /* IE6-IE8 */ url('data:application/x-font-woff2;charset=utf-8;base64,d09GMgABAAAAAAOcAAsAAAAAB7wAAANPAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHEIGVgCDHAqCfII4ATYCJAMQCwoABCAFhG0HRBumBsgusG3Yk0AyWSBSiG3Nbrnj8+iJB/j98Z773v2wBzgvLS0BamaeI1z3Q1mSopCESf/qswcxJ62qE/SQ7Dp72Zyk6j5UlPL0Ysn+O+UWBwElFKAkZzwe5P0/MK23uOD/55jp0hIwP9Acqxv3hgMctw4sKql9YDthPBX0mg6XpO0E5GqUSDKjsLwe2ImMfgFhhUbFAbuUUqzJDLIFmSVTY1/YkCE72Z/cxVH/78MHw4IdiYyUsdPsRQUiyH6N16/a9n/C3HTenvH0kGeRYgAgEpaUGuexDIMDWMpVWlsZBciWLWGshrLXC7P3Isw2/+URpBJCJiM9H4zgUMpriS3gizzBLBP9RJoEXsiUbGRiMWAnA1fva4IoVJV5ODuXbJ04CDjzutvutcpFew+rFu8/C/bqn+HdXRC3nXLIP1C5aG/Sd/G+Y1EovNi94Npl3MLrUQVX/YEZR3faK9rcXPZeuDtxjT91o03s8vUJrwU7JCBN3ljy472laxNVH10umWK2+HLDs35r/PjTqr9P2rrt/+dpH9H/5Ya43XlGlOQbp6v32tZlLWT+ykyNXb1DgWdZng2Y6Es9Tmlywv5Zdff/keqKjq5I8vdVlC9Ul/jNV1R4KWGSe6VViznkwiilL90w4Mt3b05OVvZePmdLYP1+FRPg/1l8m+6F9HHyjGgem0j/z35ut12611WU/tWOETzRXepBfj/mS2a1Zv0NebxVgzhX5RiBGMFMn93eJZqcGChBrlz8h7XUpzHEFUo6QbaKGBJZGiCVrR0ZsQMgQ54RkCnbZMjVp2h2njKUpRBz0GuWICh2FxKF+iFV7A0yYj9Chkq/IFNxsIRcc4Lnknk6g8YhjyKOIRHOG4RLBWLEloY5BJm1SNWr52iS45MbETVrPHCoVJ6NFCEjomOMWN+nUm5gGC6ixABn0W5IrydwDSVaJDDpEsZMqYNluOxO0vnEABxMRyEcBhFBvEGQhoAwwm5pxiH3+1qISi89Dq0pqDA2QigzTfOQgpS8BbJIaGxVcC23mPVRUWIYDMIowgAqJD2iN4MEZCrvpIUIGCl1D79JKhkXw22F0vnFhidcBrmMpWJIQhpiyBDncYLORt2rYVaOA0cp6U9aYBuMAAAA') format('woff2'),
    url('iconfont.woff?t=1548061553140') format('woff'),
    url('iconfont.ttf?t=1548061553140') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */ url('iconfont.svg?t=1548061553140#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
    font-family: "iconfont" !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-back:before {
    content: "\e601";
}

.icon-huitui:before {
    content: "\e6e5";
}

.icon-arrow-down:before {
    content: "\e613";
}
