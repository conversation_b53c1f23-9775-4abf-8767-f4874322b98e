.keyboard-box {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 999;
}

.u-keyboard {
    position: relative;
    z-index: 1003;
}

.u-tooltip {
    display: flex;
    justify-content: space-between;
    background-color: #ffffff;
}

.u-tooltip-item {
    color: #333333;
    flex: 0 0 33.333333%;
    text-align: center;
    padding: 20rpx 10rpx;
    font-size: 28rpx;
}

.u-tooltips-submit {
    text-align: right;
    flex-grow: 1;
    flex-wrap: 0;
    padding-right: 40rpx;
    color: #2979ff;
}

.u-tooltip-cancel {
    text-align: left;
    flex-grow: 1;
    flex-wrap: 0;
    padding-left: 40rpx;
    color: #888888;
}

.u-tooltips-submit-hover {
    color: #19be6b;
}

.u-tooltip-cancel-hover {
    color: #333333;
}

.u-keyboard-grids {
    background: rgb(215, 215, 217);
    padding: 24rpx 0;
    position: relative;
    text-align: center;
}

.u-keyboard-grids-item {
    display: inline-block;
}

.u-keyboard-grids-btn {
    text-decoration: none;
    width: 62rpx;
    flex: 0 0 64rpx;
    height: 80rpx;
    display: inline-block;
    font-size: 36rpx;
    text-align: center;
    line-height: 80rpx;
    background-color: #fff;
    margin: 8rpx 5rpx;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 0rpx #888992;
    font-weight: 500;
}

.u-carinput-hover {
    background-color: rgb(185, 188, 195) !important;
}

.u-keyboard-back {
    position: absolute;
    width: 96rpx;
    right: 22rpx;
    bottom: 32rpx;
    height: 80rpx;
    background-color: rgb(185, 188, 195);
    display: flex;
    align-items: center;
    border-radius: 8rpx;
    justify-content: center;
    box-shadow: 0 2rpx 0rpx #888992;
}

.u-keyboard-change {
    font-size: 24rpx;
    box-shadow: 0 2rpx 0rpx #888992;
    position: absolute;
    width: 96rpx;
    left: 22rpx;
    line-height: 1;
    bottom: 32rpx;
    height: 80rpx;
    background-color: #ffffff;
    display: flex;
    align-items: center;
    border-radius: 8rpx;
    justify-content: center;
}

.u-keyboard-change .inactive.zh {
    transform: scale(0.85) translateY(-10rpx);
}

.u-keyboard-change .inactive.en {
    transform: scale(0.85) translateY(10rpx);
}

.u-keyboard-change .active {
    color: rgb(237, 112, 64);
    font-size: 30rpx;
}

.u-keyboard-change .zh {
    transform: translateY(-10rpx);
}

.u-keyboard-change .en {
    transform: translateY(10rpx);
}

.backward {
    width: 60%;
    height: 70%;
    filter: contrast(50%);
}
