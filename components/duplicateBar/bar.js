import * as echarts from "../../ec-canvas/echarts";

const app = getApp();

function setOptionc1(chart, title, barData, subTitle) {

    console.log(barData, 'bar3')

    let xname = barData[0].xname
    let yname = barData[0].yname
    let series = barData[0].series

    const posList = [
        'left',
        'right',
        'top',
        'bottom',
        'inside',
        'insideTop',
        'insideLeft',
        'insideRight',
        'insideBottom',
        'insideTopLeft',
        'insideTopRight',
        'insideBottomLeft',
        'insideBottomRight'
    ];
    app.configParameters = {
        rotate: {
            min: -90,
            max: 90
        },
        align: {
            options: {
                left: 'left',
                center: 'center',
                right: 'right'
            }
        },
        verticalAlign: {
            options: {
                top: 'top',
                middle: 'middle',
                bottom: 'bottom'
            }
        },
        position: {
            options: posList.reduce(function (map, pos) {
                map[pos] = pos;
                return map;
            }, {})
        },
        distance: {
            min: 0,
            max: 100
        }
    };
    app.config = {
        rotate: 90,
        align: 'left',
        verticalAlign: 'middle',
        position: 'insideBottom',
        distance: 15,
        onChange: function () {
            const labelOption = {
                rotate: app.config.rotate,
                align: app.config.align,
                verticalAlign: app.config.verticalAlign,
                position: app.config.position,
                distance: app.config.distance
            };
            myChart.setOption({
                series: [
                    {
                        label: labelOption
                    },
                    {
                        label: labelOption
                    },
                    {
                        label: labelOption
                    },
                    {
                        label: labelOption
                    }
                ]
            });
        }
    };
    const labelOption = {
            show: true,
            position: app.config.position,
            distance: app.config.distance,
            align: app.config.align,
            verticalAlign: app.config.verticalAlign,
            rotate: app.config.rotate,
            fontSize: 16,
            rich: {
                name: {}
            }
        },
        option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                }
            },
            title: {
                text: title,
                textStyle: {
                    fontSize: 20,
                },
                subtext: subTitle,
            },
            legend: {
                data: yname,
                top: '15%',
            },
            toolbox: {
                show: true,
                orient: 'vertical',
                left: 'right',
                top: 'center',
                feature: {
                    mark: {show: true},
                    dataView: {show: true, readOnly: false},
                    magicType: {show: true, type: ['line', 'bar', 'stack']},
                    restore: {show: true},
                    saveAsImage: {show: true}
                }
            },
            xAxis: [
                {
                    type: 'category',
                    axisTick: {show: false},
                    data: xname,
                    axisLabel: {
                        rotate: -42,
                        textStyle: {
                            color: '#999'
                        }
                    },
                }
            ],
            yAxis: [
                {
                    type: 'value'
                }
            ],
            series: series.map((item) => ({
                ...item,
                label: labelOption,
            }))
            //     [
            //     {
            //         name: '实际重量',
            //         type: 'bar',
            //         barGap: 0,
            //         label: labelOption,
            //         emphasis: {
            //             focus: 'series'
            //         },
            //         data: [320, 332, 301, 334, 390]
            //     },
            //     {
            //         name: '运单重量',
            //         type: 'bar',
            //         label: labelOption,
            //         emphasis: {
            //             focus: 'series'
            //         },
            //         data: [220, 182, 191, 234, 290]
            //     },
            //     {
            //         name: '供方偏差',
            //         type: 'bar',
            //         label: labelOption,
            //         emphasis: {
            //             focus: 'series'
            //         },
            //         data: [150, 232, 201, 154, 190]
            //     }
            // ]
        }

    chart.setOption(option);
}

Component({
    /**
     * 组件的属性列表
     */
    properties: {
        barData: {
            type: Array,
            observer: async function (newVal, oldVal) {
                if (newVal) {
                    console.log('newVal', newVal)
                    await this.GetFigureData(newVal)
                }
            }
        },
        title: {
            type: String,

        },
        subTitle: {
            type: String,
        },
    },

    /**
     * 组件的初始数据
     */
    data: {
        ec_bar: {
            // 将 lazyLoad 设为 true 后，需要手动初始化图表
            lazyLoad: true,

        },
        isDisposed: false,
        isLoaded: false,
    },
    lifetimes: {
        attached: function () {
            // 在组件实例进入页面节点树时执行
            // 页面被展示
            this.GetFigureData()

        },
        detached: function () {
            // 在组件实例被从页面节点树移除时执行
            if (this.chartc) {
                this.chartc.dispose();
            }
            this.setData({
                isDisposed: true,
            });
        },
        ready: function () {
            // 在组件实例被从页面节点树移除时执行

        },
        show: function () {

        },

    },

    /**
     * 组件的方法列表
     */
    methods: {
        initc_day1: function (title, barData, subTitle) {
            this.ecComponent_day1.init(async (canvas, width, height, dpr) => {
                console.log(2222)
                console.log(barData, 'bar2')
                // 获取组件的 canvas、width、height 后的回调函数
                // 在这里初始化图表
                const chart = echarts.init(canvas, null, {
                    width: width,
                    height: height,
                    devicePixelRatio: dpr, // new
                });
                await setOptionc1(chart, title, barData, subTitle);

                // 将图表实例绑定到 this 上，可以在其他成员函数（如 dispose）中访问
                this.chartc = chart;

                this.setData({
                    isLoaded: true,
                    isDisposed: false,
                });

                // 注意这里一定要返回 chart 实例，否则会影响事件处理等
                return chart;
            });
        },
        async GetFigureData(barData) {

            this.ecComponent_day1 = this.selectComponent("#mychart-dom-line");
            console.log(barData, 'bar')
            await this.initc_day1(this.data.title, barData, this.data.subTitle);
        },
    }
})
