// components/upload.js
import {baseURL, camera} from "../config.js";

Component({
    /**
     * 组件的属性列表
     */
    properties: {
        tel: {
            type: String,
            default: "199",
        },
        proc: {
            type: String,
            default: "",
        },
        acceptType: {
            type: String,
            default: "image",
        },
        num: {
            type: Number,
            default: 1,
        },
        isClear: {
            type: Boolean,
            default: "",
        },
        gs: {
            type: String,
            default: "image/*",
        },
        type: {
            type: String,
            default: "",
        },
    },
    observers: {
        num: function (res) {
            // 在 numberA 或者 numberB 被设置时，执行这个函数
            this.setData({
                number: res,
            });
        },
        isClear: function (res) {
            // 在 numberA 或者 numberB 被设置时，执行这个函数
            this.setData({
                fileList: [],
            });
        },
    },
    /**
     * 组件的初始数据
     */
    data: {
        camera: camera,
        maxDuration: 15,
        showFiles: [],
        files: {
            name: "",
            type: "",
        },
        fileList: [],
        number: 1,
        result: null,
        headerImage: null,
        picValue: null,
    },

    /**
     * 组件的方法列表
     */

    methods: {
        afterRead(event) {
            const {file} = event.detail;
            let isLt1M = file.size / 1024 / 1024 <= 5;
            if (!isLt1M) {
                wx.showToast({
                    title: `大小要在5M以内！`,
                });
                return;
            }
            wx.showLoading({
                title: "文件上传中",
            });
            // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式
            wx.uploadFile({
                url: baseURL + "wxminiuploadFile", // 仅为示例，非真实的接口地址
                filePath: file.tempFilePath ? file.tempFilePath : file.path,
                name: "file",
                header: {
                    "Content-Type": "multipart/form-data",
                    tel: this.data.tel,
                    type: this.data.type,
                    proc: this.data.proc,
                    url: wx.getStorageSync("apiUrl"),
                },

                success: (res) => {
                    wx.hideLoading()
                    res = JSON.parse(res.data);
                    console.log(555);
                    console.log(res);
                    let id = res.data[0].id;

                    let fileurl = res.data[0].url;
                    fileurl = baseURL + fileurl;
                    let comp = {url: fileurl, name: id, isVideo: true};
                    let fileList = this.data.fileList;
                    fileList.push(comp);
                    this.setData({
                        fileList,
                    });
                    this.triggerEvent("change-file", fileList);
                },
                fail: function (err) {
                    wx.hideLoading()
                    console.log(err);
                },
            });
        },
        beforeRead(event) {
        },
        delete(event) {
            let filename = event.detail.file.name;
            let fileList = this.data.fileList;
            for (let i = 0; i < fileList.length; i++) {
                if (fileList[i].name === filename) {
                    fileList.splice(i, 1);
                    break;
                }
            }
            this.setData({
                fileList,
            });
            this.triggerEvent("change-file", fileList);
        },
    },
});
