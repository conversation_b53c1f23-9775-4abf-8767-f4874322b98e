import {
  searchKeys
} from "../../service/searchChoose.js";

Component({
  data: {
    inputShowed: false,
    inputVal: "",
    showSearch: true,
    inputShow: false,
    project: null,
    items:[]
  },
  properties: {
    showwx: {
      type: String,
      default: '搜索',
    },
    passid: {
      type: String,
      default: null,
    },
    seachKeyProc: {
      type: String,
      default: null,
    },
    title: {
      type: String,
      default: '单元',
    },
  },
  lifetimes: {
    attached: function () {
      this.setData({
        search: this.search.bind(this),
      });
    },
  },
  methods: {
    search: function (value) {
      return new Promise((resolve, reject) => {
        setTimeout(() => {
          searchKeys(this.data.seachKeyProc,value,this.data.passid,wx.getStorageSync('apiUrl')).then((res)=>{
                let arr=[]
                this.setData(
                    {
                      items:res.data
                    }
                )
                for(let i=0;i<res.data.length;i++){
                  console.log(res.data[i])
                  arr.push({text: res.data[i].keyWord, value:res.data[i].id })
                }
                resolve(arr)
              }
          )
        }, 200)
      })
    },
    transSearch() {
      this.setData({
        showSearch: true,
        inputShow: false,
      });
    },
    focus(){
      console.log(123)
      this.triggerEvent("hideAll", 1);
    },
    blur(){
      this.triggerEvent("showAll", 1);
    },
    selectResult: function (e) {
      this.setData({
        showSearch: false,
        inputShow: true,
        project: e.detail.item.text,
      });
      this.triggerEvent("getSearchValue", {
        'key':e.detail.item.text,
        'value':e.detail.item.value,
        'allContent':this.data.items
      });
    },
  }
});
