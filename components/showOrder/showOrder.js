// components/showOrder.js
import {color, station} from "../../config";

Component({
    /**
     * 组件的属性列表
     */
    properties: {
        info: {
            type: Array,
            default: [],
            unCheckNames: [],
            activeName: "1",
        },
        requeredCheck: {
            type: Boolean,
            default: false,
        },
        NotPassTag: {
            type: Boolean,
            default: false,
        },
        isPass: {
            type: Boolean,
            default: false,
        },
        showButton1: {
            type: Boolean,
            default: false,
        },
        buttonName1: {
            type: String,
            default: null,
        },
        buttonName3: {
            type: String,
            default: null,
        },
        showButton2: {
            type: <PERSON>olean,
            default: false,
        },
        showButton3: {
            type: Boolean,
            default: false,
        },
        buttonName2: {
            type: String,
            default: null,
        },
    },

    /**
     * 组件的初始数据
     */
    data: {
        controll: false,
        color: color,
        station: station
    },

    /**
     * 组件的方法列表
     */
    methods: {
        toDetail(e) {
            wx.navigateTo({
                url: `/pages/orderDetail/orderDetail?id=${e.currentTarget.dataset.id}`
            })
        },
        copyOrder(e) {
            //跳转页面
            this.triggerEvent("copyOrder", e.currentTarget.dataset.id);
        },
        refuse(e) {
            //跳转页面
            this.triggerEvent("refuse", e.currentTarget.dataset.id);
        },
        accept(e) {
            //跳转页面
            this.triggerEvent("accept", e.currentTarget.dataset.id);
        },
        onChange(event) {
            this.setData({
                activeName: event.detail,
            });
        },
    },
});
