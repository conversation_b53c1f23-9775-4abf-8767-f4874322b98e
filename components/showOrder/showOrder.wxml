<view wx:for="{{info}}" wx:key="index" class="show-info">
    <view class="center" bindtap="toDetail" data-id="{{item.id}}">
        <view class="project">{{item.ContractProject}}</view>
        <view style="color: #8a8a8a">
            计划方量
            <text space="ensp" style="color: black">{{item.Stere}}</text>
        </view>
        <view style="color: #8a8a8a">
            计划开盘时间
            <text space="ensp" style="color: black">{{item.PlannedTime}}</text>
        </view>
        <view style="color: #8a8a8a">
            施工部位
            <text space="ensp" style="color: black">{{item.ConstructionSite}}</text>
        </view>
        <view style="color: #8a8a8a">
            下单人员
            <text space="ensp" style="color: black">{{item.tel}}</text>
        </view>
        <view style="color: #8a8a8a" wx:if="{{item.RefuseNote}}">
            审核理由
            <text space="ensp" style="color: black">{{item.RefuseNote}}</text>
        </view>
        <view class="spec">
            <view>{{item.ConcreteStrength}}</view>
            <view>{{ item.tld1 }}{{item.tldWay}}{{item.tld2}}</view>
            <view>{{ item.CastingMode }}</view>
        </view>
    </view>
    <view class="btn">
        <view></view>
        <view class="btn2" wx:if="{{showButton2}}" data-id="{{item.id}}" bindtap="refuse">{{buttonName2}}</view>
        <view class="btn1" wx:if="{{showButton3&&requeredCheck}}" data-id="{{item.id}}" bindtap="accept">
            {{buttonName3}}
        </view>
        <view class="btn1" wx:if="{{showButton1&&requeredCheck&&item.showCopy===1}}" data-id="{{item.id}}"
              bindtap="copyOrder">
            {{buttonName1}}
        </view>

    </view>

</view>
