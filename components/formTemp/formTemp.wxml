
<view wx:if="{{editStatus}}" class="page" data-weui-theme="{{theme}}">
    <mp-toptips msg="{{error}}" type="error" show="{{error}}"></mp-toptips>
    <mp-form-page title="新建{{title}}" subtitle="">
        <mp-form id="{{formTag}}" rules="{{rules}}" models="{{formData}}">
            <mp-cells title="{{subTitle}}" footer="*代表必填">
                <block wx:for="{{formComponentList}}" wx:key="index">
                    <label wx:if="{{item.componentName==='input'}}">
                        <mp-cell prop="{{item.prop}}" ext-class="{{item.is_required===1?'m-label':''}}">
                            <view slot="title">
                                <text wx:if="{{item.is_required===1}}" style="color: red; margin-right: 10rpx;">*</text>
                                <text>{{item.formLabel}}</text>
                            </view>
                            <input  bindinput="formInputChange" data-field="{{item.prop}}"
                                   type="{{item.field_type}}"
                                   value="{{formData[item.prop]}}" class="weui-input" placeholder="{{item.placeHold}}"/>
                        </mp-cell>



                    </label>
                    <label wx:if="{{item.componentName==='date'}}">
                        <mp-cell prop="date" title="{{item.formLabel}}" ext-class=" {{item.is_required===1?'m-label':''}}">
                            <picker data-field="{{item.prop}}" mode="date" value="{{formData[item.prop]}}" start="{{start}}" end="" bindchange="formInputChange">

                                <view class="weui-input" wx:if="{{formData[item.prop]}}">{{formData[item.prop]}}</view>
                                <view class="weui-input" wx:else>请选择</view>
                            </picker>
                        </mp-cell>
                    </label>
                    <label wx:if="{{item.componentName==='carNo'}}">
                        <mp-cell class="weui-cell_select weui-cell_select-after select-ui "  link="{{true}}" data-field="{{item}}" bindtap="bindSelectCar">
                            <view slot="title">
                                <text wx:if="{{item.is_required===1}}" style="color: red;margin-right: 10rpx;">*</text>
                                <text>{{item.formLabel}}</text>
                            </view>
                         <block >
                             <view class="weui-select" wx:if="{{formData[item.prop]}}">{{formData[item.prop]}}</view>
                             <view class="weui-select" wx:else>请输入</view>
                         </block>


                        </mp-cell>
                    </label>
                    <label wx:if="{{item.componentName==='select'}}">
                        <mp-cell class="weui-cell_select weui-cell_select-after select-ui "  link="{{true}}">
                            <view slot="title">
                                <text wx:if="{{item.is_required===1}}" style="color: red;margin-right: 10rpx;">*</text>
                                <text>{{item.formLabel}}</text>
                            </view>
                            <picker bindchange="bindSelectChange" value="{{countryIndex}}" data-field="{{item}}"
                                    range="{{item.options}}">
                                <view class="weui-select" wx:if="{{formData[item.prop]}}">{{formData[item.prop]}}</view>
                                <view class="weui-select" wx:else>请选择</view>
                            </picker>
                        </mp-cell>
                    </label>
                    <label wx:if="{{item.componentName==='selectPage'}}" data-item="{{item}}" bindtap="navToSelectPage">
                        <mp-cell ext-class="weui-cell_select weui-cell_select-after ">
                            <view slot="title" class="weui-label {{item.is_required===1?'m-select-label':'m-select-label1'}} " >{{item.formLabel}}</view>
                            <view class="weui-select" wx:if="{{formData[item.prop]}}">{{formData[item.prop]}}</view>
                            <view class="weui-select" wx:else>请选择</view>
                        </mp-cell>
                    </label>
                    <label wx:if="{{item.componentName==='selectRegion'}}">
                        <mp-cell ext-class="weui-cell_select weui-cell_select-after ">
                            <view slot="title" class="weui-label  {{item.is_required===1?'m-select-label':'m-select-label1'}} " >{{item.formLabel}}</view>
                            <picker mode="multiSelector" bindchange="changeCitysChange"
                                    data-field="{{item.prop}}"
                                    bindcolumnchange="changeCitysChangeColumn" value="{{ citysIndex }}"
                                    range="{{ cityArray }}" range-key="name">
                                <view class="weui-select" wx:if="{{formData[item.prop]}}">{{formData[item.prop]}}</view>
                                <view class="weui-select" wx:else>请选择</view>
                            </picker>
                        </mp-cell>
                    </label>
                </block>
            </mp-cells>
        </mp-form>
        <view slot="button">
            <button class="weui-btn" type="primary" bindtap="submitForm" style="width:75%;">确定</button>
        </view>
    </mp-form-page>
</view>
<view wx:else>
    <mp-form models="{{formData}}">
        <mp-cells title="" footer="">
            <block wx:for="{{formComponentList}}" wx:key="index">
                <mp-cell value="{{item.formLabel}}" footer="{{formData[item.prop]==='请选择'?'':formData[item.prop]}}"></mp-cell>
            </block>
        </mp-cells>
    </mp-form>
</view>
