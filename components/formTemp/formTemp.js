// components/form/formTemp.js
// import cityUtil from "../../utils/city";
import {formatDate} from "../../utils/timeFormat";

Component({
    /**
     * 组件的属性列表
     */
    properties: {
        formTag: {
            type: String,
            required: false,
            value: 'form'
        },
        cityArray: {
            type: Array,
            required: false,
            value: []
        },
        subTitle: {
            type: String,
            required: false,
            value: '基本信息'
        },
        editStatus: {
            type: Boolean,
            required: true,
            value: true
        },
        title: {
            type: String,
            required: false,
            value: 'title'
        },
        formData: {
            type: Object,
            required: true,
            value: {}
        },
        formComponentList: {
            type: Array,
            required: true,
            value: []
        },
        rules: {
            type: Array,
            required: false,
            value: []
        },

    },

    /**
     * 组件的初始数据
     */
    data: {
        region:[],
        region_code:[],
        start:formatDate(new Date()),
        citysIndex:0,
        province_index:0,

    },

    /**
     * 组件的方法列表
     */
    methods: {
        navToSelectPage(e){
            let item=e.currentTarget.dataset.item
            let selectPage=item.selectPage
            let prop=item.prop
            let formData=JSON.stringify(this.data.formData)
            console.log(selectPage)
            const pages = getCurrentPages();

            const currentPage = pages[pages.length - 1];
            wx.setStorageSync('backPage',currentPage.route)
            wx.setStorageSync('backProp',prop)
            wx.setStorageSync('back_type',1)
            console.log(selectPage+'?prop='+prop+'&formData='+formData)
            wx.setStorageSync('formData',formData)
            console.log(333333)
            wx.navigateTo({
                url: selectPage+'?prop='+prop+'&formData='+formData,
            })
        },

        submitForm(){
            console.log(this.selectComponent('#form'))
            // debugger
            this.selectComponent('#form').validate((valid, errors) => {
                console.log('valid', valid, errors)
                if (!valid) {
                    const firstError = Object.keys(errors)
                    if (firstError.length) {
                        this.setData({
                            error: errors[firstError[0]].message
                        })

                    }
                } else {
                    this.setData({
                        error: ''
                    })
                    console.log(88888)
                    console.log(this.data.formData)
                    this.triggerEvent('receiveFormData', this.data.formData)
                }
            })
        },
        // changeCitysChangeColumn(e){
        //     console.log(e);
        //     let column = e.detail.column;
        //     let index = e.detail.value;
        //     if(column === 0 ){
        //         this.setData({
        //             province_index:index,
        //             cityArray:cityUtil.changeCloumt(this.data.cityArray,index,column)
        //         })
        //     }
        //     if(column === 1){
        //         this.setData({
        //             cityArray:cityUtil.changeCloumt(this.data.cityArray,index,column,this.data.province_index)
        //         })
        //     }
        // },
        // changeCitysChange(e){
        //     console.log(e);
        //     let array = cityUtil.getCityIndex(this.data.cityArray,e.detail.value);
        //     const {field} = e.currentTarget.dataset
        //     console.log(field)
        //     this.setData({
        //         region:[array[0].name,array[1].name,array[2].name],
        //         region_code:[array[0].id,array[1].id,array[2].id],
        //         citysIndex:e.detail.value,
        //         [`formData.${field}`]: [array[0].name,array[1].name,array[2].name].join('-')
        //     })
        // },
        formInputChange(e) {
            const {field} = e.currentTarget.dataset
            this.setData({
                [`formData.${field}`]: e.detail.value
            })
        },

        bindSelectChange: function (e) {
            const {field} = e.currentTarget.dataset
            let prop = field.prop
            let options = field.options
            let optionsInfo= field.optionsInfo
            console.log(field)
            this.setData({
                [`formData.${prop}`]: options[e.detail.value],
                [`formData.${prop}_id`]: optionsInfo[e.detail.value].name_id
            })
            console.log(prop)
            console.log(this.data.formData[prop])
            if(field.hasChildren){
                let childrenName = field.childrenName
                let rawOptions = field.rawOptions
                let parentId= rawOptions[e.detail.value].id
                //复制一份formComponentList
                let formComponentList = [...this.data.formComponentList]
                //更新子组件的options
                formComponentList.forEach(item=>{
                    if(item.prop===childrenName){
                        item.options = item.rawOptions.filter((it)=>{
                            return it.sex_id===parentId
                        }).map((item1)=>{
                            return item1.name
                        })

                    }
                })
                this.setData({
                    formComponentList
                })

        }
        },
        bindSelectCar: function (e) {
            const {field} = e.currentTarget.dataset
            let prop = field.prop
            wx.chooseLicensePlate({
                    success: (res) => {
                        this.setData({
                            [`formData.${prop}`]: res.plateNumber,
                        })
                    },
                    fail: (err) => {
                        console.log(err)
                    }
                }
            )
        },
    }
})
