// components/uploader/uploader.js
// import CryptoJS from '/yc/crypto-js/index';
Component({
    /**
     * 组件的属性列表
     */
    properties: {
        tel: {
            type: String,
            default: "199",
        },
        title: {
            type: String,
            default: "199",
        },
        proc: {
            type: String,
            default: "",
        },
        acceptType: {
            type: String,
            default: "image",
        },
        num: {
            type: Number,
            default: 1,
        },
        isClear: {
            type: Boolean,
            default: "",
        },
        gs: {
            type: String,
            default: "image/*",
        },
        type: {
            type: String,
            default: "",
        },
        files: {
            type: Array,
            default: [],
            observer: function (newVal, oldVal) {
                this.setData({
                    selectFile: this.selectFile.bind(this),
                    uplaodFile: this.uplaodFile.bind(this)
                })
            }
        },
    },
    observers: {
        num: function (res) {
            // 在 numberA 或者 numberB 被设置时，执行这个函数
            this.setData({
                number: res,
            });
        },
        isClear: function (res) {
            // 在 numberA 或者 numberB 被设置时，执行这个函数
            this.setData({
                files: [],
            });
        },
    },

    /**
     * 组件的初始数据
     */
    data: {
        files: []
    },

    /**
     * 组件的方法列表
     */

    methods: {
        chooseImage: function (e) {
            var that = this;
            wx.chooseImage({
                sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有
                success: function (res) {
                    // 返回选定照片的本地文件路径列表，tempFilePath可以作为img标签的src属性显示图片
                    that.setData({
                        files: that.data.files.concat(res.tempFilePaths)
                    });
                }
            })
        },
        previewImage: function (e) {
            wx.previewImage({
                current: e.currentTarget.id, // 当前显示图片的http链接
                urls: this.data.files // 需要预览的图片http链接列表
            })
        },
        selectFile(files) {
            //  console.log('files', files)
            // 返回false可以阻止某次文件上传
        },
        compressImage(filePath, quality = 80) {
            return new Promise((resolve, reject) => {
                wx.compressImage({
                    src: filePath,
                    quality: quality,
                    success(res) {
                        resolve(res.tempFilePath);
                    },
                    fail(error) {
                        reject(error);
                    }
                });
            });
        },
        // generateToken(timestamp, secret) {
        //     let msg=timestamp+secret
        //     return CryptoJS.MD5(msg).toString();
        // },
        async uplaodFile(file) {
            const timestamp = Date.now().toString();
            const secret = 'cedfegdfrrff@123U'; // 这个应该是服务器提供的密钥
            // const token = this.generateToken(timestamp, secret);
            const token=1
            console.log('upload files', file)
            // 获取当前日期
            const currentDate = new Date();
            const year = currentDate.getFullYear();
            const month = String(currentDate.getMonth() + 1).padStart(2, '0');
            const day = String(currentDate.getDate()).padStart(2, '0');

            // 构建文件夹名称
            const folderName = `${year}-${month}-${day}`;
            const firstFileFold = 'wxapp'
            const desiredFileName = file.tempFilePaths[0].split('/').pop();
            // 在文件名前添加文件夹前缀
            let fileName = `${firstFileFold}/${folderName}/${desiredFileName}`;
            let isLt1M = file.tempFiles[0].size / 1024 / 1024 <= 5;
            if (!isLt1M) {
                wx.showToast({
                    title: `大小要在5M以内！`,
                });
                return;
            }
            // 文件上传的函数，返回一个promise
            wx.showLoading({
                title: "文件上传中",
            });
           // let token = await getUploadToken();
            let that = this
            let filePath = await this.compressImage(file.tempFilePaths[0])
            wx.uploadFile({
                url: `https://wx.ytonginfo.com/upload_new?token=${token}&timestamp=${timestamp}`,// 七牛云上传地址
                filePath,
                name: 'file',
                formData: {
                    foldName:'kingbugs',
                },
                success(res) {
                     const data = JSON.parse(res.data);
                    const url = data.url;

                    wx.showToast({
                        title: '上传成功',
                        icon: 'success',
                        duration: 2000
                    });
                    let comp = {url: url};
                    let files = that.data.files;
                    files.push(comp);
                    that.setData({
                        files,
                    });
                    that.triggerEvent("change-file", files);
                    // 你可以在这里处理上传成功后的逻辑
                },
                fail(error) {
                    console.error('上传失败', error);
                    // 你可以在这里处理上传失败后的逻辑
                }
            });
            // 当设置 mutiple 为 true 时, file 为数组格式，否则为对象格式

            // wx.uploadFile({
            //     url: baseURL + "wxminiuploadFile", // 仅为示例，非真实的接口地址
            //     filePath: file.tempFilePaths[0],
            //     name: "file",
            //     header: {
            //         "Content-Type": "multipart/form-data",
            //         tel: this.data.tel,
            //         type: this.data.type,
            //         proc: this.data.proc,
            //         url: wx.getStorageSync("apiUrl"),
            //     },
            //
            //     success: (res) => {
            //         wx.hideLoading()
            //         res = JSON.parse(res.data);
            //         console.log(555);
            //         console.log(res);
            //         let id = res.data[0].id;
            //
            //         let fileurl = res.data[0].url;
            //         fileurl = baseURL + fileurl;
            //         let comp = {url: fileurl, name: id, isVideo: true};
            //         let files = this.data.files;
            //         files.push(comp);
            //         this.setData({
            //             files,
            //         });
            //         this.triggerEvent("change-file", files);
            //     },
            //     fail: function (err) {
            //         wx.hideLoading()
            //         console.log(err);
            //     },
            // });
        },
        deleteFile(event) {
            const {index} = event.detail;
            console.log("删除文件的索引:", index);

            const {files} = this.data;
            files.splice(index, 1);
            this.setData({
                files,
            });
            this.triggerEvent("change-file", files);
        },
        uploadError(e) {
            console.log('upload error', e.detail)
        },
        uploadSuccess(e) {
            console.log('upload success', e.detail)
        }
    }
})
