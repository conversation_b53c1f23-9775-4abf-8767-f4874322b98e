// components/showInfo.js
import {color} from "../../config";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    info: {
      type: Array,
      default: [],
    },
    activeNames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    color: color,
    show: false,
    id: '',
    showBorder: false,
    driveId: '0',
    showActionsheet: false,
    getDriversProc: 'get_drivers',
    groups: [{text: '示例菜单', value: 1},
      {text: '示例菜单', value: 2},
      {text: '负向菜单', type: 'warn', value: 3}]
  },
  pageLifetimes: {
    show: function () {
    },
    hide: function () {
      // 页面被隐藏
      //this.videoControl.pause()
    },
    resize: function (size) {
      // 页面尺寸变化
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    onChange(event) {
      this.setData({
        activeNames: event.detail,
      });
    },
    btnClick(e) {
      console.log(111111)

      let res = this.data.groups.find((item) => {
        return item.value === e.detail.value
      })
      console.log(res.text)
      wx.showModal({
        title: '提示',
        content: '确认选择该司机吗？',
        success: () => {
          this.triggerEvent("navPage", {id: this.data.id, driveId: e.detail.value});
          this.close()
        }
      })

    },
    async selectContent(e) {
      this.triggerEvent("navPage",  e.currentTarget.dataset.id);


    },
  },
});
