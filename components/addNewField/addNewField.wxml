<t-popup visible="{{visible}}" bind:visible-change="onVisibleChange" placement="bottom">
    <view class="block">
        <view class="container">
            <view class="title">{{title}}</view>
            <block wx:for="{{fieldInfo}}">
                <view    wx:if="{{item.type==='select'}}">
                <mp-cell

                        prop="date" title="{{item.name}}"
                        ext-class="weui-cell_select weui-cell_select-after"
                >
                    <picker  data-modal="{{index}}" mode="date" value="{{item.value}}" start="" end="{{endDate}}" bindchange="selectDate">
                        <view class="weui-input" >{{item.value}}</view>
                    </picker>
                </mp-cell>
                    <view style="height:40rpx"></view>
                </view>

                <van-field
                        wx:else
                        bindinput="inputInfo"
                        data-modal="{{index}}"
                        value="{{item.value}}"
                        type="{{item.type}}"
                        label="{{item.name}}"
                        placeholder="{{item.placeholder}}"
                        input-align="right"
                />
            </block>
            <view class="btn">
                <button type="primary" class="but" bindtap="confirm">确认</button>
            </view>
        </view>
    </view>
</t-popup>
