// nm/pages/test/test.js
import {getAnyFormatDate} from "../../utils/formatTime";

Component({
    properties: {
        visible: {
            type: Boolean,
            value: false
        },
        title:{
            type: String,
            value: ''
        },
        fieldInfo:{
            type: Array,
            value: []
        }
    },
    data: {
        optValue: '',
        optIndex: 0,
        endDate:getAnyFormatDate(new Date()),
        // fieldInfo:[
        //     {
        //         name:'姓名',
        //         value:'yuan',
        //         fieldKey:'name',
        //         placeholder: '请输入姓名',
        //     },
        //     {
        //         name:'性别',
        //         value:'',
        //         fieldKey:'sex',
        //         placeholder: '请输入性别',
        //     }
        // ]
    },

    /**
     * 生命周期函数--监听页面加载
     */
    methods: {
        selectDate(e){
            let index = e.currentTarget.dataset.modal;

            // 取出实时的变量值
            let value = e.detail.value;
            let info=this.data.fieldInfo
            info[index].value=value
            // 刷新数据
            this.setData({
                fieldInfo: info,
            });
        },
        confirm() {
            //验证fieldInfo中所有的value是不是空
            let info=this.data.fieldInfo
            let flag=true
            for(let i=0;i<info.length;i++){
                if(info[i].value===''){
                    flag=false
                    break
                }
            }
            if(!flag){
                wx.showToast({
                    title: '请填写完整信息',
                    icon: 'none',
                    duration: 2000
                })
                return
            }
            this.setData({
                visible: false,
            });
           this.triggerEvent('fill', this.data.fieldInfo);

        },
        inputInfo(e) {
            // 表单双向数据绑定
            let index = e.currentTarget.dataset.modal;

            // 取出实时的变量值
            let value = e.detail;
            let info=this.data.fieldInfo
            info[index].value=value
            // 刷新数据
            this.setData({
                fieldInfo: info,
            });
        },
        cancel() {
            this.setData({
                visible: false,
            });
        },
        focusHandle() {
            this.setData({
                actionText: '取消',
            });
        },
        blurHandle() {
            this.setData({
                actionText: '',
            });
        },
        showPopup() {
            this.setData({
                visible: true
            })
        },
        onVisibleChange(e) {
            this.setData({
                visible: e.detail.visible,
            });
        },
    },


})