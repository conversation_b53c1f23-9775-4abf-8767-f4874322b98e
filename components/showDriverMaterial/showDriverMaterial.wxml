<wxs src="../../utils/formatUrl.wxs" module="utils"/>
<wxs src="../../utils/util.wxs" module="formatdate"/>
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl"/>
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl"/>
<view wx:for="{{pass}}" wx:key="id">
    <van-collapse value="{{unCheckNames}}" class="e-block">
        <view>
            <van-collapse-item name="{{index}}" is-link="{{controll}}">
                <view slot="title">
                    <view class="ele">
                        <view>材料品种: {{ item.material }}</view>
                        <view>计划入厂时间: {{ item.storageTime}}</view>
                        <view>产地厂家: {{ item.address }}</view>
                        <view>车牌号: {{ item.CarNo }}</view>
                        <view>供应商: {{ item.provider }}</view>
                        <view>司机: {{ item.driver_name }}</view>
                        <view>数量: {{ item.number ? item.number : '' }}吨</view>
                        <view>单据: {{ item.send_number }}</view>
                        <view wx:if="{{item.zs}}">轴数: {{ item.zs }}</view>
                        <view>仓位: {{ item.storage }}</view>
                        <view wx:if="{{item.storageId}}">密码: {{ item.password }}</view>
                        <view wx:if="{{item.status===2}}">拒绝理由: {{ item.reject_note ? item.reject_note : '' }}</view>
                        <van-grid column-num="3" border="{{ false }}">
                            <van-grid-item
                                    use-slot
                                    wx:if="{{item.isOldFileWay}}"
                                    wx:for="{{item['urls']}}"
                                    wx:for-item="url"
                                    wx:key="id"
                            >
                                <van-image
                                        use-error-slot
                                        use-loading-slot
                                        bindtap="getImage"
                                        data-img="{{utils.formatUrl(url)}}"
                                        lazy-load
                                        width="100"
                                        height="100"
                                        src="{{utils.formatUrl(url)}}"
                                >
                                    <text slot="error">加载失败</text>
                                    <van-loading
                                            slot="loading"
                                            type="spinner"
                                            size="20"
                                            vertical
                                    />
                                </van-image>
                            </van-grid-item>
                            <block wx:if="{{item['canShowNewUrls']}}">
                              <van-grid-item
                                    use-slot
                                    wx:if="{{!item.isOldFileWay}}"
                                    wx:for="{{item['newUrls']}}"
                                    wx:for-item="url"
                                    wx:key="id"
                            >
                                <van-image
                                        use-error-slot
                                        use-loading-slot
                                        bindtap="getImage"
                                        data-img="{{url}}"
                                        lazy-load
                                        width="100"
                                        height="100"
                                        src="{{url}}"
                                >
                                    <text slot="error">加载失败</text>
                                    <van-loading
                                            slot="loading"
                                            type="spinner"
                                            size="20"
                                            vertical
                                    />
                                </van-image>
                            </van-grid-item>
                            </block>
                           

                        </van-grid>
                        <view wx:if="{{item.issue_end_time}}">审核时间: {{ item.issue_end_time }}</view>
                        <view wx:if="{{item.status===3||item.status===4}}">入场时间: {{ item.inFactoryTime}}</view>
                        <view wx:if="{{item.status===4}}">订单完成时间: {{ item.finishTime}}</view>
                        <view class="submit-button">
                            <view/>
                            <view/>
                            <van-button
                                    wx:if="{{item.status===1||item.status===0}}"
                                    size="small"
                                    type="danger"
                                    data-orderid="{{item.id}}"
                                    bindtap="cancel"
                                    class="pass-button"
                            >取消订单
                            </van-button>
                            <van-button
                                    wx:if="{{item.status===4}}"
                                    size="small"
                                    data-id="{{item.weightId}}"
                                    bindtap="checkHzd"
                                    type="danger"
                                    class="pass-button"
                            >查看回执单
                            </van-button>
                            <van-button
                                    wx:if="{{item.status===3&&item.isCheck===0}}"
                                    size="small"
                                    type="danger"
                                    class="pass-button"
                            >等待质检
                            </van-button>
                            <van-button
                                    wx:if="{{item.status===3&&item.storageId}}"
                                    size="small"
                                    data-id="{{item}}"
                                    type="danger"
                                    bindtap="openDoor"
                                    class="pass-button"
                            >仓门操作
                            </van-button>
                            <van-button
                                    wx:if="{{item.status===2}}"
                                    size="small"
                                    type="danger"
                                    class="pass-button"
                            >订单被拒绝
                            </van-button>
                            <van-button
                                    wx:if="{{item.status===9}}"
                                    size="small"
                                    type="danger"
                                    class="pass-button"
                            >订单被取消
                            </van-button>
                        </view>
                    </view>
                </view>
                <!--      -->
            </van-collapse-item>
        </view>
    </van-collapse>
</view>
