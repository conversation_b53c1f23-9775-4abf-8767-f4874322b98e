// components/showInfo.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    pass: {
      type: Array,
      default: [],
    },
    unCheckNames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    videoIndex: null,
  },
  pageLifetimes: {
    show: function () {
    },
    hide: function () {
      // 页面被隐藏
      // this.videoControl.pause()
    },
    resize: function () {
      // 页面尺寸变化
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    play() {
      this.videoControl = wx.createVideoContext("videos");
      this.triggerEvent("video-control", this.videoControl);
    },
    openDoor(e) {
      let item = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `/nm/pages/checkStorage/checkStorage?carNo=${item.CarNo}&storageId=${item.storageId}`,
      });
    },
    checkHzd(e) {
      let weightId = e.currentTarget.dataset.id;
      wx.navigateTo({
        url: `/pages/hzd/hzd?weightId=${weightId}&factoryname=${wx.getStorageSync(
            "clientName"
        )}`,
      });
    },
    cancel(e) {
      let orderid = e.currentTarget.dataset.orderid;
      console.log(orderid);
      this.triggerEvent("cancel-preorder", orderid);
    },
    getImage(e) {
      wx.previewImage({
        urls: [e.currentTarget.dataset.img], //需要预览的图片http链接列表，注意是数组
        current: "", // 当前显示图片的http链接，默认是第一个
        success: function (res) {
        },
        fail: function (res) {
        },
        complete: function (res) {
        },
      });
    },
  },
});
