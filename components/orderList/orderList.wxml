<view class="container" wx:for="{{info}}" wx:key="index">
    <view class="top">
        <view wx:if="{{item.U_Code}}" class="top1">订单编号</view>
        <view wx:if="{{item.U_Code}}" class="top2">{{item.U_Code}}</view>
        <view wx:if="{{!item.U_Code}}" class="top1">合同编号</view>
        <view wx:if="{{!item.U_Code}}" class="top2">{{item.ContractProject}}</view>
        <view class="top3">{{item.status}}</view>
    </view>
    <van-divider></van-divider>
    <view class="center-title">{{item.ContractProject}}</view>
    <view class="center-content">
        <text class="weui-form-preview__label">计划方量</text> {{item.Stere}}
    </view>
    <view class="center-content">
        <text class="weui-form-preview__label">计划开盘时间</text> {{item.PlannedTime}}
    </view>
    <view class="center-content">
        <text class="weui-form-preview__label">施工部位</text> {{item.ConstructionSite}}
    </view>
    <view class="center-content">
        <text class="weui-form-preview__label">下单人员</text> {{item.tel}}
    </view>
    <view class="center-content" wx:if="{{item.refuseNote}}">
        <text class="weui-form-preview__label">拒绝理由</text> {{item.refuseNote}}
    </view>
    <van-divider></van-divider>
    <view class="center-content1">
        <view>{{item.ConcreteStrength}}</view>
        <view wx:if="{{item.tld1}}">{{item.tld1}}{{item.tldWay}}{{item.tld2}}</view>
        <view wx:else>{{item.tld}}</view>
        <view>{{item.CastingMode}}</view>

    </view>
    <van-divider></van-divider>
    <view class="bottom-content">

        <van-button custom-style="padding:10rpx 40rpx;" class="bottom-button2" data-item="{{item}}" size="small"
                    bindtap="checkDetail" round="true">订单详情
        </van-button>
        <!--        <view class="bottom-button1">合同详情</view>-->
        <!--        <view class="bottom-button2">合同下单</view>-->
    </view>
</view>

