<wxs src="../../utils/formatUrl.wxs" module="utils"/>
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl"/>
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl"/>
<view wx:for="{{pass1}}" wx:key="id" class="page-class" wx:for-index="idx">
    <van-collapse value="{{activeNames}}" class="e-block" bind:change="onChange">
        <view class="ele">
            <van-collapse-item name="{{idx}}" is-link="{{controll}}">
                <view slot="title">
                    <view
                            wx:for="{{item}}"
                            wx:for-index="key"
                            wx:for-item="value"
                            wx:key="key"
                    >
                        <van-cell
                                wx:if="{{key!='id'&&key!='Sys_Material_ID'&&key!='rn'&&key!='total'}}"
                                title="{{key}}"
                                value="{{value}}"
                                border="{{ false }}"
                        />
                        <van-row wx:if="{{key==='urls'}}" wx:for="{{value}}" wx:key="item">
                            <van-col span="8">
                                <van-image
                                        use-error-slot
                                        use-loading-slot
                                        bindtap="getImage"
                                        data-img="{{utils.formatUrl(item)}}"
                                        lazy-load
                                        width="100"
                                        height="100"
                                        src="{{utils.formatUrl(item)}}"
                                >
                                    <text slot="error">加载失败</text>
                                    <van-loading slot="loading" type="spinner" size="20" vertical/>
                                </van-image>
                            </van-col>
                        </van-row>
                        <!--            <video-->
                        <!--              data-id="video{{item.id}}"-->
                        <!--              bindplay="play"-->
                        <!--              wx:if="{{dealVideoUrl.videoUrl(item.url)}}"-->
                        <!--              id="videos"-->
                        <!--              src="{{utils.formatUrl(item.url)}}"-->
                        <!--              binderror="videoErrorCallback"-->
                        <!--              show-center-play-btn="{{false}}"-->
                        <!--              show-mute-btn="{{true}}"-->
                        <!--              show-play-btn="{{true}}"-->
                        <!--              auto-pause-if-navigate-->
                        <!--              auto-pause-if-open-native-->
                        <!--              enable-auto-rotation="{{true}}"-->
                        <!--              controls-->
                        <!--              picture-in-picture-mode="{{['push', 'pop']}}"-->
                        <!--              bindenterpictureinpicture="bindVideoEnterPictureInPicture"-->
                        <!--              bindleavepictureinpicture="bindVideoLeavePictureInPicture"-->
                        <!--            />-->
                    </view>
                    <van-button
                            wx:if="{{butt}}"
                            custom-class="button"
                            size="normal"
                            class="ewm"
                            color="{{ color }}"
                            data-id="{{item.id}}"
                            catchtap="showRate"
                    >查看评价
                    </van-button
                    >
                    <van-button
                            custom-class="button"
                            wx:if="{{quitQueque}}"
                            size="normal"
                            class="ewm"
                            color="{{ color }}"
                            data-id="{{item.id}}"
                            catchtap="showRate"
                    >{{buttonName}}
                    </van-button
                    >
                    <van-button
                            custom-class="button"
                            wx:if="{{showButton1}}"
                            size="normal"
                            class="ewm1"
                            color="{{ color }}"
                            data-id="{{item.id}}"
                            catchtap="showCurse1"
                    >{{buttonName1}}
                    </van-button
                    >
                    <van-button
                            custom-class="button"
                            wx:if="{{ButtonShow}}"
                            size="normal"
                            class="ewm22"
                            color="{{ color }}"
                            data-id="{{item.id}}"
                            catchtap="pass1"
                    >{{BName}}
                    </van-button
                    >
                    <van-button
                            custom-class="button"
                            wx:if="{{showButton2}}"
                            size="normal"
                            class="ewm2"
                            color="{{ color }}"
                            data-id="{{item.id}}"
                            catchtap="showCurse2"
                    >{{buttonName2}}
                    </van-button
                    >
                    <van-button
                            custom-class="button"
                            wx:if="{{showButton3}}"
                            size="normal"
                            class="ewm3"
                            color="{{ color }}"
                            data-id="{{item.id}}"
                            data-mid="{{item.Sys_Material_ID}}"
                            catchtap="showCurse3"
                    >{{buttonName3}}
                    </van-button
                    >
                </view>
                <view
                        wx:for="{{pass2[idx]}}"
                        wx:for-index="key"
                        wx:for-item="value"
                        wx:key="key"
                >
                    <van-cell
                            wx:if="{{key!='urls'&&key!='total'&&key!='rn'}}"
                            title="{{key}}"
                            value="{{value}}"
                            border="{{ false }}"
                    />

                </view>

                <!--      -->
            </van-collapse-item>
        </view>
    </van-collapse>
</view>
