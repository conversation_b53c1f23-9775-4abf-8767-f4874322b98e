// components/showInfo.js
import { color } from "../../config";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    pass1: {
      type: Array,
      default: [],
    },
    butt: {
      type: Boolean,
      default: false,
    },
    showButton1: {
      type: <PERSON>olean,
      default: false,
    },
    showButton2: {
      type: Boolean,
      default: false,
    },
    showButton3: {
      type: Boolean,
      default: false,
    },
    buttonName: {
      type: String,
      default: null,
    },
    ButtonShow: {
      type: Boolean,
      default: false,
    },
    BName: {
      type: String,
      default: null,
    },
    buttonName1: {
      type: String,
      default: null,
    },
    buttonName2: {
      type: String,
      default: null,
    },
    buttonName3: {
      type: String,
      default: null,
    },
    quitQueque: {
      type: Boolean,
      default: false,
    },
    pass2: {
      type: Array,
      default: [],
    },
    activeNames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    color: color,
  },
  pageLifetimes: {
    show: function () {},
    hide: function () {
      // 页面被隐藏
      //this.videoControl.pause()
    },
    resize: function (size) {
      // 页面尺寸变化
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    showRate(e) {
      //跳转页面
      this.triggerEvent("changeValue", e.currentTarget.dataset.id);
    },
    showCurse1(e) {
      //跳转页面
      this.triggerEvent("Curse1",e.currentTarget.dataset.id);
    },
    pass1(e) {
      //跳转页面
      this.triggerEvent("pass",e.currentTarget.dataset.id);
    },
    showCurse2(e) {
      //跳转页面
      this.triggerEvent("Curse2", e.currentTarget.dataset.id);
    },
    showCurse3(e) {
      //跳转页面
      this.triggerEvent("Curse3",{id:e.currentTarget.dataset.id,materialId:e.currentTarget.dataset.mid});
    },

    play() {
      this.videoControl = wx.createVideoContext("videos");
      this.triggerEvent("video-control", this.videoControl);
    },
    onChange(event) {
      this.setData({
        activeNames: event.detail,
      });
    },
    cancel(e) {
      this.triggerEvent("dialog-control", e.currentTarget.dataset.orderid);
    },
    getImage(e) {
      wx.previewImage({
        urls: [e.currentTarget.dataset.img], //需要预览的图片http链接列表，注意是数组
        current: "", // 当前显示图片的http链接，默认是第一个
        success: function (res) {},
        fail: function (res) {},
        complete: function (res) {},
      });
    },
  },
});
