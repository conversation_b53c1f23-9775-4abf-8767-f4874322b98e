import * as echarts from "../../ec-canvas/echarts";

const app = getApp();

function setOptionc1(chart, title, barData, subtitle) {
  console.log(barData);
  console.log("test");
  let ydata = barData.map((item) => {
    return item.name;
  });
  let xdata = barData.map((item) => {
    return item.value;
  });
  console.log(ydata);
  console.log(xdata);
  const option = {
    title: {
      text: title,
      textStyle: {
        fontSize: 20,
      },
      subtext: subtitle,
    },
    xAxis: {
      type: "category",
      data: ydata,
      axisLabel: {
        rotate: -45,
        textStyle: {
          fontSize: 5,
          color: "#999",
        },
      },
    },

    yAxis: {
      type: "value",
    },
    series: [
      {
        data: xdata,
        type: "bar",
        showBackground: true,
        backgroundStyle: {
          color: "rgba(180, 180, 180, 0.2)",
        },
      },
    ],
  };
  chart.setOption(option);
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    barData: {
      type: Array,
      observer: async function (newVal, oldVal) {
        if (newVal) {
          console.log(newVal);
          await this.GetFigureData(newVal);
        }
      },
    },
    title: {
      type: String,
      observer: async function (newVal, oldVal) {
        if (newVal) {
          console.log(newVal);
        }
      },
    },
    subTitle: {
      type: String,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    ec_bar: {
      // 将 lazyLoad 设为 true 后，需要手动初始化图表
      lazyLoad: true,
    },
    isDisposed: false,
    isLoaded: false,
  },
  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      // 页面被展示
      this.GetFigureData();
    },
    detached: function () {
      // 在组件实例被从页面节点树移除时执行
      if (this.chartc) {
        this.chartc.dispose();
      }
      this.setData({
        isDisposed: true,
      });
    },
    ready: function () {
      // 在组件实例被从页面节点树移除时执行
    },
    show: function () {},
  },

  /**
   * 组件的方法列表
   */
  methods: {
    initc_day1: function (title, barData, subTitle) {
      this.ecComponent_day1.init(async (canvas, width, height, dpr) => {
        console.log(2222);
        // 获取组件的 canvas、width、height 后的回调函数
        // 在这里初始化图表
        const chart = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr, // new
        });
        await setOptionc1(chart, title, barData, subTitle);

        // 将图表实例绑定到 this 上，可以在其他成员函数（如 dispose）中访问
        this.chartc = chart;

        this.setData({
          isLoaded: true,
          isDisposed: false,
        });

        // 注意这里一定要返回 chart 实例，否则会影响事件处理等
        return chart;
      });
    },
    async GetFigureData(barData) {
      this.ecComponent_day1 = this.selectComponent("#mychart-dom-line");
      this.initc_day1(this.data.title, barData, this.data.subTitle);
    },
  },
});
