// components/showOrder.js
import { station } from "../../config.js";
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    info: {
      type: Array,
      default: [],
      unCheckNames:[],
      activeName: '1',
    },
    NotPassTag:{
      type: Boolean,
      default: false,
    },
    isPass:{
      type: Boolean,
      default: false,
    }
  },
  lifetimes: {
    attached: function() {
      this.setData({
        station: wx.getStorageSync('clientName')
      });
      // 在组件实例进入页面节点树时执行
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    controll:false,
    station:wx.getStorageSync('clientName'),
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onChange(event) {
      this.setData({
        activeName: event.detail,
      });
    },
  }
})
