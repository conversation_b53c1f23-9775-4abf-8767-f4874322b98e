<view wx:for="{{info}}" wx:key="index" class="show-info">
  <view class="item-info">
  <van-collapse accordion value="{{ activeName }}" bind:change="onChange">
    <van-collapse-item name="{{index}}" is-link="{{controll}}" >
      <view slot="title">
        <view class="base-whole">
          <view class="title">
            <view class="items">
              <view class="ele text2"> {{ item.ContractProject }} </view>
              <view class="ele text3">生产站点：{{station}}</view>
              <view class="ele text6">
                计划开盘时间:{{ item.PlannedTime }}
              </view>
            </view>
            <view class="watch-number">
              <view class="numb1">{{ item.Stere }}方</view>
              <view class="ele right numb2"> {{ item.ConcreteStrength }} </view>
              <view class="ele right numb3">{{ item.tld1 }}±{{item.tld2}}</view>
              <view class="ele right numb4"> {{ item.CastingMode }} </view>
            </view>
          </view>
          <view class="time">
            <view>提交时间</view>
            <view>{{ item.CreationTime}}</view>
          </view>
        </view>
      </view>
      <view class="base-info">
        <van-cell title="施工信息" border="{{controll}}" class="base-title" />
        <van-cell title="工地地址" value="{{item.address}}" />
        <van-cell title="原材要求" value="{{item.MaterialRequirements}}" />
        <van-cell title="施工部位" value="{{item.ConstructionSite}}" />
        <van-cell title="运输距离" value="{{item.LoadDistance}}" />
<!--        <van-cell title="是否以图纸结算方量" value="{{item.isPaperCheck==='1'?'是':'否'}}" />-->
        <view wx:if="{{NotPassTag}}">
          <van-field
                  value="{{ item.refuseNote }}"
                  label="审核不通过理由"
                  type="textarea"
                  placeholder=""
                  autosize
          />
        </view>
      </view>
      <view class="base-info">
        <van-cell title="联系信息" border="{{controll}}" class="base-title" />
        <van-cell title="工地联系人" value="{{item.Site_Contacter}}" />
        <van-cell
          title="工地联系人电话"
          value="{{item.Site_Contact_PhoneNumber}}"
        />
        <van-cell title="备注" value="{{item.Remarks}}" />
      </view>
      <view wx:if="{{isPass}}" class="base-info">
        <van-cell title="发货情况" border="{{controll}}" class="base-title" />
        <van-cell title="发货量" value="{{item.SendOutStere}}" />
        <van-cell title="发货时间" value="{{item.ProdTime}}" />
        <van-cell title="发货车次" value="{{item.Truck_Times}}" />
      </view>
    </van-collapse-item>
  </van-collapse>
  </view>
</view>
