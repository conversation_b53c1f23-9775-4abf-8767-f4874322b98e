<view class="custom_nav" style="height:{{navbarHeight}}px; background:{{background}}">
    <view class="custom_nav_box" style="height:{{navbarHeight}}px; background:{{background}}">
        <view class="custom_nav_bar" style="top:{{statusBarHeight}}px; height:{{cusnavH}}px; background:{{background}}">
            <!-- 正常 -->
            <block wx:if="{{isNormal}}">
                <view class="nav_title"
                      style="height:{{cusnavH}}px; line-height:{{cusnavH}}px; font-weight: bold; color:#{{titleColor}}">
                    {{vTitle}}
                </view>
            </block>
            <!-- 搜索 -->
            <block wx:elif="{{isSearch}}">
                <input class="navSearch" disabled="true"
                       style="height:{{navbarBtn.height-2}}px;line-height:{{navbarBtn.height-4}}px; top:{{navbarBtn.top+1}}px; left:{{navbarBtn.right}}px; border-radius:{{navbarBtn.height/2}}px;"
                       maxlength="10" bindtap="onClickInput" placeholder="{{placeholder}}"/>
            </block>
            <!-- 地址 -->
            <block wx:elif="{{isAddress}}">
                <view style="height:{{navbarBtn.height}}px;line-height:{{navbarBtn.height-2}}px; top:{{navbarBtn.top}}px; left:{{navbarBtn.right}}px; border-radius:{{navbarBtn.height/2}}px;"
                      bindtap="locationClick">
                    <view class="icon-location">
                        <image src='/static/imgs/location.png' class='location'></image>
                    </view>
                    <view class="title" style="color:#{{titleColor}}">{{vTitle}}</view>
                    <view class="icon-arrow-right">
                        <image src='/static/imgs/arrow-right.png' class='arrow-right'></image>
                    </view>
                </view>
            </block>
            <!-- 返回 -->
            <block wx:elif="{{isBack}}">
                <view style="height:{{navbarBtn.height}}px;line-height:{{navbarBtn.height-2}}px; top:{{navbarBtn.top}}px; left:{{navbarBtn.right}}px; border-radius:{{navbarBtn.height/2}}px;">
                    <view class="icon-arrow-left" bindtap='_goBack'>
                        <image src='/static/imgs/arrow-left-{{titleColor}}.png' class='arrow-left'></image>
                    </view>
                    <view class="title" style="color:#{{titleColor}}" bindtap='_goBack'>{{vTitle}}</view>
                </view>
            </block>
            <!-- 其他 -->
            <block wx:else>
                <view class="custom_nav_icon {{!haveBack||'borderLine'}}"
                      style="height:{{navbarBtn.height}}px;line-height:{{navbarBtn.height-2}}px; top:{{navbarBtn.top}}px; left:{{navbarBtn.right}}px; border-radius:{{navbarBtn.height/2}}px;">
                    <view wx:if="{{haveBack}}" class="icon-back" bindtap='_goBack'>
                        <image src='{{url}}nav_back.jpg' mode='aspectFill' class='back-pre'></image>
                    </view>
                    <view wx:if="{{haveBack}}" class='navbar-v-line'></view>
                    <view class="icon-home">
                        <navigator class="home_a" url="/pages/home/<USER>" open-type="switchTab">
                            <image src='{{url}}nav_home.jpg' mode='aspectFill' class='back-home'></image>
                        </navigator>
                    </view>
                </view>
                <view class="nav_title" style="height:{{cusnavH}}px; line-height:{{cusnavH}}px; color:{{titleColor}}">
                    {{vTitle}}
                </view>
            </block>
        </view>
    </view>
</view>
