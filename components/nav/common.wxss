/* components/plugins/nav/test.wxss*/
.custom_nav {
  width: 100%;
  position: relative;
  z-index: 99999;
}

.custom_nav_box {
  position: fixed;
  width: 100%;
  z-index: 99999;
}

.custom_nav_bar {
  display: flex;
  align-items: center;
  position: relative;
  z-index: 9;
}

.custom_nav_box .nav_title {
  font-size: 28rpx;
  text-align: center;
  position: absolute;
  max-width: 360rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: 1;
}

.custom_nav_box .custom_nav_icon {
  position: absolute;
  z-index: 2;
  display: inline-block;
  border-radius: 50%;
  vertical-align: top;
  font-size: 0;
  box-sizing: border-box;
}

.custom_nav_box .custom_nav_icon.borderLine {
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  background: rgba(0, 0, 0, 0.1);
}

.navbar-v-line {
  width: 1px;
  margin-top: 14rpx;
  height: 32rpx;
  background-color: rgba(255, 255, 255, 0.3);
  display: inline-block;
  vertical-align: top;
}

.icon-arrow-left {
  width: 13px;
  display: inline-block;
  padding-left: 16rpx;
  vertical-align: top;
  height: 20px;
}

.icon-location {
  display: inline-block;
  padding-left: 20rpx;
  vertical-align: top;
  height: 100%;
}

.title {
  max-width: 190px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: top;
  font-size: 15px;
  height: 100%;
  padding-top: 1px;
}

.icon-arrow-right {
  display: inline-block;
  vertical-align: top;
  height: 100%;
  margin-left: 4px;
}

.icon-back {
  display: inline-block;
  width: 74rpx;
  padding-left: 20rpx;
  vertical-align: top;
  height: 100%;
}

.icon-home {
  display: inline-block;
  width: 80rpx;
  text-align: center;
  vertical-align: top;
  height: 100%;
}

.icon-home .home_a {
  height: 100%;
  display: inline-block;
  vertical-align: top;
  width: 35rpx;
}

.custom_nav_box .back-pre,
.custom_nav_box .back-home {
  width: 35rpx;
  height: 35rpx;
  vertical-align: middle;
}


.custom_nav_box .arrow-left {
  width: 18rpx;
  height: 27rpx;
  padding-right: 4px;
  vertical-align: middle;
}

.custom_nav_box .location {
  width: 28rpx;
  height: 28rpx;
  vertical-align: middle;
  padding-bottom: 2px;
}

.custom_nav_box .arrow-right {
  width: 16rpx;
  height: 16rpx;
  vertical-align: middle;
}

.navSearch {
  width: 200px;
  background: rgb(240, 240, 240);
  left: 16px !important;
  font-size: 14px;
  position: absolute;
  padding: 0 20rpx;
  z-index: 9;
}