// components/showRefuse.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    refuse: {
      type: Array,
      default: [],
    },
    unCheckNames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    getImage(e) {
      wx.previewImage({
        urls: [e.currentTarget.dataset.img], //需要预览的图片http链接列表，注意是数组
        current: '', // 当前显示图片的http链接，默认是第一个
        success: function (res) {},
        fail: function (res) {},
        complete: function (res) {},
      })
    },
    navTo(e){
      let id=e.currentTarget.dataset.id
      wx.navigateTo({
        url:`/pages/map/map?id=${id}`
      })
    }
  }
})
