<wxs src="../../utils/formatUrl.wxs" module="utils" />
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl" />
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl" />

<view wx:for="{{refuse}}" wx:key="id">
<!--    <van-collapse value="{{unCheckNames}}" class="e-block">-->
<!--        <view>-->
<!--            <van-collapse-item name="{{index}}" is-link="{{controll}}">-->
<!--                <view slot="title">-->
<!--                    <view class="ele">-->
<!--                        <view>材料品种: {{ item.material }}</view>-->
<!--                        <view>-->
<!--                            预约入厂时间: {{item.storageTime}}-->
<!--                        </view>-->
<!--                        <view>产地厂家: {{ item.address }}</view>-->
<!--                        <view>车牌号: {{ item.CarNo }}</view>-->
<!--                        <view>供应商: {{ item.provider }}</view>-->
<!--                        <view>司机: {{ item.driver_name }}</view>-->
<!--                        <view>数量: {{ item.number }}</view>-->
<!--                        <view>单据: {{ item.send_number }}</view>-->
<!--                        <view>审核人员: {{ item.manageId }}</view>-->
<!--                        <van-image-->
<!--                                use-loading-slot-->
<!--                                bindtap="getImage"-->
<!--                                data-img="{{utils.formatUrl(item.url)}}"-->
<!--                                wx:if="{{dealImageUrl.ImageUrl(item.url)}}"-->
<!--                                lazy-load-->
<!--                                width="100"-->
<!--                                height="100"-->
<!--                                src="{{utils.formatUrl(item.url)}}"-->
<!--                        />-->
<!--                        <view>-->
<!--                            审核通过时间: {{ item.issue_end_time}}-->
<!--                        </view>-->
<!--                        <view wx:if="{{item.status===3||item.status===4}}">-->
<!--                            入场时间: {{ item.inFactoryTime}}-->
<!--                        </view>-->
<!--                        <view wx:if="{{item.status===4}}">-->
<!--                            订单完成时间: {{ item.finishTime}}-->
<!--                        </view>-->
<!--                        <view wx:if="{{item.status===2}}">-->
<!--                            <view>拒绝时间: {{ item.issue_end_time }}</view>-->
<!--                            <view>拒绝理由: {{ item.reject_note }}</view>-->
<!--                        </view>-->
<!--                        <view wx:if="{{item.status===3&&item.isValid===0}}">-->
<!--                            入场车辆审核时间: {{ item.factoryCheckTime}}-->
<!--                        </view>-->
<!--                        <view wx:if="{{item.factoryStatus===1||item.factoryStatus===2}}">-->
<!--                            审核类型: {{ item.factoryStatus===1?'手动过磅':'强制删除'}}-->
<!--                        </view>-->
<!--                        <view wx:if="{{item.status===3&&item.isValid===0&&item.factoryStatus===2}}">-->
<!--                            入场车辆审核理由: {{ item.deleteCause}}-->
<!--                        </view>-->

<!--                    </view>-->
<!--                </view>-->
<!--                &lt;!&ndash;      &ndash;&gt;-->
<!--            </van-collapse-item>-->
<!--        </view>-->
<!--    </van-collapse>-->
    <view class="ele">
        <view>材料品种: {{ item.material }}</view>
        <view>
            预约入厂时间: {{item.storageTime}}
        </view>
        <view>产地厂家: {{ item.address }}</view>
        <view>车牌号: {{ item.CarNo }}</view>
        <view>供应商: {{ item.provider }}</view>
        <view>司机: {{ item.driver_name }}</view>
        <view wx:if="{{item.number}}">数量: {{ item.number }}</view>
        <view>单据: {{ item.send_number }}</view>
        <view>审核人员: {{ item.manageId }}</view>
        <view>车辆状态: {{ item.carStatus }}</view>
        <van-image
                use-loading-slot
                bindtap="getImage"
                data-img="{{utils.formatUrl(item.url)}}"
                wx:if="{{dealImageUrl.ImageUrl(item.url)}}"
                lazy-load
                width="100"
                height="100"
                src="{{utils.formatUrl(item.url)}}"
        />
        <view>
            审核通过时间: {{ item.issue_end_time}}
        </view>
        <view wx:if="{{item.status===3||item.status===4}}">
            入场时间: {{ item.inFactoryTime}}
        </view>
        <view wx:if="{{item.status===4}}">
            订单完成时间: {{ item.finishTime}}
        </view>
        <view wx:if="{{item.status===2}}">
            <view>拒绝时间: {{ item.issue_end_time }}</view>
            <view>拒绝理由: {{ item.reject_note }}</view>
        </view>
        <view wx:if="{{item.status===3&&item.isValid===0}}">
            入场车辆审核时间: {{ item.factoryCheckTime}}
        </view>
        <view wx:if="{{item.factoryStatus===1||item.factoryStatus===2}}">
            审核类型: {{ item.factoryStatus===1?'手动过磅':'强制删除'}}
        </view>
        <view wx:if="{{item.status===3&&item.isValid===0&&item.factoryStatus===2}}">
            入场车辆审核理由: {{ item.deleteCause}}
        </view>
        <van-grid column-num="3" border="{{ false }}">
            <van-grid-item
                    use-slot
                    wx:for="{{item['urls']}}"
                    wx:key="id"
            >
                <van-image
                        use-error-slot
                        use-loading-slot
                        bindtap="getImage"
                        data-img="{{item}}"
                        lazy-load
                        width="100"
                        height="100"
                        src="{{item}}"
                >
                    <text slot="error">加载失败</text>
                    <van-loading
                            slot="loading"
                            type="spinner"
                            size="20"
                            vertical
                    />
                </van-image>
            </van-grid-item>
        </van-grid>
        <view class="submit-button">
            <view />
            <view />
            <van-button
                    plain
                    wx:if="{{item.status===1||item.status===3}}"
                    size="small"
                    type="danger"
                    data-id="{{item.id}}"
                    bindtap="navTo"
                    class="pass-button"
            >车辆位置
            </van-button>
        </view>

    </view>
</view>

