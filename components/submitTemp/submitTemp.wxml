<view>
  <mp-toptips msg="{{error}}" type="error" show="{{error}}"></mp-toptips>
  <slot name="top5"></slot>
  <van-popup show="{{show}}" position="bottom">
    <van-picker
      show-toolbar
      columns="{{addBtnList[item.index].content}}"
      bind:cancel="cancel"
      data-index="{{item.index}}"
      bind:confirm="Callb"
    />
  </van-popup>
  <van-popup show="{{timelist.showtime}}" position="bottom">
    <van-datetime-picker
      model:value="{{jzTime}}"
      type="datetime"
      title="选择计划入库时间"
      min-date="{{timelist.minDate}}"
      max-date="{{timelist.maxDate}}"
      bind:cancel="cancelTime"
      bind:confirm="confirmTime"
    />
  </van-popup>

  <van-tabs
    animated
    swipeable
    active="{{active}}"
    bind:change="choose"
    color="{{color}}"
  >
    <block wx:for="{{tabnames}}" wx:key="index">
      <van-tab wx:if="{{index===0}}" title="{{item}}">
        <view wx:for="{{addBtnList}}" wx:key="index">
          <view>
            <searchSelect
              showwx="请输入工程名"
              wx:if="{{item.title==='工程'}}"
              title="工程名"
              passid="{{passid}}"
              bind:hideAll="hideAll"
              bind:showAll="showAll"
              bind:getSearchValue="getSearchValue"
              seachKeyProc="{{seachKeyProc}}"
            ></searchSelect>
            <view wx:if="{{showMess}}">
            <van-field
                    wx:if="{{item.title!='工程'}}"
              class="m-field"
              readonly
              clickable
              name="picker"
              value="{{item.selected}}"
              label="{{item.title}}"
              placeholder="{{item.placeholder}}"
              data-index="{{index}}"
              bindtap="getContent"
              is-link
              required
              input-align="right"
              border="{{controll}}"
            />
            </view>
          </view>
        </view>
        <view
          wx:for="{{others}}"
          wx:for-index="key"
          wx:for-item="value"
          wx:key="index"
          wx:if="{{isshow}}"
        >
          <van-cell title="{{key}}" value="{{value}}" />
        </view>
        <slot name="fields"></slot>
        <!--          <van-field-->
        <!--            border="{{controll}}"-->
        <!--            readonly-->
        <!--            clickable-->
        <!--            label="提交时间"-->
        <!--            value="{{timelist.value}}"-->
        <!--            placeholder="选择时间"-->
        <!--            bindtap="timeshow"-->
        <!--            input-align="right"-->
        <!--          />-->
        <view class="section" wx:if="{{showMess}}">
          <picker
            mode="time"
            value="{{timelist.value}}"
            start="{{timelist.minDate}}"
            end="{{timelist.maxDate}}"
            bindchange="timeChoose"
          >
            <van-field
              border="{{controll}}"
              readonly
              required
              label="提交时间"
              value="{{timelist.value}}"
              placeholder="选择时间"
              input-align="right"
            />
          </picker>
        </view>
        <slot name="others"></slot>
      </van-tab>
      <van-tab wx:if="{{index>0}}" title="{{item}}">
        <slot name="tab1"></slot>
      </van-tab>
    </block>
  </van-tabs>
</view>
