import { getSkInfo } from "../../service/submitTemp.js";
import { color } from "../../config";
import { Content, contentById } from "../../service/submitTemp";
import {FormatTime} from "../../utils/formatTime";
import {
  getTask
} from "../../service/locCheck.js";
Component({
  options:{
    multipleSlots: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    passid: {
      type: String,
      default: null,
    },
    seachKeyProc:{
      type: String,
      default:null,
    },
    tel: {
      type: String,
      default: [],
    },
    addBtnList: {
      type: Array,
      default: [],
    },
    isshow: {
      type: Boolean,
      default: [],
    },
    timelist: {
      type: Object,
      default: {},
    },
    tabcontent: {
      type: Array,
      default: [],
    },
    tabnames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    controll: false,
    others: [],
    allContent:[],
    project:null,
    projectId:null,
    jzTime: null,
    show: false,
    showMess:true,
    color: color,
    active: 0,
    error: '',
    item: {
      index: 0,
    },
  },

  /**
   * 组件的方法列表
   */
  onload(){
    //console.log(this.data.addBtnList)
  },
  methods: {
    cancelTime() {
      this.setData({
        "timelist.showtime": false,
      });
    },
    hideAll(){
      this.setData({
        showMess: false,
      });
      this.triggerEvent("hideMess", 1);
    },
    getSearchValue(e){
      this.showAll()
      const {key,value,allContent}=e.detail
      this.setData(
          {
            project:key,
            projectId:value,
            allContent:allContent
          }
      )
      this.triggerEvent("clkCallBk", {
        val: key,
        index: 0,
        id: value,
        allContent:allContent
      });
    },
    showAll(){
      this.setData({
        showMess: true,
      });
      this.triggerEvent("showMess", 1);
    },
    async choose(e) {
      let name=e.detail.name
      console.log(name)
      if (name === 1) {
        wx.showLoading({
          title: "加载中",
        });
        let clientId=wx.getStorageSync('clientId')
        let res = await getSkInfo(this.data.tabcontent[0].proc, this.data.tel,this.data.passid,clientId);
        res = res.data;
        wx.hideLoading()
        this.triggerEvent("clprocb", {
          res: res,
          name: name - 1,
        });
        // this.$emit("clprocb", res,name-1);
      }
      if(name === 0){
        wx.hideLoading()
      }
      // if (name > 1) {
      //   let res = await getSelect(
      //     this.data.tabcontent[name - 1].proc,
      //     this.data.tel
      //   );
      //   res = res.data;
      //   this.triggerEvent("clprocb", {
      //     res: res,
      //     name: name - 1,
      //   });
      //   //this.$emit("clprocb", res,name-1);
      // }
    },
    getDate(){
      // 获取当前日期
      let date = new Date();

// 获取当前月份
      let nowMonth = date.getMonth() + 1;

// 获取当前是几号
      let strDate = date.getDate();

// 添加分隔符“-”
      let seperator = "-";

// 对月份进行处理，1-9月在前面添加一个“0”
      if (nowMonth >= 1 && nowMonth <= 9) {
        nowMonth = "0" + nowMonth;
      }

// 对月份进行处理，1-9号在前面添加一个“0”
      if (strDate >= 0 && strDate <= 9) {
        strDate = "0" + strDate;
      }

// 最后拼接字符串，得到一个格式为(yyyy-MM-dd)的日期
      return date.getFullYear() + seperator + nowMonth + seperator + strDate;

    },
    timeChoose(e){
      let {value}=e.detail
      let date =this.getDate();
      date=date+' '+value
      this.setData({
        "timelist.value": date
      });
      this.triggerEvent("clkTime", date);

      console.log(this.data.timelist.value)
    },
    confirmTime(e) {
     let jzTime= FormatTime(e.detail)
      this.setData({
        jzTime: jzTime,
        "timelist.showtime": false,
      });
      this.triggerEvent("clbtime", jzTime);
      //this.$emit("clbtime", this.jzTime);
    },
    getnames(array1) {
      let res = [];
      for (const element of array1) {
        Object.keys(element).forEach(function (key) {
          if (key === "U_Name") {
            res.push(element[key]);
          }
        });
      }
      return res;
    },
    getid(name, all) {
      for (let i = 0; i < all.length; i++) {
        if (name === all[i].U_Name) {
          return all[i].id;
        }
      }
    },
    getotherel(val, arr) {
      let res = arr.filter((item) => {
        return item.U_Name === val;
      });
      delete res[0].U_Name;
      delete res[0].id;
      return res[0];
    },
    onShow(value) {
      this.setData({
        error: value
      })
    },
    async getContent(e) {
      let index = e.currentTarget.dataset.index;
      if (index === 0) {
        let res = await Content(
          this.data.addBtnList[index].proc,
          this.data.tel,
          this.data.passid
        );
        res = res.data;
        const allcontent = `addBtnList[${index}].allcontent`;
        const content = `addBtnList[${index}].content`;
        this.setData({
          show: true,
          [allcontent]: res,
          [content]: this.getnames(res),
          "item.index": index,
        });
        // this.addBtnList[index].allcontent = res;
        // this.addBtnList[index].content = this.getnames(res);
        // this.item.index = index;
      }else if(index === 1){
        let res = await getTask(
            this.data.addBtnList[index].proc,
            this.data.passid,
            this.data.projectId
        );
        res = res.data;
        let tasks = res.map((item) => {
          return item.U_Name;
        });
        const allcontent = `addBtnList[${index}].allcontent`;
        const content = `addBtnList[${index}].content`;
        this.setData({
          show: true,
          [allcontent]: res,
          [content]: tasks,
          "item.index": index,
        });
      } else {
        if (!this.data.addBtnList[index - 1].selected) {
          this.onShow("请先选择" + this.data.addBtnList[index - 1].title)
          //Toast.fail("请先选择" + this.data.addBtnList[index - 1].title);
          return;
        }
        console.log(5555)
        console.log(this.data.addBtnList[index - 1].selected)
        console.log(this.data.addBtnList[index - 1].allcontent)
        console.log(666)
        let res = await contentById(
          this.data.addBtnList[index].proc,
          this.data.tel,
          this.data.passid,
          this.getid(
            this.data.addBtnList[index - 1].selected,
            this.data.addBtnList[index - 1].allcontent
          )
        );
        res = res.data;
        const allcontent = `addBtnList[${index}].allcontent`;
        const content = `addBtnList[${index}].content`;
        this.setData({
          show: true,
          [allcontent]: res,
          [content]: this.getnames(res),
          "item.index": index,
        });
        // this.addBtnList[index].allcontent = res;
        // this.addBtnList[index].content = this.getnames(res);
        // this.item.index = index;
      }
    },
    cancel(e) {
      this.setData({
        show: false,
      });
    },
    Callb(e) {
      let { value} = e.detail;
      let index=e.currentTarget.dataset.index
      let val = value;
      this.setData({
        show: false,
      });
      let allcontent=this.data.addBtnList[index].allcontent
      let id = this.getid(val, this.data.addBtnList[index].allcontent);
      if (this.data.addBtnList[index].proc === "get_order_strength") {
        let h = JSON.parse(
          JSON.stringify(this.data.addBtnList[index].allcontent)
        );
        this.others = this.getotherel(val, h);
        this.setData({
          isshow: true,
        });
      }
      this.triggerEvent("clkCallBk", {
        val: val,
        index: index,
        id: id,
        allContent:allcontent
      });
    },
  },
});
