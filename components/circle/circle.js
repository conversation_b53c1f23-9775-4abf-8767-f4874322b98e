import * as echarts from "../../ec-canvas/echarts";

const app = getApp();


Component({
    /**
     * 组件的属性列表
     */
    properties: {
        circleData: {
            type: Array,
            observer: function (newVal, oldVal) {
                if (newVal) {
                    this.GetFigureData()
                }
            }
        },
        title: {
            type: String,
        },
        subTitle: {
            type: String,
        },
    },

    /**
     * 组件的初始数据
     */
    data: {
        ec_bar: {
            // 将 lazyLoad 设为 true 后，需要手动初始化图表
            lazyLoad: true,

        },
        isDisposed: false,
        isLoaded: false,
    },
    lifetimes: {
        attached: function () {
            // 在组件实例进入页面节点树时执行
            // 页面被展示
            this.GetFigureData()

        },
        detached: function () {
            // 在组件实例被从页面节点树移除时执行
            if (this.chartc) {
                this.chartc.dispose();
            }
            this.setData({
                isDisposed: true,
            });
        },
        ready: function () {
            // 在组件实例被从页面节点树移除时执行

        },
        show: function () {

        },

    },

    /**
     * 组件的方法列表
     */
    methods: {
        setOptionc1(chart, series, circleData, title, subtitle) {
            let option = {
                title: {
                    text: title,
                    subtext: subtitle,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    type: 'scroll',
                    orient: 'horizontal',
                    left: 15,				// 图例位置
                    bottom: 20,
                },
                series: [
                    {
                        name: 'Access From',
                        type: 'pie',
                        radius: '50%',
                        data: circleData,
                        top: 30,
                        label: {
                            normal: {
                                show: true,
                                formatter: '{b}:\n {c}({d}%)'
                            }
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    }
                ]
            }
            chart.setOption(option);
            chart.on('click', (param) => {
                this.triggerEvent("toDetail", {name: param.name, value: param.value});
            });
        },
        initc_day1(series, circleData, title, subtitle) {
            this.ecComponent_day1.init((canvas, width, height, dpr) => {
                // console.log(2222)
                // 获取组件的 canvas、width、height 后的回调函数
                // 在这里初始化图表
                const chart = echarts.init(canvas, null, {
                    width: width,
                    height: height,
                    devicePixelRatio: dpr, // new
                });
                this.setOptionc1(chart, series, circleData, title, subtitle);

                // 将图表实例绑定到 this 上，可以在其他成员函数（如 dispose）中访问
                this.chartc = chart;

                this.setData({
                    isLoaded: true,
                    isDisposed: false,
                });

                // 注意这里一定要返回 chart 实例，否则会影响事件处理等
                return chart;
            });
        },
        async GetFigureData() {

            this.ecComponent_day1 = this.selectComponent("#mychart-dom-line");
            this.initc_day1(this.data.seriesc_day, this.data.circleData, this.data.title, this.data.subTitle);
        },
    }
})
