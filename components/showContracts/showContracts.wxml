<view wx:for="{{info}}" wx:key="index" class="show-info">
    <view class="center" bindtap="toDetail" data-item="{{item}}" >
        <view class="project">{{item.U_Name}}</view>
        <view style="color: #8a8a8a">
            客户名称
            <text space="ensp" style="color: black">{{item.contact}}</text>
        </view>
        <view style="color: #8a8a8a">
            签订日期
            <text space="ensp" style="color: black">{{item.SignDate}}</text>
        </view>
        <view style="color: #8a8a8a">
            签订方量
            <text space="ensp" style="color: black">{{item.ContractStere}}</text>
        </view>
        <view style="color: #8a8a8a">
            合同性质
            <text space="ensp" style="color: black">{{item.Property}}</text>
        </view>
        <view style="color: #8a8a8a" wx:if="{{item.contract_status===1}}">
            审核状态
            <text space="ensp" style="color: black">通过</text>
        </view>
        <view style="color: #8a8a8a" wx:if="{{item.contract_status===2}}">
            审核状态
            <text space="ensp" style="color: black">不通过</text>
        </view>
        <view style="color: #8a8a8a" wx:if="{{item.contract_status===2}}">
            审核理由
            <text space="ensp" style="color: black">{{item.refuse_note}}</text>
        </view>
        <view style="color: #8a8a8a" wx:if="{{item.contract_status===2||item.contract_status===1}}">
            审核时间
            <text space="ensp" style="color: black">{{item.check_time}}</text>
        </view>

    </view>
    <view class="btn">
        <view></view>
        <view class="btn2" wx:if="{{showButton2}}" data-id="{{item.id}}" bindtap="refuse">{{buttonName2}}</view>
        <view class="btn1" wx:if="{{showButton3}}" data-id="{{item.id}}" bindtap="accept">
            {{buttonName3}}
        </view>


    </view>

</view>
