.show-info {
    display: flex;
    background-color: white;
    border-radius: 10rpx;
    padding: 30rpx;
    font-size: 30rpx;
    height: 500rpx;
    margin: 30rpx;
    justify-content: space-between;
    flex-direction: column;
}

.center {
    display: flex;
    flex-direction: column;
    height: 400rpx;
    justify-content: space-between;
}

.project {
    font-weight: bold;
}

.spec {
    border-top-style: solid;
    border-top-width: 1rpx;
    border-top-color: #f1f1f1;
    border-bottom-style: solid;
    border-bottom-width: 1rpx;
    border-bottom-color: #f1f1f1;
    padding-top: 20rpx;
    padding-bottom: 20rpx;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

page {
    background-color: #f1f1f1;
}

.btn {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.btn1 {
    border-radius: 30rpx;
    color: #3a72f5;
    text-align: center;
    width: 120rpx;
    padding: 5rpx 30rpx;
    border-width: 1rpx;
    border-color: #3a72f5;
    border-style: solid;
}

.btn2 {
    border-radius: 30rpx;
    color: #3a72f5;
    width: 120rpx;
    text-align: center;
    padding: 5rpx 30rpx;
    border-width: 1rpx;
    border-color: #3a72f5;
    border-style: solid;
}

