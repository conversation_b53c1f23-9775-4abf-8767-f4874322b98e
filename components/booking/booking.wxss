/* pages/hzd/hzd.wxss */
page {
  background-color: #f1f1f1;
}
.whole {
  margin-bottom: 200rpx;
}
.main {
  background-color: #ffffff;
  margin: 30rpx;
}
.tip {
  margin-left: 35rpx;
  color: #7f7f7f;
}
.title {
  position: relative;
  font-weight: bold;
  font-size: 34rpx;
  padding-top: 60rpx;
  padding-bottom: 60rpx;
  text-align: center;
}
.img {
  position: absolute;
  right: 80rpx;
  width: 230rpx;
  height: 230rpx;
}
.title-bottom {
  padding-top: 40rpx;
}
.hdz {
  position: absolute;
  width: 300rpx;
  height: 150rpx;
  border-width: 8rpx;
  border-color: red;
  border-style: solid;
  border-radius: 100px/50px;
  color: red;
  top: 80rpx;
  font-size: 30rpx;
  right: 90rpx;
  transform: rotate(-20deg);
}
.hdz-title {
  padding-top: 25rpx;
  font-size: 20rpx;
}
.hdz-bottom {
  padding-top: 20rpx;
}
.wx-cell {
  height: 10rpx;
}
