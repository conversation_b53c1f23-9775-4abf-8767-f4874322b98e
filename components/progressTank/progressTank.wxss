/* progressTank.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 85%;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 10px;
  margin: 10px 0;
}

.tank-wrapper {
  position: relative;
  display: flex;
  width: 300rpx;
  align-items: center;
}

.progress-wrapper {
  height: 150px;
  width: 5px;
  background-color: #f0f0f0;
  margin-right: 10px;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
}

.progress-bar {
  position: absolute;
  bottom: 0;
  width: 5px;
  background: linear-gradient(to top, #30e3ca, #2bc0e4);
  border-radius: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}
.percentage-text {
  position: absolute;
  right: -10px;
  top: 5rpx;
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
  background-color: #30e3ca;
  padding: 2px 4px;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-50%);
}
.cement-tank {
  width: 100%;
  height: 150px;
  object-fit: cover;
  filter: brightness(110%) contrast(120%) saturate(120%);
}

.tank-quantity {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  font-weight: bold;
  color: white;
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
}

.info-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  color: #ffffff;
  font-size: 14px;
}

.info {
  font-size: 10px;
  color: #333;
  margin-bottom: 2px;
  text-align: center;
}

.tank-name {
  width: 100%;
  margin-top: 5px;
  font-weight: bold;
  margin-bottom: 5px;
  font-size: 12px;
  color: #333;
  text-align: center;
}
.vol{
    font-size: 11px;
    color: #333;
    text-align: center;
   font-weight: bold;
}
.progress-bar-danger {
  position: absolute;
  bottom: 0;
  width: 5px;
  background-color: #ff4c4c;
  border-radius: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
}

.percentage-text-danger {
  position: absolute;
  right: -10px;
  top: 5rpx;
  font-size: 12px;
  font-weight: bold;
  color: #ffffff;
  background-color: #ff4c4c;
  padding: 2px 4px;
  border-radius: 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-50%);
}
