Component({
  properties: {
    Name: {
      type: String,
      value: '水泥罐'
    },
    Type: {
      type: String,
      value: '厂家'
    },
    TestMessage: {
      type: String,
      value: 'cdddfrerr'
    },
    TestNo: {
      type: String,
      value: '编号'
    },
    CarNo: {
      type: String,
      value: '状态'
    },
    Comp: {
      type: String,
      value: '车牌号'
    },
    percentage: {
      type: Number,
      value: 0
    },
    Weight: {
      type: String,
      value: 0
    },
    MaxWeight:{
        type: String,
        value: '材料'
    },
    progressColor: {
      type: String,
      value: '#30e3ca'
    }
  },

  data: {
    clipPath: '',
    maskImage: '',
    tankImageUrl:'../../nm/asserts/lwj.jpg'
  },

  observers: {
    'progressColor': function(progressColor) {
      this.setData({ maskImage: `linear-gradient(${progressColor}, ${progressColor})` });
    },
    'percentage': function(percentage) {
      this.calculateClipPath(percentage);
    },
  },

  methods: {
    calculateClipPath(percentage) {
      const clipPath = `polygon(0% ${100 - percentage}%, 100% ${100 - percentage}%, 100% 100%, 0% 100%, 0% 100%, 0% 100%)`;
      this.setData({ clipPath });
      console.log(clipPath)
    }
  }
});
