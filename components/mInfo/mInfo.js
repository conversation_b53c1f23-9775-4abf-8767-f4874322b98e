// components/showInfo.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    pass: {
      type: Array,
      default: [],
    },
    showCar:{
      type: Boolean,
      default: true,
    },
    showBtn:{
      type:Boolean,
      default:false
    },
    unCheckNames: {
      type: Array,
      default: [],
    },
    btn:{
      type:String,
      default:''
    },
    btn1:{
      type:String,
      default:''
    },
    btn2:{
      type:String,
      default:''
    },
    btn3:{
      type:String,
      default:''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
  },
  pageLifetimes: {
    show: function() {
    },
    hide: function() {
      // 页面被隐藏
      //this.videoControl.pause()
    },
    resize: function(size) {
      // 页面尺寸变化
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    play(){
      this.videoControl=wx.createVideoContext('videos')
      this.triggerEvent('video-control', this.videoControl)

    },
    navTo(e){
      let id=e.currentTarget.dataset.id
      wx.navigateTo({
        url:`/pages/map/map?id=${id}`
      })
    },

    cancel(e) {
      this.triggerEvent('dialog-control',e.currentTarget.dataset.orderid)
    },
    cancel1(e) {
      this.triggerEvent('dialog-control1',e.currentTarget.dataset.orderid)
    },
    getImage(e) {
      wx.previewImage({
        urls: [e.currentTarget.dataset.img], //需要预览的图片http链接列表，注意是数组
        current: '', // 当前显示图片的http链接，默认是第一个
        success: function (res) {},
        fail: function (res) {},
        complete: function (res) {},
      })
    },
  },
})
