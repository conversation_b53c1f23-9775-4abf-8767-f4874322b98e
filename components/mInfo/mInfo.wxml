<wxs src="../../utils/formatUrl.wxs" module="utils" />
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl" />
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl" />

<view wx:for="{{pass}}" wx:key="id">
  <!--  <van-collapse value="{{unCheckNames}}" class="e-block">-->
  <!--    <view>-->
  <!--      <van-collapse-item name="{{index}}" is-link="{{controll}}">-->
  <!--        <view slot="title">-->
  <!--          <view class="ele">-->
  <!--            <view>材料品种: {{ item.material }}</view>-->
  <!--            <view>提交时间: {{ item.submitTime }}</view>-->
  <!--            <view>-->
  <!--              预约入厂时间: {{ item.storageTime}}-->
  <!--            </view>-->
  <!--            <view>产地厂家: {{ item.address }}</view>-->
  <!--            <view>车牌号: {{ item.CarNo }}</view>-->
  <!--            <view>供应商: {{ item.provider }}</view>-->
  <!--            <view>司机: {{ item.driver_name }}</view>-->
  <!--            <view>数量: {{ item.number }}吨</view>-->
  <!--            <view>单据: {{ item.send_number }}</view>-->
  <!--            <van-image-->
  <!--              use-loading-slot-->
  <!--              bindtap="getImage"-->
  <!--              data-img="{{utils.formatUrl(item.url)}}"-->
  <!--              wx:if="{{dealImageUrl.ImageUrl(item.url)}}"-->
  <!--              lazy-load-->
  <!--              width="100"-->
  <!--              height="100"-->
  <!--              src="{{utils.formatUrl(item.url)}}"-->
  <!--            />-->
  <!--            <view>-->
  <!--              审核通过时间: {{ item.issue_end_time }}-->
  <!--            </view>-->
  <!--            <view wx:if="{{item.status===3}}">-->
  <!--              入场时间: {{ item.inFactoryTime}}-->
  <!--            </view>-->
  <!--            <view wx:if="{{item.status===4}}">-->
  <!--              订单完成时间: {{ item.finishTime}}-->
  <!--            </view>-->
  <!--            <view class="submit-button">-->
  <!--              <view />-->
  <!--              <view />-->
  <!--              <van-button-->
  <!--                size="small"-->
  <!--                type="danger"-->
  <!--                data-orderid="{{item}}"-->
  <!--                bindtap="cancel"-->
  <!--                class="pass-button"-->
  <!--                >{{btn}}-->
  <!--              </van-button>-->
  <!--              <van-button-->
  <!--                      wx:if="{{showBtn}}"-->
  <!--                      size="small"-->
  <!--                      type="danger"-->
  <!--                      data-orderid="{{item}}"-->
  <!--                      bindtap="cancel1"-->
  <!--                      class="pass-button"-->
  <!--              >{{btn1}}-->
  <!--              </van-button>-->
  <!--            </view>-->
  <!--          </view>-->
  <!--        </view>-->
  <!--        &lt;!&ndash;      &ndash;&gt;-->
  <!--      </van-collapse-item>-->
  <!--    </view>-->
  <!--  </van-collapse>-->

  <view class="ele">
    <view>材料品种: {{ item.material }}</view>
    <view>提交时间: {{ item.submitTime }}</view>
    <view>预约入厂时间: {{ item.storageTime}}</view>
    <view>产地厂家: {{ item.address }}</view>
    <view>车牌号: {{ item.CarNo }}</view>
    <view>供应商: {{ item.provider }}</view>
    <view>司机: {{ item.driver_name }}</view>
    <view wx:if="{{item.number}}">数量: {{ item.number }}吨</view>
    <view>单据: {{ item.send_number }}</view>
    <view>车辆状态: {{ item.carStatus }}</view>
    <!--            <video-->
    <!--              data-id="video{{item.id}}"-->
    <!--              bindplay="play"-->
    <!--              wx:if="{{dealVideoUrl.videoUrl(item.url)}}"-->
    <!--              id="videos"-->
    <!--              src="{{utils.formatUrl(item.url)}}"-->
    <!--              binderror="videoErrorCallback"-->
    <!--              show-center-play-btn="{{false}}"-->
    <!--              show-mute-btn="{{true}}"-->
    <!--              show-play-btn="{{true}}"-->
    <!--              auto-pause-if-navigate-->
    <!--              auto-pause-if-open-native-->
    <!--              enable-auto-rotation="{{true}}"-->
    <!--              controls-->
    <!--              picture-in-picture-mode="{{['push', 'pop']}}"-->
    <!--              bindenterpictureinpicture="bindVideoEnterPictureInPicture"-->
    <!--              bindleavepictureinpicture="bindVideoLeavePictureInPicture"-->
    <!--            />-->
    <van-grid column-num="3" border="{{ false }}">
      <van-grid-item
        use-slot
        wx:for="{{item['urls']}}"
        wx:key="id"
      >
        <van-image
          use-error-slot
          use-loading-slot
          bindtap="getImage"
          data-img="{{item}}"
          lazy-load
          width="100"
          height="100"
          src="{{item}}"
        >
          <text slot="error">加载失败</text>
          <van-loading
            slot="loading"
            type="spinner"
            size="20"
            vertical
          />
        </van-image>
      </van-grid-item>
    </van-grid>
    <view>提交时间: {{ item.submitTime }}</view>
    <view>审核通过时间: {{ item.issue_end_time }}</view>
    <view wx:if="{{item.status===3}}">入场时间: {{ item.inFactoryTime}}</view>
    <view wx:if="{{item.status===4}}">订单完成时间: {{ item.finishTime}}</view>
    <view class="submit-button">
      <view/>
      <view/>
      <van-button
        plain
        size="small"
        type="danger"
        wx:if="{{item.status===1}}"
        data-orderid="{{item}}"
        bindtap="cancel"
        class="pass-button"
      >{{btn}}
      </van-button>
      <van-button
        plain
        size="small"
        wx:if="{{btn2}}"
        type="danger"
        data-orderid="{{item}}"
        bindtap="cancel"
        class="pass-button"
      >{{btn2}}
      </van-button>
      <van-button
        plain
        size="small"
        wx:if="{{btn3}}"
        type="danger"
        data-orderid="{{item}}"
        bindtap="cancel"
        class="pass-button"
      >{{btn3}}
      </van-button>
      <van-button
        plain
        size="small"
        type="danger"
        wx:if="{{showCar&(item.status===1||item.status===3)}}"
        data-id="{{item.id}}"
        bindtap="navTo"
        class="pass-button"
      >车辆位置
      </van-button>
      <van-button
        plain
        wx:if="{{showBtn}}"
        size="small"
        type="danger"
        data-orderid="{{item}}"
        bindtap="cancel1"
        class="pass-button"
      >{{btn1}}
      </van-button>
    </view>
  </view>
</view>

