Component({
  properties: {
    name: {
      type: String,
      default: '请勿外传',
    },
  },
  data: {
    // color: 'rgba(0,0,0,0.03)',
    color: 'rgba(188, 188, 188, 0.5)',
    rows: [],
    cols: []
  },
  // 组件在页面上挂载时触发,注意如果页面没卸载过，该事件就不会触发第二次
  attached() {

  },
  lifetimes: {
    ready(){
      const { windowWidth, windowHeight } = wx.getSystemInfoSync();
     // console.log(windowWidth,typeof this.data.name)
     // const rows = Math.ceil(windowWidth / (30 * this.data.name.length));
      const cols = Math.ceil(windowHeight / 100);
      //console.log(rows)
      this.setData({
        rows: new Array(2),
        cols: new Array(cols)
      });
    }
  }
})
