// 格式化时间
function time(val, option) {
  var date = getDate(val)
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var day = date.getDate()
  var week = date.getDay()
  var hour = date.getHours()
  var minute = date.getMinutes()
  var second = date.getSeconds()
  
  //获取 年月日
  if (option == 'YY-MM-DD') return [year, month, day].map(formatNumber).join('-')

  //获取 年月
  if (option == 'YY-MM') return [year, month].map(formatNumber).join('-')

  //获取 年
  if (option == 'YY') return [year].map(formatNumber).toString()

  //获取 月
  if (option == 'MM') return  [mont].map(formatNumber).toString()

  //获取 日
  if (option == 'DD') return [day].map(formatNumber).toString()

  //获取 年月日 周一 至 周日
  if (option == 'YY-MM-DD Week')  return [year, month, day].map(formatNumber).join('-') + ' ' + getWeek(week)

  //获取 月日 周一 至 周日
  if (option == 'MM-DD Week')  return [month, day].map(formatNumber).join('-') + ' ' + getWeek(week)

  //获取 周一 至 周日
  if (option == 'Week')  return getWeek(week)

  //获取 时分秒
  if (option == 'hh-mm-ss') return [hour, minute, second].map(formatNumber).join(':')

  //获取 时分
  if (option == 'hh-mm') return [hour, minute].map(formatNumber).join(':')

  //获取 分秒
  if (option == 'mm-dd') return [minute, second].map(formatNumber).join(':')

  //获取 时
  if (option == 'hh')  return [hour].map(formatNumber).toString()

  //获取 分
  if (option == 'mm')  return [minute].map(formatNumber).toString()

  //获取 秒
  if (option == 'ss') return [second].map(formatNumber).toString()

  //默认 年月日 时分秒 
  return [year, month, day].map(formatNumber).join('-') + ' ' + [hour, minute, second].map(formatNumber).join(':')
}

function formatNumber(n) {
  n = n.toString()
  return n[1] ? n : '0' + n
}

function getWeek(n) {
  switch(n) {
    case 1:
      return '星期一'
    case 2:
      return '星期二'
    case 3:
      return '星期三'
    case 4:
      return '星期四'
    case 5:
      return '星期五'
    case 6:
      return '星期六'
    case 7:
      return '星期日'
  }
}
// 格式化价格
function price(val, option) {
  var option = option || 0
   // 不是数字返回空
   if(val != null && val != '' && isNaN(val/1)) {
    return ''
  }
  if(!!val && val != 'null' && val != 'undefined') {
    return val.toFixed(option)
  }
  return ''
}
// 格式化百分比
function percent(val) {
  // 不是数字返回空
  if(val != null && val != '' && isNaN(val/1)) {
    return ''
  }
  // 如果值小于万分位（小于 0.01%）则返回 0%
  if(val < 0.0001) {
    return '0%'
  }
  // 小数 *100 后不包含小数点，返回 *100 后的结果
  if(val >= 0.0001 && ((val * 100) + '').indexOf('.') == -1) {
    return (val * 100) + '%'
  }
  // 有些小数 *100 之后会出现很长的位数，比如0.07*100=0.000000000001
  // 先处理成数组再截取 arr[1] 的前两个字符判断是否等于0，等于 0 返回 arr[0]，不等于 0 则保留两位小数
  if(val >= 0.0001 && ((val * 100) + '').indexOf('.') > -1) {
    if((val * 100 + '').split('.')[1].slice(0,2) == 0){
      return (val * 100 + '').split('.')[0] + '%'
    }
    return (val * 100).toFixed(2) / 1 + '%'
  }
}

module.exports.time = time;
module.exports.price = price;
module.exports.percent = percent;
