.container {
    overflow-x: scroll;
}

.table {
    border: solid #ebeef5;
    border-radius: 0;
    box-sizing: border-box;
    background: #fff;
    font-size: 24rpx;
    color: #606266;
    overflow: hidden;
}

/* 表头 */
.thead {
    display: flex;
}

.th {
    padding: 10rpx 20rpx;
    border-right: 2rpx solid #ebeef5;
    border-bottom: 2rpx solid #ebeef5;
    display: flex;
    align-items: center;
}

.th .txt {

    font-weight: bold;
    text-align: left;
}

.tr {
    display: flex;
}

.td {

    padding: 10rpx 20rpx;
    border-right: 2rpx solid #ebeef5;
    border-bottom: 2rpx solid #ebeef5;
    display: flex;
    align-items: center;
}

.td .txt {

    text-align: left;
    font-weight: normal;
}


.msg {
    width: 750rpx;
    height: 240rpx;
    line-height: 240rpx;
    font-size: 26rpx;
    text-align: center;
}

/* 隐藏表格滚动条 */
::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
}
