<wxs src="../utils/formatUrl.wxs" module="utils" />
<wxs src="../utils/dealVideoUrl.wxs" module="dealVideoUrl" />
<wxs src="../utils/dealImageUrl.wxs" module="dealImageUrl" />

<view wx:for="{{refuse}}" wx:key="id">
<!--    <van-collapse value="{{unCheckNames}}" class="e-block">-->
<!--        <view>-->
<!--            <van-collapse-item name="{{index}}" is-link="{{controll}}">-->
<!--                <view slot="title">-->
<!--                 -->
<!--                </view>-->
<!--                &lt;!&ndash;      &ndash;&gt;-->
<!--            </van-collapse-item>-->
<!--        </view>-->
<!--    </van-collapse>-->
    <view class="ele">
        <view>材料品种: {{ item.material }}</view>
        <view>
            预约入厂时间: {{item.storageTime}}
        </view>
        <view>产地厂家: {{ item.address }}</view>
        <view>车牌号: {{ item.CarNo }}</view>
        <view>供应商: {{ item.provider }}</view>
        <view>司机: {{ item.driver_name }}</view>
        <view>数量: {{ item.number }}</view>
        <view>单据: {{ item.send_number }}</view>
        <view>手机号: {{ item.tel }}</view>
        <!--                        <video-->
        <!--                                data-id="video{{item.id}}"-->
        <!--                                bindplay="play"-->
        <!--                                wx:if="{{dealVideoUrl.videoUrl(item.url)}}"-->
        <!--                                id="videos"-->
        <!--                                src="{{utils.formatUrl(item.url)}}"-->
        <!--                                binderror="videoErrorCallback"-->
        <!--                                show-center-play-btn="{{false}}"-->
        <!--                                show-mute-btn="{{true}}"-->
        <!--                                show-play-btn="{{true}}"-->
        <!--                                auto-pause-if-navigate-->
        <!--                                auto-pause-if-open-native-->
        <!--                                enable-auto-rotation="{{true}}"-->
        <!--                                controls-->
        <!--                                picture-in-picture-mode="{{['push', 'pop']}}"-->
        <!--                                bindenterpictureinpicture="bindVideoEnterPictureInPicture"-->
        <!--                                bindleavepictureinpicture="bindVideoLeavePictureInPicture"-->
        <!--                        />-->
        <van-image
                use-loading-slot
                bindtap="getImage"
                data-img="{{utils.formatUrl(item.url)}}"
                wx:if="{{dealImageUrl.ImageUrl(item.url)}}"
                lazy-load
                width="100"
                height="100"
                src="{{utils.formatUrl(item.url)}}"
        />
        <view>
            审核通过时间: {{ item.issue_end_time}}
        </view>
        <view wx:if="{{item.status===3}}">
            入场时间: {{ item.inFactoryTime}}
        </view>

        <view wx:if="{{item.status===3&&item.isValid===0}}">
            入场车辆审核时间: {{ item.factoryCheckTime}}
        </view>
        <view wx:if="{{item.factoryStatus===1||item.factoryStatus===2}}">
            审核类型: {{ item.factoryStatus===1?'手动过磅':'强制删除'}}
        </view>
        <view wx:if="{{item.status===3&&item.isValid===0&&item.factoryStatus===2}}">
            入场车辆审核理由: {{ item.deleteCause}}
        </view>
        <view wx:if="{{item.status===4}}">
            订单完成时间: {{ item.finishTime}}
        </view>
        <view>拒绝时间: {{ item.issue_end_time }}</view>
        <view>拒绝理由: {{ item.reject_note }}</view>
    </view>
</view>

