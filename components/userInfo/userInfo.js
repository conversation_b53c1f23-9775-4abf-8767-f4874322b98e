// components/showInfo.js
import { color,baseURL } from "../../config";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    pass1: {
      type: Array,
      default: [],
    },
    butt: {
      type: Boolean,
      default: false,
    },
    showButton1: {
      type: Boolean,
      default: false,
    },
    showButton2: {
      type: Boolean,
      default: false,
    },
    showButton3: {
      type: Boolean,
      default: false,
    },
    userType: {
      type: Number,
      default: 0,
    },
    buttonName: {
      type: String,
      default: null,
    },
    ButtonShow: {
      type: Boolean,
      default: false,
    },
    BName: {
      type: String,
      default: null,
    },
    buttonName1: {
      type: String,
      default: null,
    },
    buttonName2: {
      type: String,
      default: null,
    },
    buttonName3: {
      type: String,
      default: null,
    },
    quitQueque: {
      type: Boolean,
      default: false,
    },
    pass2: {
      type: Array,
      default: [],
    },
    activeNames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  lifetimes: {
    attached: function() {
      this.setData({
        showSlide:false,
        slideButtons: [{
          text: '普通',
          extClass: 'test',
          src: '/assets/common/edit.svg', // icon的路径
        },{
          type: 'warn',
          text: '警示',
          extClass: 'test',
          src: '/assets/common/delete.svg', // icon的路径
        }],
      });
    },
    detached: function() {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  data: {
    color: color,
    url:baseURL
  },
  pageLifetimes: {
    show: function () {},
    hide: function () {
      // 页面被隐藏
      //this.videoControl.pause()
    },
    resize: function (size) {
      // 页面尺寸变化
    },
  },
  /**
   * 组件的方法列表
   */
  methods: {
    slideButtonTap(e) {
      console.log(e.currentTarget.dataset.id)
      console.log('slide button tap', e.detail)
      if(e.detail.index===1){
        this.triggerEvent("delete",
            {
              id:e.currentTarget.dataset.id.id,
              type:this.data.userType
            });
      }
      if(e.detail.index===0){
        this.triggerEvent("edit",
            {
              name:e.currentTarget.dataset.id['用户名'],
              tel:e.currentTarget.dataset.id['手机号'],
              notice:e.currentTarget.dataset.id['通知内容'],
              id:e.currentTarget.dataset.id.id,
              type:this.data.userType
            });
      }
    },
    showRate(e) {
      //跳转页面
      this.triggerEvent("changeValue", e.currentTarget.dataset.id);
    },
    showCurse1(e) {
      //跳转页面
      this.triggerEvent("Curse1",e.currentTarget.dataset.id);
    },
    pass1(e) {
      //跳转页面
      this.triggerEvent("pass",e.currentTarget.dataset.id);
    },
    showCurse2(e) {
      //跳转页面
      this.triggerEvent("Curse2", e.currentTarget.dataset.id);
    },
    showCurse3(e) {
      //跳转页面
      this.triggerEvent("Curse3", e.currentTarget.dataset.id);
    },

    play() {
      this.videoControl = wx.createVideoContext("videos");
      this.triggerEvent("video-control", this.videoControl);
    },
    onChange(event) {
      this.setData({
        activeNames: event.detail,
      });
    },
    cancel(e) {
      this.triggerEvent("dialog-control", e.currentTarget.dataset.orderid);
    },
    getImage(e) {
      wx.previewImage({
        urls: [e.currentTarget.dataset.img], //需要预览的图片http链接列表，注意是数组
        current: "", // 当前显示图片的http链接，默认是第一个
        success: function (res) {},
        fail: function (res) {},
        complete: function (res) {},
      });
    },
  },
});
