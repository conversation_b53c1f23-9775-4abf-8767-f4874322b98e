<wxs src="../../utils/formatUrl.wxs" module="utils" />
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl" />
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl" />
<view wx:for="{{pass1}}" wx:key="id" class="page-class" wx:for-index="idx">

  <mp-slideview
    buttons="{{slideButtons}}"
    icon="{{true}}"
    data-id="{{item}}"
    bindbuttontap="slideButtonTap"
  >
    <van-collapse
      value="{{activeNames}}"
      class="e-block"
      bind:change="onChange"
    >
      <view class="ele" >

        <van-collapse-item name="{{idx}}" is-link="{{controll}}" >
            <image wx:if="{{item.status===1}}" src="{{url}}ywj.png" class="page-img"></image>

          <view slot="title">
            <view
              wx:for="{{item}}"
              wx:for-index="key"
              wx:for-item="value"
              wx:key="key"
            >
<!--              <van-cell-->
<!--                wx:if="{{key!='id'}}"-->
<!--                title="{{key}}"-->
<!--                value="{{value}}"-->
<!--                border="{{ false }}"-->
<!--              />-->
              <van-field
                      wx:if="{{key!='id'&key!='status'}}"
                      value="{{value}}"
                      label="{{key}}"
                      type="textarea"
                      autosize
                      readonly="{{true}}"
                      border="{{ false }}"
              />
            </view>
            <van-button
              wx:if="{{butt}}"
              custom-class="button"
              size="normal"
              class="ewm"
              color="{{ color }}"
              data-id="{{item.id}}"
              catchtap="showRate"
              >查看评价</van-button
            >
            <van-button
              custom-class="button"
              wx:if="{{quitQueque}}"
              size="normal"
              class="ewm"
              color="{{ color }}"
              data-id="{{item.id}}"
              catchtap="showRate"
              >{{buttonName}}</van-button
            >
            <van-button
              custom-class="button"
              wx:if="{{showButton1}}"
              size="normal"
              class="ewm1"
              color="{{ color }}"
              data-id="{{item.id}}"
              catchtap="showCurse1"
              >{{buttonName1}}</van-button
            >
            <van-button
              custom-class="button"
              wx:if="{{ButtonShow}}"
              size="normal"
              class="ewm22"
              color="{{ color }}"
              data-id="{{item.id}}"
              catchtap="pass1"
              >{{BName}}</van-button
            >
            <van-button
              custom-class="button"
              wx:if="{{showButton2}}"
              size="normal"
              class="ewm2"
              color="{{ color }}"
              data-id="{{item.id}}"
              catchtap="showCurse2"
              >{{buttonName2}}</van-button
            >
            <van-button
              custom-class="button"
              wx:if="{{showButton3}}"
              size="normal"
              class="ewm3"
              color="{{ color }}"
              data-id="{{item.id}}"
              catchtap="showCurse3"
              >{{buttonName3}}</van-button
            >
          </view>
          <view
            wx:for="{{pass2[idx]}}"
            wx:for-index="key"
            wx:for-item="value"
            wx:key="key"
          >
            <van-cell
              wx:if="{{key!='urls'&&key!='total'&&key!='status'}}"
              title="{{key}}"
              value="{{value}}"
              border="{{ false }}"
            />
            <van-row wx:if="{{key==='urls'}}" wx:for="{{value}}" wx:key="item">
              <van-col span="8">
                <van-image
                  use-error-slot
                  use-loading-slot
                  bindtap="getImage"
                  data-img="{{utils.formatUrl(item)}}"
                  lazy-load
                  width="100"
                  height="100"
                  src="{{utils.formatUrl(item)}}"
                >
                  <text slot="error">加载失败</text>
                  <van-loading
                    slot="loading"
                    type="spinner"
                    size="20"
                    vertical
                  />
                </van-image>
              </van-col>
            </van-row>
          </view>
        </van-collapse-item>
      </view>
    </van-collapse>
  </mp-slideview>
</view>
