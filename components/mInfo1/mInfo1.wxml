<wxs src="../../utils/formatUrl.wxs" module="utils" />
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl" />
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl" />

<view wx:for="{{pass}}" wx:key="id">
<!--    <van-collapse value="{{unCheckNames}}" class="e-block">-->
<!--        <view>-->
<!--            <van-collapse-item name="{{index}}" is-link="{{controll}}">-->
<!--                <view slot="title">-->
<!--                  -->
<!--                </view>-->
<!--                &lt;!&ndash;      &ndash;&gt;-->
<!--            </van-collapse-item>-->
<!--        </view>-->
<!--    </van-collapse>-->
    <view class="ele">
        <view>材料品种: {{ item.material }}</view>
        <view>
            预约入厂时间: {{ item.storageTime}}
        </view>
        <view>产地厂家: {{ item.address }}</view>
        <view>提交时间: {{ item.submitTime }}</view>
        <view>车牌号: {{ item.CarNo }}</view>
        <view>供应商: {{ item.provider }}</view>
        <view>司机: {{ item.driver_name }}</view>
        <view>数量: {{ item.number }}吨</view>
        <view>单据: {{ item.send_number }}</view>
        <van-image
                use-loading-slot
                bindtap="getImage"
                data-img="{{utils.formatUrl(item.url)}}"
                wx:if="{{dealImageUrl.ImageUrl(item.url)}}"
                lazy-load
                width="100"
                height="100"
                src="{{utils.formatUrl(item.url)}}"
        />
        <view wx:if="{{item.status===3}}">
            入场时间: {{ item.inFactoryTime}}
        </view>
        <view wx:if="{{item.status===4}}">
            订单完成时间: {{ item.finishTime}}
        </view>
        <view class="submit-button">
            <view />
            <view />
            <van-button
                    plain
                    size="small"
                    type="danger"
                    data-orderid="{{item.id}}"
                    bindtap="refuse"
                    class="pass-button"
            >不通过
            </van-button>
            <van-button
                    plain
                    size="small"
                    type="danger"
                    data-orderid="{{item.id}}"
                    bindtap="pass"
                    class="pass-button"
            >通过
            </van-button>
        </view>
    </view>
</view>

