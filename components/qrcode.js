import drawQrcode from '../utils/weapp.qrcode.min.js'

Component({
  behaviors: [],
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  properties: {
    text: {
      type: String,
      default: 'https://github.com/yingye',
    },
    isDraw: {
      type: <PERSON><PERSON><PERSON>,
      value: true,
      observer: function(newVal, oldVal) {
        if (newVal) {
          drawQrcode({
            width: 100,
            height: 100,
            canvasId: 'qrcodePro',
            text: this.data.text,
            _this: this
          })
        }
      }
    }
  },
  data: {
  },

  attached: function () {
  },
  ready: function () {
    // drawQrcode({
    //   width: 80,
    //   height: 80,
    //   canvasId: 'qrcodePro',
    //   text: 'test drawQrcode in component',
    //   _this: this
    // })
  },
  moved: function () {},
  detached: function () {},

  methods: {
  }
})
