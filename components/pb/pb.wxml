<view class="container" wx:for="{{info}}" wx:key="index">
    <view class="top">

        <view class="top1">{{item['任务单编号']}}</view>
        <view class="{{item['上传状态'] > 0 ? '' : 'top-blank'}} top3">{{item['上传状态'] > 0 ? '已有配合比' : '无配合比'}}</view>
    </view>
    <van-divider></van-divider>
    <view class="center-title">{{item['工程名称']}}</view>
    <view class="center-content">
        <text class="weui-form-preview__label">施工单位</text> {{item['施工单位']}}
    </view>
    <view class="center-content">
        <text class="weui-form-preview__label">计划开盘时间</text> {{item['调度计划开盘时间']}}
    </view>
    <view class="center-content">
        <text class="weui-form-preview__label">施工部位</text> {{item['施工部位']}}
    </view>
    <view class="center-content">
        <text class="weui-form-preview__label">状态</text>
        <text style="color:orange">{{item['状态']}}</text>
    </view>
    <view class="center-content" wx:if="{{item.refuseNote}}">
        <text class="weui-form-preview__label">拒绝理由</text> {{item.refuseNote}}
    </view>
    <van-divider></van-divider>
    <view class="center-content1">
        <view>{{item['浇筑方式']}}</view>
        <view>{{item['坍落度']}}</view>
        <view>{{item['砼强度']}}</view>

    </view>
    <van-divider></van-divider>
    <view class="bottom-content">

        <van-button custom-style="padding:10rpx 40rpx;width:250rpx;" class="bottom-button2" data-item="{{item}}"
                    size="small"
                    bindtap="checkDetail" round="true">调整配比
        </van-button>
        <!--        <view class="bottom-button1">合同详情</view>-->
        <!--        <view class="bottom-button2">合同下单</view>-->
    </view>
</view>
