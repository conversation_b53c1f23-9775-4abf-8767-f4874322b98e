<view wx:for="{{info}}" wx:key="index" class="show-info">
    <view class="center" bindtap="toDetail" data-id="{{item.id}}">
        <view class="project">{{item.ProjectName}}</view>
        <view style="color: #8a8a8a">
            客户名称
            <text space="ensp" style="color: black">{{item.ClientName}}</text>
        </view>
        <view style="color: #8a8a8a">
            施工部位
            <text space="ensp" style="color: black">{{item.ConstructionSite}}</text>
        </view>
        <view style="color: #8a8a8a ">
            特殊须知
            <text space="ensp" style="color: black">{{item.Reading ? item.Reading : ''}}</text>
        </view>
        <view class="spec">
            <view>{{item.StrengthLevel}}</view>
            <view>{{ item.Slumps}}</view>
            <view>{{ item.CastingMode }}</view>
        </view>
    </view>
    <view class="btn">
        <view></view>
        <view class="btn1" data-id="{{item}}"
              bindtap="edit">
            编辑
        </view>

    </view>
</view>
