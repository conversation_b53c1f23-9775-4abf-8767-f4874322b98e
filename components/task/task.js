// components/showOrder.js
import {color, station} from "../../config";

Component({
    /**
     * 组件的属性列表
     */
    properties: {
        info: {
            type: Array,
            default: [],
            unCheckNames: [],
            activeName: "1",
        },
    },

    /**
     * 组件的初始数据
     */
    data: {
        controll: false,
        color: color,
        station: station
    },

    /**
     * 组件的方法列表
     */
    methods: {
        edit(e) {
            //跳转页面
            this.triggerEvent("edit", e.currentTarget.dataset.id);
        },
        toDetail(e) {
            wx.navigateTo({
                url: `/pages/orderDetail/orderDetail?id=${e.currentTarget.dataset.id}`
            })
        },
        onChange(event) {
            this.setData({
                activeName: event.detail,
            });
        },
    },
});
