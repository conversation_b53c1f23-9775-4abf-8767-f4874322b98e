
<view class="container">
    <view class="header-container">
        <view class="table">
            <view class="table-header">
                <view class="table-cell table-cell-fixed">{{headers[0].label}}</view>
                <view
                        class="table-cell"
                        wx:for="{{headers}}"
                        wx:if="{{index>0}}"
                        wx:key="header"
                        data-key="{{item.key}}"
                        bindtap="handleSort"
                >
                    {{item.label}}{{currentSortKey === item.key ? (sortOrder === 'asc' ? '▲' : '▼') : ''}}
                </view>
            </view>
        </view>
    </view>
    <scroll-view class="outer-scroll-view" scroll-x scroll-y bindscroll="onScroll">
        <view class="table">
            <block wx:for="{{tableData}}" wx:key="rowIndex">
                <view class="table-row">
                    <view
                            class="table-cell table-cell-fixed"
                            data-row-index="{{index}}"
                            bindtap="handleRowClick"
                    >
                        {{item.expanded ? '▼' : '▶'}} {{item.month}}
                    </view>
                    <view class="table-cell">{{item.name}}</view>
                    <view class="table-cell">{{item.value}}</view>
                </view>
                <block wx:if="{{item.expanded}}">
                    <view
                            wx:for="{{item.suppliers}}"
                            wx:key="supplierIndex"
                            wx:for-item="childItem"
                            class="table-row"
                    >
                        <view class="table-cell table-cell-fixed">{{childItem.month}}</view>
                        <view class="table-cell">{{childItem.name}}</view>
                        <view class="table-cell">{{childItem.value}}</view>
                    </view>
                </block>
            </block>
        </view>
    </scroll-view>
</view>
