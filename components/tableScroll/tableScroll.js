Component({
  properties: {
    headers: {
      type: Array,
      value: [],
    },
    tableData: {
      type: Array,
      value: [],
    },
  },
  data: {
    // ...
    expandedRow: -1,
    scrollTop: 0,
    scrollLeft: 0,
    currentSortKey: 'value',
    sortOrder: 'asc',
    isScrollingVertically: false,
  },
  methods: {
    handleRowClick(e) {
      const rowIndex = e.currentTarget.dataset.rowIndex;
      const newTableData = this.data.tableData.map((item, index) => {
        if (index === rowIndex) {
          item.expanded = !item.expanded;
        } else {
          item.expanded = false;
        }
        return item;
      });
      //console.log(newTableData)
      this.setData({ tableData: newTableData });
    },
    // ...
    onScroll: function (e) {
      const {scrollTop, scrollLeft} = e.detail;
      this.setData({
        scrollTop,
        scrollLeft,
        isScrollingVertically: scrollTop > 7,
      });
     // console.log(this.data.isScrollingVertically,scrollTop,this.data.scrollTop)
    },
    handleSort(e) {
      const key = e.currentTarget.dataset.key;
      const { tableData, currentSortKey, sortOrder } = this.data;
      let newSortOrder = 'asc';

      if (currentSortKey === key) {
        newSortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
      }

      const sortedData = tableData.slice().sort((a, b) => {
        if (newSortOrder === 'asc') {
          return a[key] > b[key] ? 1 : -1;
        } else {
          return a[key] < b[key] ? 1 : -1;
        }
      });

      this.setData({
        tableData: sortedData,
        currentSortKey: key,
        sortOrder: newSortOrder,
      });
    },
  },
});
