
.container {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    font-size: 14px;
    line-height: 1.5;
    color: #303133;
}

.header-container {
    position: sticky;
    top: 0;
    z-index: 10;
    width:700rpx;
    background-color: white;
}

.outer-scroll-view {
    border: 1px solid #ebeef5;
    width: 700rpx;
    height: 800rpx;
    border-radius: 4px;
    overflow: hidden;
}


.scroll-view {
    position: absolute;
    overflow: scroll;
    width:700rpx;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.table {
    display: table;
    min-width: 100%;
    table-layout: fixed;
    word-wrap: break-word;
    background-color: #fff;
    border-collapse: collapse;
}

.table-row {
    display: table-row;
}

.table-header {
    display: table-row;
    font-weight: 600;
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
}

.table-cell {
    display: table-cell;
    padding: 12px;
    width: 200rpx; /* 为单元格设置宽度 */
    border-right: 1px solid #ebeef5;
    text-align: left;
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    user-select: none;
}

.table-cell:last-child {
    border-right: none;
}

.table-row:nth-child(even) {
    background-color: #fafbfc;
}

.table-cell-fixed {
    position: sticky;
    left: 0;
    width: 200rpx; /* 设置与 .table-cell 相同的宽度 */
    z-index: 1;
}
