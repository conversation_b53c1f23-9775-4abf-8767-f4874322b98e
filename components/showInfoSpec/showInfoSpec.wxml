<wxs src="../../utils/formatUrl.wxs" module="utils" />
<wxs src="../../utils/dealVideoUrl.wxs" module="dealVideoUrl" />
<wxs src="../../utils/dealImageUrl.wxs" module="dealImageUrl" />
<view wx:for="{{pass1}}" wx:key="id" class="page-class" wx:for-index="idx">
    <van-collapse value="{{activeNames}}" class="e-block" bind:change="onChange">
        <view class="ele">
            <van-collapse-item name="{{idx}}" is-link="{{controll}}">
                <view slot="title">
                    <view
                            wx:for="{{item}}"
                            wx:for-index="key"
                            wx:for-item="value"
                            wx:key="key"
                    >
                        <van-cell
                                wx:if="{{(key!='id')&&(key!='status')}}"
                                title="{{key}}"
                                value="{{value}}"
                                border="{{ false }}"
                        />
                    </view>
                    <van-button
                            wx:if="{{item.status===3}}"
                            custom-class="button"
                            size="normal"
                            class="ewm"
                            color="{{ color }}"
                            data-id="{{item}}"
                            catchtap="showRate"
                    >查看评价</van-button
                    >
                    <van-button
                            custom-class="button"
                            wx:if="{{item.status===2}}"
                            size="normal"
                            class="ewm"
                            color="{{ color }}"
                            data-id="{{item}}"
                            catchtap="showRate"
                    >查看/追评</van-button
                    >
                </view>
                <view
                        wx:for="{{pass2[idx]}}"
                        wx:for-index="key"
                        wx:for-item="value"
                        wx:key="key"
                >
                    <van-cell
                            wx:if="{{key!='urls'&&key!='total'}}"
                            title="{{key}}"
                            value="{{value}}"
                            border="{{ false }}"
                    />
                    <van-row wx:if="{{key==='urls'}}" wx:for="{{value}}" wx:key="item">
                        <van-col span="8">
                            <van-image
                                    use-error-slot
                                    use-loading-slot
                                    bindtap="getImage"
                                    data-img="{{item}}"
                                    lazy-load
                                    width="100"
                                    height="100"
                                    src="{{utils.formatUrl(item)}}"
                            >
                                <text slot="error">加载失败</text>
                                <van-loading slot="loading" type="spinner" size="20" vertical />
                            </van-image>
                        </van-col>
                    </van-row>
                </view>

                <!--      -->
            </van-collapse-item>
        </view>
    </van-collapse>
</view>

