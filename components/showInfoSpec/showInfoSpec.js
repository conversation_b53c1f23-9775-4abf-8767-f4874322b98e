// components/showInfo.js
import {color} from "../../config";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    pass1: {
      type: Array,
      default: [],
    },
    butt: {
      type: Boolean,
      default: false,
    },
    pass2: {
      type: Array,
      default: [],
    },
    activeNames: {
      type: Array,
      default: [],
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    color:color
  },
  pageLifetimes: {
    show: function() {
    },
    hide: function() {
      // 页面被隐藏
      //this.videoControl.pause()
    },
    resize: function(size) {
      // 页面尺寸变化
    }
  },
  /**
   * 组件的方法列表
   */
  methods: {
    showRate(e){
      console.log(e.currentTarget.dataset.id)
      //跳转页面
      this.triggerEvent('changeValue', e.currentTarget.dataset.id)
    },
    onChange(event) {
      this.setData({
        activeNames: event.detail,
      });
    },
    cancel(e) {
      this.triggerEvent('dialog-control',e.currentTarget.dataset.orderid)
    },
    getImage(e) {
      wx.previewImage({
        urls: [e.currentTarget.dataset.img], //需要预览的图片http链接列表，注意是数组
        current: '', // 当前显示图片的http链接，默认是第一个
        success: function (res) {},
        fail: function (res) {},
        complete: function (res) {},
      })
    },
  },
})
