# 用户站点管理数据库说明

## 表结构

### wx_user_manage_site 表
```sql
CREATE TABLE [dbo].[wx_user_manage_site](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NULL,
	[tel] [nvarchar](50) NULL,
	[user_name] [nvarchar](50) NULL,
	[site_code] [nvarchar](50) NULL,
	[update_time] [datetime] NULL,
	[create_time] [datetime] NULL
) ON [PRIMARY]
```

## 业务逻辑说明

### 权限设计原则
1. **默认全部权限**：如果用户在 `wx_user_manage_site` 表中没有任何记录，表示该用户可以访问所有站点
2. **明确权限限制**：如果用户在表中有记录，则只能访问表中明确记录的站点
3. **先删除后新增**：每次更新用户权限时，先删除该用户的所有记录，再添加新的记录

### 站点编码说明
- `"001,002,003"` - 具体的站点编码列表，用逗号分隔
- `"ALL"` - 表示所有站点权限（在表中不存储记录）
- `""` 或 `NULL` - 同样表示所有站点权限

## 存储过程说明

### 1. sp_manage_user_site - 设置用户站点权限

**功能**：为指定用户设置站点权限（先删除该用户所有站点记录，再新增）

**参数**：
- `@user_id INT` - 用户ID
- `@tel NVARCHAR(50)` - 用户电话
- `@user_name NVARCHAR(50)` - 用户姓名
- `@site_codes NVARCHAR(MAX)` - 站点编码列表，用逗号分隔

**使用示例**：
```sql
-- 设置具体站点权限
EXEC [dbo].[sp_manage_user_site] 
    @user_id = 1,
    @tel = '13800138001',
    @user_name = '张三',
    @site_codes = '001,002,003';

-- 设置全部站点权限
EXEC [dbo].[sp_manage_user_site] 
    @user_id = 2,
    @tel = '13800138002',
    @user_name = '李四',
    @site_codes = 'ALL';
```

**返回值**：
```sql
result_code: 0表示成功，非0表示失败
result_message: 操作结果消息
```

### 2. sp_get_user_sites - 查询用户站点权限

**功能**：查询用户的站点权限信息

**参数**：
- `@user_id INT` - 用户ID（可选，为NULL时返回所有用户信息）

**使用示例**：
```sql
-- 查询所有用户的站点权限
EXEC [dbo].[sp_get_user_sites];

-- 查询特定用户的站点权限
EXEC [dbo].[sp_get_user_sites] @user_id = 1;
```

**返回字段**：
- 查询所有用户时：`user_id, user_name, tel, site_count, site_codes, last_update_time`
- 查询特定用户时：`user_id, user_name, tel, site_code, create_time, update_time`

### 3. sp_check_user_site_permission - 检查用户站点权限

**功能**：检查用户是否有特定站点的权限

**参数**：
- `@user_id INT` - 用户ID
- `@site_code NVARCHAR(50)` - 站点编码

**使用示例**：
```sql
EXEC [dbo].[sp_check_user_site_permission] 
    @user_id = 1, 
    @site_code = '001';
```

**返回值**：
```sql
has_permission: 1表示有权限，0表示无权限
```

### 4. sp_get_multiple_user_sites - 批量查询用户站点权限

**功能**：批量查询多个用户的站点权限信息

**参数**：
- `@user_ids NVARCHAR(MAX)` - 用户ID列表，用逗号分隔

**使用示例**：
```sql
EXEC [dbo].[sp_get_multiple_user_sites] @user_ids = '1,2,3,4';
```

## 兼容性说明

### SQL Server版本兼容
- 使用 `FOR XML PATH` 和 `STUFF` 函数替代 `STRING_AGG`
- 兼容 SQL Server 2008 及以上版本
- 避免使用 SQL Server 2017+ 才支持的新特性

### 字符串聚合实现
```sql
-- 兼容旧版本的字符串聚合方法
STUFF((
    SELECT ',' + site_code 
    FROM [dbo].[wx_user_manage_site] s2 
    WHERE s2.user_id = s1.user_id 
    FOR XML PATH('')
), 1, 1, '') as site_codes
```

## 使用场景

### 1. 初始化用户权限
用户第一次被分配站点权限时，调用 `sp_manage_user_site`

### 2. 更新用户权限
用户权限变更时，调用 `sp_manage_user_site`（会自动先删除再新增）

### 3. 权限检查
在业务逻辑中，调用 `sp_check_user_site_permission` 检查用户是否有访问某个站点的权限

### 4. 权限查询
管理界面显示用户权限时，调用 `sp_get_user_sites` 获取权限列表

## 注意事项

1. **事务处理**：所有存储过程都使用了事务，确保数据一致性
2. **错误处理**：包含完整的错误处理和日志记录
3. **性能考虑**：查询时建议在 `user_id` 和 `site_code` 字段上建立索引
4. **数据清理**：定期清理无效的用户记录 