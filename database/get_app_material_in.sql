USE [CIM_HX]
GO
/****** Object:  StoredProcedure [dbo].[get_app_material_in]    Script Date: 2025/1/18 9:44:19 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
--获取原材进料统计分析数据
ALTER PROCEDURE [dbo].[get_app_material_in] @分类   NVARCHAR(10),
        @起始条数 INT,
        @结束条数 INT,
        @开始日期 NVARCHAR(10),
        @截止日期 NVARCHAR(10)
  AS 

BEGIN

		SET NOCOUNT ON; 
/*
SET @起始条数='{起始条数}';      
SET @结束条数='{结束条数}';  
SET @分类='{分类}';      
SET @开始日期='{开始日期}'  
SET @截止日期='{截止日期}'     
*/ 
     
DECLARE @Sql NVARCHAR(max),
        @Str NVARCHAR(200);
DECLARE @startime NVARCHAR(20),
        @endime   NVARCHAR(20),
        @day      INTEGER;
DECLARE @TS INT;

IF( Len(@开始日期) = 4 )
  BEGIN
      SELECT @startime = StartDate
      FROM   (SELECT TOP 1 CONVERT(CHAR(19), StartDate, 121) StartDate
              FROM   dbo.Sys_Month_T
              WHERE  Substring(U_Month, 1, 4) = @开始日期
              ORDER  BY StartDate) t1;

      SELECT @endime = EndDate
      FROM   (SELECT TOP 1 CONVERT(NVARCHAR(19), EndDate, 121) EndDate
              FROM   dbo.Sys_Month_T
              WHERE  Substring(U_Month, 1, 4) = @截止日期
              ORDER  BY EndDate DESC) t2;
  END

IF( Len(@开始日期) = 7 )
  BEGIN
      SELECT @startime = CONVERT(NVARCHAR(19), StartDate, 121)
      FROM   dbo.Sys_Month_T
      WHERE  U_Month = @开始日期;

      SELECT @endime = CONVERT(NVARCHAR(19), EndDate, 121)
      FROM   dbo.Sys_Month_T
      WHERE  U_Month = @截止日期;
  END

IF( Len(@开始日期) > 7 )
  BEGIN
      SELECT @TS = CASE
                     WHEN StartTime > EndTime THEN 1
                     ELSE 0
                   END
      FROM   dbo.Sys_Time_T

      SET @startime=@开始日期

      SELECT @startime = @开始日期 + ' ' + StartTime
      FROM   dbo.Sys_Time_T

      SELECT @endime = CONVERT(NVARCHAR(10), Cast(@截止日期 AS DATETIME)+@TS, 120)
                       + ' ' + EndTime
      FROM   Sys_Time_T
  END;

WITH T
     AS (SELECT A.Material_Supplier_ID,
                B.Price,
                B.EffectiveDate,
                B.Sys_Material_ID
         FROM   Material_SupplyContract_T A
                LEFT JOIN (SELECT Material_SupplyContract_ID,
                                  Price,
                                  EffectiveDate,
                                  Sys_Material_ID
                           FROM   Material_SupplyContract_Detail_T
                           WHERE  IsValid = 1) B
                       ON B.Material_SupplyContract_ID = A.ID
         WHERE  A.IsValid = 1)
SELECT E.ID                                                                                                 AS CLDLID,
	   A.U_Code 过磅单号,
	   A.FinalDate,
	   A.BusNumber as 车号,
	   row_number()over(order by A.FinalDate DESC) as [序号],
	   F.Storage AS 仓位,
		CONVERT(VARCHAR(100), A.InitialDate, 120) as 进场称重时间,
		     CONVERT(VARCHAR(100), A.FinalDate, 120) AS 出场称重时间,
		isnull(A.GrossWeight, 0)/1000 AS 毛重,
		isnull(A.TARE, 0)/1000 AS 皮重,
		isnull(MinusProportion, 0) AS 扣重比例,
		isnull(MinusWeight, 0)/1000 AS 扣重重量,
		isnull(A.CubicMeters, 0) AS 立方米,
		A.SupplierNumber [供方单据号],
		A.SupplierTicketData [供方单据量],
		C.U_Name 供应商,
		convert(float,isnull((select top 1 Price from T where T.Material_Supplier_ID =A.Material_Supplier_ID 
		and T.Sys_Material_ID = A.Sys_Material_ID and T.EffectiveDate <= A.FinalDate order by T.EffectiveDate desc),0)) as 单价,
       A.Material_Supplier_ID                                                                               AS GYSID,
       A.Sys_Material_ID                                                                                    AS CLID,
       B.NameNorms                                                                                          AS 材料名称,
       Isnull(a.PrintNetWeight, 0) / 1000                                                                   [打印净重],
       CONVERT(FLOAT, Isnull((SELECT TOP 1 Price
                              FROM   T
                              WHERE  T.Material_Supplier_ID = A.Material_Supplier_ID
                                     AND T.Sys_Material_ID = A.Sys_Material_ID
                              ORDER  BY T.EffectiveDate DESC), 0))                                          AS 当前价格,
       CONVERT(FLOAT, Isnull((SELECT TOP 1 Price
                              FROM   T
                              WHERE  T.Material_Supplier_ID = A.Material_Supplier_ID
                                     AND T.Sys_Material_ID = A.Sys_Material_ID
                                     AND T.EffectiveDate <= A.FinalDate
                              ORDER  BY T.EffectiveDate DESC), 0)) * ( Isnull(a.PrintNetWeight, 0) / 1000 ) 金额
into #TEMP
FROM   Material_WeighingData_T A
       LEFT JOIN Sys_Material_T B
              ON A.Sys_Material_ID = B.ID
       LEFT JOIN Material_Supplier_T C
              ON A.Material_Supplier_ID = C.ID
       LEFT JOIN Material_SecondType_T D
              ON B.Material_SecondType_ID = D.ID
       LEFT JOIN Material_FirstType_T E
              ON E.ID = D.Material_FirstType_ID
       LEFT JOIN Sys_Storage_T F ON A.Sys_Storage_ID=F.ID   
WHERE  a.IsValid = 1
       AND A.MaterialType = 1
       AND CONVERT(NVARCHAR(19), a.FinalDate, 121) >= @startime
       AND CONVERT(CHAR(19), a.FinalDate, 121) <= @endime
       AND a.PrintNetWeight > 0;

IF @分类 = '时间'
  BEGIN
      WITH T2
           AS (SELECT TOP 1000 Row_number()
                                 OVER(
                                   ORDER BY CONVERT(DATETIME, 进场称重时间) DESC) AS [XH],
                               过磅单号,
                               FinalDate,
                               材料名称,
                               打印净重,
                               金额,
                               CLDLID,
							   车号,
								仓位,
								进场称重时间,
								出场称重时间,
								毛重,
								皮重,
								扣重比例,
								扣重重量,
								立方米,
								[供方单据号],
								[供方单据量]/1000 供方单据量,
								供应商,
								单价,
								GYSID,
								CLID,
								当前价格
               FROM   #TEMP)
      SELECT *
      FROM   T2
      WHERE  XH BETWEEN @起始条数 AND @结束条数
  END;
  
  
IF @分类 = '材料'
  BEGIN
      WITH T2
           AS (SELECT TOP 1000 Row_number()
                                 OVER(
                                   ORDER BY CLDLID) AS [XH],
                               CLID,
                               材料名称,
                               Sum(打印净重)            AS 打印净重,
                               Sum(金额)              AS 金额
               FROM   #TEMP
               GROUP  BY CLDLID,
                         材料名称,
                         CLID)
      SELECT *
      FROM   T2
      WHERE  XH BETWEEN @起始条数 AND @结束条数
  END;

IF @分类 = '供应商'
  BEGIN
      WITH T2
           AS (SELECT TOP 1000 Row_number()
                                 OVER(
                                   ORDER BY GYSID) AS [XH],
                               GYSID,
                               供应商,
                               Sum(打印净重)           AS 打印净重,
                               Sum(金额)             AS 金额
               FROM   #TEMP
               GROUP  BY 供应商,
                         GYSID)
      SELECT *
      FROM   T2
      WHERE  XH BETWEEN @起始条数 AND @结束条数
  END;

-- 修改最终查询结果，按照进场称重时间倒序排列
SELECT CLID,
       GYSID,
       供应商,
       材料名称,
       当前价格,
       Sum(金额) / Sum(打印净重) AS 平均价格,
       Sum(打印净重)           AS 打印净重,
       Sum(金额)             AS 金额,
       MIN(进场称重时间) AS 最早进场时间,
       MAX(进场称重时间) AS 最晚进场时间
FROM   #TEMP
GROUP  BY CLID,
          GYSID,
          供应商,
          材料名称,
          当前价格
ORDER  BY MAX(进场称重时间) DESC,  -- 按照最晚进场称重时间倒序排列
          材料名称,
          供应商

IF Object_id('tempdb..#TEMP') IS NOT NULL
  DROP TABLE #TEMP;
	                

END 