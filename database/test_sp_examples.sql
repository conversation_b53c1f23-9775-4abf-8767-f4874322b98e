-- 用户站点管理存储过程测试示例

-- ===============================
-- 1. 设置用户站点权限测试
-- ===============================

-- 测试1: 为用户1设置具体的站点权限（北京和上海站点）
EXEC [dbo].[sp_manage_user_site] 
    @user_id = 1,
    @tel = '13800138001',
    @user_name = '张三',
    @site_codes = '001,002';

-- 测试2: 为用户2设置所有站点权限
EXEC [dbo].[sp_manage_user_site] 
    @user_id = 2,
    @tel = '13800138002',
    @user_name = '李四',
    @site_codes = 'ALL';

-- 测试3: 为用户3设置单个站点权限
EXEC [dbo].[sp_manage_user_site] 
    @user_id = 3,
    @tel = '13800138003',
    @user_name = '王五',
    @site_codes = '003';

-- 测试4: 更新用户1的站点权限（先删除原有的，再添加新的）
EXEC [dbo].[sp_manage_user_site] 
    @user_id = 1,
    @tel = '13800138001',
    @user_name = '张三',
    @site_codes = '001,003,004';

-- ===============================
-- 2. 查询用户站点权限测试
-- ===============================

-- 查询所有用户的站点权限信息
EXEC [dbo].[sp_get_user_sites];

-- 查询特定用户的站点权限信息
EXEC [dbo].[sp_get_user_sites] @user_id = 1;

-- ===============================
-- 3. 检查用户站点权限测试
-- ===============================

-- 检查用户1是否有站点001的权限
EXEC [dbo].[sp_check_user_site_permission] 
    @user_id = 1, 
    @site_code = '001';

-- 检查用户2是否有站点005的权限（用户2设置为ALL，应该返回1）
EXEC [dbo].[sp_check_user_site_permission] 
    @user_id = 2, 
    @site_code = '005';

-- 检查用户3是否有站点001的权限（用户3只有站点003，应该返回0）
EXEC [dbo].[sp_check_user_site_permission] 
    @user_id = 3, 
    @site_code = '001';

-- ===============================
-- 4. 批量查询用户站点权限测试
-- ===============================

-- 批量查询用户1,2,3的站点权限信息
EXEC [dbo].[sp_get_multiple_user_sites] @user_ids = '1,2,3';

-- ===============================
-- 5. 查看表中的实际数据
-- ===============================

-- 查看wx_user_manage_site表的所有数据
SELECT * FROM [dbo].[wx_user_manage_site] ORDER BY user_id, site_code;

-- 按用户统计站点数量
SELECT 
    user_id,
    user_name,
    tel,
    COUNT(*) as site_count,
    STUFF((
        SELECT ',' + site_code 
        FROM [dbo].[wx_user_manage_site] s2 
        WHERE s2.user_id = s1.user_id 
        FOR XML PATH('')
    ), 1, 1, '') as site_codes
FROM [dbo].[wx_user_manage_site] s1
GROUP BY user_id, user_name, tel
ORDER BY user_id;

-- ===============================
-- 6. 清理测试数据（可选）
-- ===============================

-- 取消注释下面的语句来清理测试数据
-- DELETE FROM [dbo].[wx_user_manage_site] WHERE user_id IN (1,2,3); 