USE [cim_wx]
GO

-- =============================================
-- Author:		花生-AI进化论
-- Create date: 2025-06-11
-- Description:	更新wx_order表结构以支持二级审核系统
-- =============================================

-- 检查并添加业务审核相关字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[wx_order]') AND name = 'businessReviewerId')
BEGIN
    ALTER TABLE [dbo].[wx_order] ADD [businessReviewerId] VARCHAR(50) NULL
    PRINT '已添加字段: businessReviewerId (业务审核人员ID)'
END
ELSE
    PRINT '字段 businessReviewerId 已存在'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[wx_order]') AND name = 'businessReviewTime')
BEGIN
    ALTER TABLE [dbo].[wx_order] ADD [businessReviewTime] DATETIME NULL
    PRINT '已添加字段: businessReviewTime (业务审核时间)'
END
ELSE
    PRINT '字段 businessReviewTime 已存在'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[wx_order]') AND name = 'businessReviewer')
BEGIN
    ALTER TABLE [dbo].[wx_order] ADD [businessReviewer] VARCHAR(50) NULL
    PRINT '已添加字段: businessReviewer (业务审核人员姓名)'
END
ELSE
    PRINT '字段 businessReviewer 已存在'

-- 检查并添加管理审核相关字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[wx_order]') AND name = 'manageReviewerId')
BEGIN
    ALTER TABLE [dbo].[wx_order] ADD [manageReviewerId] VARCHAR(50) NULL
    PRINT '已添加字段: manageReviewerId (管理审核人员ID)'
END
ELSE
    PRINT '字段 manageReviewerId 已存在'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[wx_order]') AND name = 'manageReviewTime')
BEGIN
    ALTER TABLE [dbo].[wx_order] ADD [manageReviewTime] DATETIME NULL
    PRINT '已添加字段: manageReviewTime (管理审核时间)'
END
ELSE
    PRINT '字段 manageReviewTime 已存在'

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[wx_order]') AND name = 'manageReviewer')
BEGIN
    ALTER TABLE [dbo].[wx_order] ADD [manageReviewer] VARCHAR(50) NULL
    PRINT '已添加字段: manageReviewer (管理审核人员姓名)'
END
ELSE
    PRINT '字段 manageReviewer 已存在'

-- 添加订单状态说明注释
EXEC sp_addextendedproperty 
@name = N'MS_Description', 
@value = N'订单状态：0-待审核，1-审核通过(兼容老版本)，2-业务审核不通过，3-业务审核通过等待管理审核，4-管理审核通过，5-管理审核不通过', 
@level0type = N'Schema', @level0name = 'dbo', 
@level1type = N'Table', @level1name = 'wx_order', 
@level2type = N'Column', @level2name = 'status'

-- 创建索引以优化查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[wx_order]') AND name = 'IX_wx_order_status_reviewers')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_wx_order_status_reviewers] 
    ON [dbo].[wx_order] ([status], [businessReviewerId], [manageReviewerId])
    PRINT '已创建索引: IX_wx_order_status_reviewers'
END
ELSE
    PRINT '索引 IX_wx_order_status_reviewers 已存在'

PRINT '数据库表结构更新完成！'

/*
二级审核系统字段说明：

1. businessReviewerId (VARCHAR(50)): 业务审核人员电话/ID
2. businessReviewTime (DATETIME): 业务审核时间
3. businessReviewer (VARCHAR(50)): 业务审核人员姓名
4. manageReviewerId (VARCHAR(50)): 管理审核人员电话/ID  
5. manageReviewTime (DATETIME): 管理审核时间
6. manageReviewer (VARCHAR(50)): 管理审核人员姓名

订单状态流转：
0 → 3 (业务审核通过) → 4 (管理审核通过)
0 → 2 (业务审核不通过)
3 → 5 (管理审核不通过)

原有字段兼容性：
- reviewerId: 保留，用于向后兼容
- issueEndTime: 保留，用于记录最终审核完成时间
- RefuseNote: 保留，用于记录拒绝原因
- IsValid: 保留，审核不通过时设为0
*/
GO 