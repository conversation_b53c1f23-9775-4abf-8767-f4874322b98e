USE [cim_wx]
GO

-- =============================================
-- Author:		花生-AI进化论
-- Create date: 2025-06-11
-- Description:	根据角色和状态查询订单列表（支持二级审核系统）
-- =============================================
ALTER PROCEDURE [dbo].[Get_Orders_By_Status]
    @role_id INT,              -- 角色ID: 1-管理人员, 7-业务人员
    @user_tel VARCHAR(50),     -- 当前用户电话
    @page_size INT = 20,       -- 每页记录数，默认20
    @page_index INT = 1,       -- 页码，从1开始
    @order_status INT = NULL   -- 指定查询的订单状态，NULL表示查询所有可审核的订单
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @offset INT = (@page_index - 1) * @page_size;
    DECLARE @total_count INT;

    -- 根据角色确定查询条件
    IF @role_id = 7  -- 业务人员
    BEGIN
        -- 业务人员只能看到待业务审核的订单（状态为0）
        SELECT @total_count = COUNT(1)
        FROM wx_order 
        WHERE ((@order_status IS NULL AND status = 0) 
               OR (@order_status IS NOT NULL AND status = @order_status))
              AND (businessReviewerId IS NULL OR businessReviewerId = '');

        SELECT 
            id,
            orderNo,
            U_Code,
            Market_Contract_ID,
            ConstructionSite,
            Stere,
            Sys_ConcreteStrength_ID,
            PlannedTime,
            Address,
            Site_Contacter,
            Site_Contact_PhoneNumber,
            status,
            createTime,
            creator,
            businessReviewerId,
            businessReviewTime,
            businessReviewer,
            manageReviewerId,
            manageReviewTime,
            manageReviewer,
            RefuseNote,
            CASE status
                WHEN 0 THEN '待业务审核'
                WHEN 2 THEN '业务审核不通过'
                WHEN 3 THEN '业务审核通过，等待管理审核'
                WHEN 4 THEN '管理审核通过'
                WHEN 5 THEN '管理审核不通过'
                ELSE '未知状态'
            END as statusText,
            @total_count as totalCount
        FROM wx_order 
        WHERE ((@order_status IS NULL AND status = 0) 
               OR (@order_status IS NOT NULL AND status = @order_status))
              AND (businessReviewerId IS NULL OR businessReviewerId = '')
        ORDER BY createTime DESC
        OFFSET @offset ROWS FETCH NEXT @page_size ROWS ONLY;
    END
    ELSE IF @role_id = 1  -- 管理人员
    BEGIN
        -- 管理人员可以看到等待管理审核的订单（状态为3）
        SELECT @total_count = COUNT(1)
        FROM wx_order 
        WHERE ((@order_status IS NULL AND status = 3) 
               OR (@order_status IS NOT NULL AND status = @order_status))
              AND (manageReviewerId IS NULL OR manageReviewerId = '');

        SELECT 
            id,
            orderNo,
            U_Code,
            Market_Contract_ID,
            ConstructionSite,
            Stere,
            Sys_ConcreteStrength_ID,
            PlannedTime,
            Address,
            Site_Contacter,
            Site_Contact_PhoneNumber,
            status,
            createTime,
            creator,
            businessReviewerId,
            businessReviewTime,
            businessReviewer,
            manageReviewerId,
            manageReviewTime,
            manageReviewer,
            RefuseNote,
            distribute_site,
            CASE status
                WHEN 0 THEN '待业务审核'
                WHEN 2 THEN '业务审核不通过'
                WHEN 3 THEN '业务审核通过，等待管理审核'
                WHEN 4 THEN '管理审核通过'
                WHEN 5 THEN '管理审核不通过'
                ELSE '未知状态'
            END as statusText,
            @total_count as totalCount
        FROM wx_order 
        WHERE ((@order_status IS NULL AND status = 3) 
               OR (@order_status IS NOT NULL AND status = @order_status))
              AND (manageReviewerId IS NULL OR manageReviewerId = '')
        ORDER BY businessReviewTime DESC, createTime DESC
        OFFSET @offset ROWS FETCH NEXT @page_size ROWS ONLY;
    END
    ELSE
    BEGIN
        -- 无效角色，返回空结果
        SELECT 0 as totalCount;
        SELECT TOP 0 * FROM wx_order;
    END
END

/*
使用示例：

1. 业务人员查询待审核订单：
   EXEC Get_Orders_By_Status @role_id=7, @user_tel='***********', @page_size=10, @page_index=1

2. 管理人员查询待审核订单：
   EXEC Get_Orders_By_Status @role_id=1, @user_tel='***********', @page_size=10, @page_index=1

3. 查询特定状态的订单：
   EXEC Get_Orders_By_Status @role_id=1, @user_tel='***********', @page_size=10, @page_index=1, @order_status=4

返回字段说明：
- statusText: 订单状态的中文描述
- totalCount: 总记录数，用于前端分页
- businessReviewer/manageReviewer: 审核人员姓名
- businessReviewTime/manageReviewTime: 审核时间

查询逻辑：
- 业务人员：只能看到状态为0且未被业务审核的订单
- 管理人员：只能看到状态为3且未被管理审核的订单
- 支持分页查询，提高大数据量情况下的查询性能

订单状态说明：
0 - 待业务审核
2 - 业务审核不通过
3 - 业务审核通过，等待管理审核
4 - 管理审核通过
5 - 管理审核不通过
*/

GO 