-- 用户站点管理存储过程
-- 功能：为指定用户设置站点权限（先删除该用户所有站点记录，再新增）

CREATE PROCEDURE [dbo].[sp_manage_user_site]
    @user_id INT,                    -- 用户ID
    @tel NVARCHAR(50),              -- 用户电话
    @user_name NVARCHAR(50),        -- 用户姓名
    @site_codes NVARCHAR(MAX)       -- 站点编码列表，用逗号分隔，如 "001,002,003" 或 "ALL" 表示所有站点
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @current_time DATETIME = GETDATE();
    DECLARE @result_code INT = 0;
    DECLARE @result_message NVARCHAR(200) = '操作成功';
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- 1. 先删除该用户的所有站点记录
        DELETE FROM [dbo].[wx_user_manage_site] 
        WHERE user_id = @user_id;
        
        -- 2. 如果site_codes不为空且不是'ALL'，则添加新的站点记录
        IF @site_codes IS NOT NULL AND @site_codes != '' AND @site_codes != 'ALL'
        BEGIN
            -- 处理逗号分隔的站点编码
            DECLARE @site_code NVARCHAR(50);
            DECLARE @pos INT;
            DECLARE @temp_codes NVARCHAR(MAX) = @site_codes + ',';
            
            WHILE LEN(@temp_codes) > 0
            BEGIN
                SET @pos = CHARINDEX(',', @temp_codes);
                IF @pos > 0
                BEGIN
                    SET @site_code = LTRIM(RTRIM(SUBSTRING(@temp_codes, 1, @pos - 1)));
                    
                    -- 插入站点记录（只有当site_code不为空时）
                    IF LEN(@site_code) > 0
                    BEGIN
                        INSERT INTO [dbo].[wx_user_manage_site] 
                        (user_id, tel, user_name, site_code, create_time, update_time)
                        VALUES 
                        (@user_id, @tel, @user_name, @site_code, @current_time, @current_time);
                    END
                    
                    SET @temp_codes = SUBSTRING(@temp_codes, @pos + 1, LEN(@temp_codes));
                END
                ELSE
                BEGIN
                    BREAK;
                END
            END
        END
        -- 如果是'ALL'或空字符串，表示该用户可以访问所有站点，不需要插入具体记录
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @result_code = ERROR_NUMBER();
        SET @result_message = ERROR_MESSAGE();
        
        -- 记录错误日志（可选）
        DECLARE @error_msg NVARCHAR(MAX) = 
            'Error in sp_manage_user_site: ' + 
            'User ID: ' + CAST(@user_id AS NVARCHAR(10)) + 
            ', Error: ' + @result_message;
        
        RAISERROR(@error_msg, 16, 1);
    END CATCH
    
    -- 返回操作结果
    SELECT @result_code as result_code, @result_message as result_message;
END

GO

-- 查询用户站点权限的存储过程（兼容旧版本SQL Server）
CREATE PROCEDURE [dbo].[sp_get_user_sites]
    @user_id INT = NULL              -- 用户ID，如果为NULL则返回所有用户的站点信息
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @user_id IS NULL
    BEGIN
        -- 返回所有用户的站点信息，按用户分组
        -- 使用FOR XML PATH和STUFF替代STRING_AGG（兼容旧版本SQL Server）
        SELECT 
            user_id,
            user_name,
            tel,
            COUNT(*) as site_count,
            STUFF((
                SELECT ',' + site_code 
                FROM [dbo].[wx_user_manage_site] s2 
                WHERE s2.user_id = s1.user_id 
                FOR XML PATH('')
            ), 1, 1, '') as site_codes,
            MAX(update_time) as last_update_time
        FROM [dbo].[wx_user_manage_site] s1
        GROUP BY user_id, user_name, tel
        ORDER BY user_name;
    END
    ELSE
    BEGIN
        -- 返回指定用户的站点信息
        SELECT 
            user_id,
            user_name,
            tel,
            site_code,
            create_time,
            update_time
        FROM [dbo].[wx_user_manage_site]
        WHERE user_id = @user_id
        ORDER BY site_code;
    END
END

GO

-- 检查用户是否有站点权限的存储过程
CREATE PROCEDURE [dbo].[sp_check_user_site_permission]
    @user_id INT,
    @site_code NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @has_permission BIT = 0;
    DECLARE @site_count INT = 0;
    
    -- 检查该用户是否有任何站点记录
    SELECT @site_count = COUNT(*) 
    FROM [dbo].[wx_user_manage_site] 
    WHERE user_id = @user_id;
    
    IF @site_count = 0
    BEGIN
        -- 如果用户没有任何站点记录，表示可以访问所有站点
        SET @has_permission = 1;
    END
    ELSE
    BEGIN
        -- 检查是否有该特定站点的权限
        IF EXISTS (
            SELECT 1 FROM [dbo].[wx_user_manage_site] 
            WHERE user_id = @user_id AND site_code = @site_code
        )
        BEGIN
            SET @has_permission = 1;
        END
    END
    
    SELECT @has_permission as has_permission;
END

GO

-- 批量获取多个用户的站点权限信息（可选）
CREATE PROCEDURE [dbo].[sp_get_multiple_user_sites]
    @user_ids NVARCHAR(MAX)         -- 用户ID列表，用逗号分隔，如 "1,2,3,4"
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 创建临时表来存储用户ID
    CREATE TABLE #temp_user_ids (user_id INT);
    
    -- 解析用户ID字符串
    DECLARE @user_id NVARCHAR(10);
    DECLARE @pos INT;
    DECLARE @temp_ids NVARCHAR(MAX) = @user_ids + ',';
    
    WHILE LEN(@temp_ids) > 0
    BEGIN
        SET @pos = CHARINDEX(',', @temp_ids);
        IF @pos > 0
        BEGIN
            SET @user_id = LTRIM(RTRIM(SUBSTRING(@temp_ids, 1, @pos - 1)));
            
            IF ISNUMERIC(@user_id) = 1 AND LEN(@user_id) > 0
            BEGIN
                INSERT INTO #temp_user_ids (user_id) VALUES (CAST(@user_id AS INT));
            END
            
            SET @temp_ids = SUBSTRING(@temp_ids, @pos + 1, LEN(@temp_ids));
        END
        ELSE
        BEGIN
            BREAK;
        END
    END
    
    -- 返回指定用户的站点权限信息
    SELECT 
        s.user_id,
        s.user_name,
        s.tel,
        COUNT(*) as site_count,
        STUFF((
            SELECT ',' + site_code 
            FROM [dbo].[wx_user_manage_site] s2 
            WHERE s2.user_id = s.user_id 
            FOR XML PATH('')
        ), 1, 1, '') as site_codes,
        MAX(s.update_time) as last_update_time
    FROM [dbo].[wx_user_manage_site] s
    INNER JOIN #temp_user_ids t ON s.user_id = t.user_id
    GROUP BY s.user_id, s.user_name, s.tel
    
    UNION ALL
    
    -- 返回没有站点记录的用户（表示可以访问所有站点）
    SELECT 
        t.user_id,
        '' as user_name,
        '' as tel,
        0 as site_count,
        'ALL' as site_codes,
        NULL as last_update_time
    FROM #temp_user_ids t
    WHERE t.user_id NOT IN (SELECT DISTINCT user_id FROM [dbo].[wx_user_manage_site])
    
    ORDER BY user_id;
    
    -- 清理临时表
    DROP TABLE #temp_user_ids;
END

GO 