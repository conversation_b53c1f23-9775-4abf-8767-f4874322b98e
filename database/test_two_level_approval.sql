USE [cim_wx]
GO

-- =============================================
-- Author:		花生-AI进化论
-- Create date: 2025-06-11
-- Description:	二级审核系统测试脚本
-- 测试所有审核场景和边界条件
-- =============================================

PRINT '开始二级审核系统测试...'
PRINT '================================'

-- 清理测试数据
IF EXISTS (SELECT 1 FROM wx_order WHERE id = 'TEST_ORDER_001')
    DELETE FROM wx_order WHERE id = 'TEST_ORDER_001'

-- 创建测试订单
INSERT INTO wx_order (
    id, orderNo, U_Code, Market_Contract_ID, ConstructionSite, 
    Stere, Sys_ConcreteStrength_ID, PlannedTime, Address, 
    Site_Contacter, Site_Contact_PhoneNumber, status, 
    createTime, creator, IsValid
) VALUES (
    'TEST_ORDER_001', 'ORDER20250611001', 'UC001', 'MC001', '测试工地',
    '100', 'C30', '2025-06-12 08:00:00', '北京市测试地址',
    '张三', '13800138000', 0,
    GETDATE(), '测试用户', 1
)

PRINT '1. 测试订单创建成功，订单号：TEST_ORDER_001'

-- ================================
-- 测试场景1：业务人员审核通过
-- ================================
PRINT ''
PRINT '测试场景1：业务人员审核通过'
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='1',
    @orderId='TEST_ORDER_001',
    @refuseNote='',
    @distributsite='北京站点',
    @name='业务员李四',
    @passid='test_db',
    @role_id=7

-- 验证状态
SELECT '业务审核后状态检查:' as 检查项, 
       status as 订单状态, 
       businessReviewerId as 业务审核人,
       businessReviewer as 业务审核人姓名,
       businessReviewTime as 业务审核时间
FROM wx_order WHERE id = 'TEST_ORDER_001'

-- ================================
-- 测试场景2：业务人员重复审核（应该被拒绝）
-- ================================
PRINT ''
PRINT '测试场景2：业务人员重复审核（应该被拒绝）'
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='1',
    @orderId='TEST_ORDER_001',
    @refuseNote='',
    @distributsite='北京站点',
    @name='业务员王五',
    @passid='test_db',
    @role_id=7

-- ================================
-- 测试场景3：管理人员审核通过
-- ================================
PRINT ''
PRINT '测试场景3：管理人员审核通过'
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='1',
    @orderId='TEST_ORDER_001',
    @refuseNote='',
    @distributsite='北京站点',
    @name='管理员赵六',
    @passid='test_db',
    @role_id=1

-- 验证最终状态
SELECT '管理审核后状态检查:' as 检查项, 
       status as 订单状态, 
       businessReviewer as 业务审核人,
       businessReviewTime as 业务审核时间,
       manageReviewer as 管理审核人,
       manageReviewTime as 管理审核时间
FROM wx_order WHERE id = 'TEST_ORDER_001'

-- ================================
-- 测试场景4：管理人员重复审核（应该被拒绝）
-- ================================
PRINT ''
PRINT '测试场景4：管理人员重复审核（应该被拒绝）'
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='1',
    @orderId='TEST_ORDER_001',
    @refuseNote='',
    @distributsite='北京站点',
    @name='管理员钱七',
    @passid='test_db',
    @role_id=1

-- ================================
-- 测试场景5：业务审核不通过流程
-- ================================
PRINT ''
PRINT '测试场景5：业务审核不通过流程'

-- 创建第二个测试订单
IF EXISTS (SELECT 1 FROM wx_order WHERE id = 'TEST_ORDER_002')
    DELETE FROM wx_order WHERE id = 'TEST_ORDER_002'

INSERT INTO wx_order (
    id, orderNo, U_Code, Market_Contract_ID, ConstructionSite, 
    Stere, status, createTime, creator, IsValid
) VALUES (
    'TEST_ORDER_002', 'ORDER20250611002', 'UC002', 'MC002', '测试工地2',
    '200', 0, GETDATE(), '测试用户2', 1
)

-- 业务审核不通过
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='2',
    @orderId='TEST_ORDER_002',
    @refuseNote='材料规格不符合要求',
    @distributsite='北京站点',
    @name='业务员孙八',
    @passid='test_db',
    @role_id=7

-- 验证业务拒绝状态
SELECT '业务拒绝后状态检查:' as 检查项, 
       status as 订单状态, 
       RefuseNote as 拒绝原因,
       IsValid as 是否有效,
       businessReviewer as 业务审核人
FROM wx_order WHERE id = 'TEST_ORDER_002'

-- 尝试管理审核被拒绝的订单（应该失败）
PRINT ''
PRINT '测试：尝试管理审核被业务拒绝的订单（应该失败）'
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='1',
    @orderId='TEST_ORDER_002',
    @refuseNote='',
    @distributsite='北京站点',
    @name='管理员周九',
    @passid='test_db',
    @role_id=1

-- ================================
-- 测试场景6：管理审核不通过流程
-- ================================
PRINT ''
PRINT '测试场景6：管理审核不通过流程'

-- 创建第三个测试订单
IF EXISTS (SELECT 1 FROM wx_order WHERE id = 'TEST_ORDER_003')
    DELETE FROM wx_order WHERE id = 'TEST_ORDER_003'

INSERT INTO wx_order (
    id, orderNo, U_Code, Market_Contract_ID, ConstructionSite, 
    Stere, status, createTime, creator, IsValid
) VALUES (
    'TEST_ORDER_003', 'ORDER20250611003', 'UC003', 'MC003', '测试工地3',
    '150', 0, GETDATE(), '测试用户3', 1
)

-- 业务审核通过
EXEC Manage_check_order_improved 
    @tel='13700137003',
    @CHECK='1',
    @orderId='TEST_ORDER_003',
    @refuseNote='',
    @distributsite='北京站点',
    @name='业务员吴十',
    @passid='test_db',
    @role_id=7

-- 管理审核不通过
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='2',
    @orderId='TEST_ORDER_003',
    @refuseNote='预算超出限制',
    @distributsite='北京站点',
    @name='管理员郑十一',
    @passid='test_db',
    @role_id=1

-- 验证管理拒绝状态
SELECT '管理拒绝后状态检查:' as 检查项, 
       status as 订单状态, 
       RefuseNote as 拒绝原因,
       IsValid as 是否有效,
       businessReviewer as 业务审核人,
       manageReviewer as 管理审核人
FROM wx_order WHERE id = 'TEST_ORDER_003'

-- ================================
-- 测试场景7：无效角色ID测试
-- ================================
PRINT ''
PRINT '测试场景7：无效角色ID测试'
EXEC Manage_check_order_improved 
    @tel='***********',
    @CHECK='1',
    @orderId='TEST_ORDER_001',
    @refuseNote='',
    @distributsite='北京站点',
    @name='无效角色用户',
    @passid='test_db',
    @role_id=99

-- ================================
-- 测试场景8：订单查询功能测试
-- ================================
PRINT ''
PRINT '测试场景8：订单查询功能测试'

-- 创建待业务审核的订单
IF EXISTS (SELECT 1 FROM wx_order WHERE id = 'TEST_ORDER_004')
    DELETE FROM wx_order WHERE id = 'TEST_ORDER_004'

INSERT INTO wx_order (
    id, orderNo, U_Code, Market_Contract_ID, ConstructionSite, 
    Stere, status, createTime, creator, IsValid
) VALUES (
    'TEST_ORDER_004', 'ORDER20250611004', 'UC004', 'MC004', '测试工地4',
    '300', 0, GETDATE(), '测试用户4', 1
)

PRINT '业务人员查询待审核订单：'
EXEC Get_Orders_By_Status @role_id=7, @user_tel='***********', @page_size=10, @page_index=1

PRINT ''
PRINT '管理人员查询待审核订单：'
EXEC Get_Orders_By_Status @role_id=1, @user_tel='***********', @page_size=10, @page_index=1

-- ================================
-- 测试总结
-- ================================
PRINT ''
PRINT '================================'
PRINT '测试总结：'
PRINT '================================'

SELECT '测试订单状态汇总:' as 汇总信息
SELECT 
    id as 订单ID,
    status as 状态,
    CASE status
        WHEN 0 THEN '待业务审核'
        WHEN 2 THEN '业务审核不通过'
        WHEN 3 THEN '等待管理审核'
        WHEN 4 THEN '管理审核通过'
        WHEN 5 THEN '管理审核不通过'
        ELSE '未知状态'
    END as 状态说明,
    businessReviewer as 业务审核人,
    manageReviewer as 管理审核人,
    RefuseNote as 拒绝原因,
    IsValid as 是否有效
FROM wx_order 
WHERE id LIKE 'TEST_ORDER_%'
ORDER BY id

PRINT ''
PRINT '测试完成！'
PRINT '预期结果：'
PRINT '- TEST_ORDER_001: 状态4（管理审核通过）'
PRINT '- TEST_ORDER_002: 状态2（业务审核不通过）'
PRINT '- TEST_ORDER_003: 状态5（管理审核不通过）'
PRINT '- TEST_ORDER_004: 状态0（待业务审核）'
PRINT ''
PRINT '如果实际结果与预期一致，说明二级审核系统工作正常！'

-- 清理测试数据（可选）
-- DELETE FROM wx_order WHERE id LIKE 'TEST_ORDER_%'
-- PRINT '测试数据已清理'

/*
测试用例说明：

1. 正常审核流程：业务通过 → 管理通过
2. 防重复审核：同一级别不能重复审核
3. 业务拒绝流程：业务直接拒绝，管理无法操作
4. 管理拒绝流程：业务通过后管理拒绝
5. 权限验证：无效角色ID被拒绝
6. 查询功能：按角色权限查询待审核订单

验证要点：
- 状态流转的正确性
- 防重复审核机制
- 权限隔离效果
- 审核信息记录完整性
- 查询结果准确性

订单状态说明：
0 - 待业务审核
2 - 业务审核不通过
3 - 业务审核通过，等待管理审核
4 - 管理审核通过
5 - 管理审核不通过
*/

GO 