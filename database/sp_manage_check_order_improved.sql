USE [cim_wx]
GO
/****** Object:  StoredProcedure [dbo].[Manage_check_order_improved]    Script Date: 2025-06-11 14:11:26 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		花生-AI进化论
-- Create date: 2025-06-11
-- Description:	改进版订单二级审核存储过程
-- 功能说明：
-- role_id = 7: 业务人员审核
--   check = 1: 审核通过，status = 3（业务审核通过，等待管理审核）
--   check = 2: 审核不通过，status = 2（业务审核不通过）
-- role_id = 1: 管理人员审核  
--   check = 1: 审核通过，status = 4（管理审核通过），同步到CIM系统
--   check = 2: 审核不通过，status = 5（管理审核不通过）
-- =============================================
ALTER PROCEDURE [dbo].[Manage_check_order_improved] 
  @tel        VARCHAR(50),    -- 审核人员电话
  @CHECK      VARCHAR(50),    -- 审核结果: 1-通过, 2-不通过
  @orderId    VARCHAR(50),    -- 订单ID
  @refuseNote VARCHAR(50),    -- 拒绝原因备注
  @distributsite VARCHAR(50), -- 分配站点
  @name       VARCHAR(50),    -- 审核人员姓名
  @passid     VARCHAR(50),    -- 数据库连接ID
  @role_id    INT             -- 角色ID: 1-管理人员, 7-业务人员

AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @str NVARCHAR(MAX),
            @isPaperCheck VARCHAR(100),
            @OUTPUT NVARCHAR(100);

    DECLARE @COUNT INT,
            @currentStatus INT,
            @reviewerId VARCHAR(50),
            @businessReviewerId VARCHAR(50),  -- 业务审核人员ID
            @manageReviewerId VARCHAR(50);    -- 管理审核人员ID

    -- 变量声明（用于CIM系统同步）
    DECLARE @address VARCHAR(100),
            @CastingMode VARCHAR(100),
            @ConstructionSite VARCHAR(100),
            @ContractProjectID VARCHAR(100),
            @LoadDistance VARCHAR(100),
            @Market_Contract_ID VARCHAR(100),
            @MaterialRequirements VARCHAR(100),
            @PlannedTime VARCHAR(100),
            @Site_Contact_PhoneNumber VARCHAR(100),
            @Site_Contacter VARCHAR(100),
            @Remarks VARCHAR(100),
            @Slumps VARCHAR(100),
            @CreatorID VARCHAR(100),
            @Stere VARCHAR(100),
            @Sys_ConcreteStrength_ID VARCHAR(100),
            @U_Code VARCHAR(100),
            @Reviewer VARCHAR(100),
            @distributcode VARCHAR(50),
            @TabulationPerson VARCHAR(100);

    -- 获取订单当前状态和审核信息
    SELECT @currentStatus = status,
           @businessReviewerId = businessReviewerId,
           @manageReviewerId = manageReviewerId
    FROM wx_order
    WHERE id = @orderId;

    -- 检查订单是否存在
    IF @@ROWCOUNT = 0
    BEGIN
        SELECT 0 tag, '订单不存在' msg
        RETURN
    END

    -- 二级审核逻辑验证
    IF @role_id = 7  -- 业务人员审核
    BEGIN
        -- 检查是否已经被业务人员审核过
        IF @currentStatus != 0  -- 只有状态为0的订单才能被业务审核
        BEGIN
            SELECT 0 tag, '该订单已经被业务人员审核完毕，不能重复审核' msg
            RETURN
        END
        
        -- 业务人员审核逻辑
        IF @CHECK = '1'  -- 业务审核通过
        BEGIN
            UPDATE wx_order
            SET status = 3,  -- 业务审核通过，等待管理审核
                businessReviewerId = @tel,
                businessReviewTime = GETDATE(),
                businessReviewer = @name
            WHERE id = @orderId;
            
            SELECT 1 tag, '业务审核通过，已提交给管理人员审核' msg
        END
        ELSE IF @CHECK = '2'  -- 业务审核不通过
        BEGIN
            UPDATE wx_order
            SET status = 2,  -- 业务审核不通过
                businessReviewerId = @tel,
                businessReviewTime = GETDATE(),
                businessReviewer = @name,
                RefuseNote = @refuseNote,
                IsValid = 0
            WHERE id = @orderId;
            
            SELECT 1 tag, '业务审核不通过，订单已拒绝' msg
        END
    END
    ELSE IF @role_id = 1  -- 管理人员审核
    BEGIN
        -- 检查订单是否处于等待管理审核状态
        IF @currentStatus != 3
        BEGIN
            IF @currentStatus IN (4, 5)
                SELECT 0 tag, '该订单已经被管理人员审核完毕，不能重复审核' msg
            ELSE IF @currentStatus = 0
                SELECT 0 tag, '该订单还未通过业务人员审核，无法进行管理审核' msg
            ELSE IF @currentStatus = 2
                SELECT 0 tag, '该订单已被业务人员拒绝，无法进行管理审核' msg
            ELSE
                SELECT 0 tag, '订单状态异常，无法进行审核' msg
            RETURN
        END
        
        -- 管理人员审核逻辑
        IF @CHECK = '1'  -- 管理审核通过
        BEGIN
            -- 更新订单状态为管理审核通过
            UPDATE wx_order
            SET status = 4,  -- 管理审核通过
                manageReviewerId = @tel,
                manageReviewTime = GETDATE(),
                manageReviewer = @name,
                distribute_site = @distributsite,
                TabulationPerson = @name
            WHERE id = @orderId;

            -- 获取订单详细信息用于同步到CIM系统
            SELECT @address = w.Address,
                   @CastingMode = w.castingId,
                   @ConstructionSite = w.ConstructionSite,
                   @ContractProjectID = w.ContractProjectID,
                   @LoadDistance = w.LoadDistance,
                   @Market_Contract_ID = w.Market_Contract_ID,
                   @MaterialRequirements = w.MaterialRequirements,
                   @PlannedTime = CONVERT(VARCHAR(100), w.PlannedTime, 120),
                   @Remarks = w.Remarks,
                   @Site_Contact_PhoneNumber = w.Site_Contact_PhoneNumber,
                   @Site_Contacter = w.Site_Contacter,
                   @Slumps = w.Slumps,
                   @Stere = w.Stere,
                   @CreatorID = w.id,
                   @distributcode = w.distribute_site,
                   @Sys_ConcreteStrength_ID = w.Sys_ConcreteStrength_ID,
                   @U_Code = w.U_Code,
                   @Reviewer = '',
                   @TabulationPerson = w.TabulationPerson
            FROM wx_order w
            WHERE w.id = @orderId;

            -- 同步数据到CIM系统
            SET @str = @passid + '.dbo.create_market_order_unit '''
                       + ISNULL(@Market_Contract_ID, '') + ''','''
                       + ISNULL(@ConstructionSite, '') + ''',''' 
                       + ISNULL(@LoadDistance, '') + ''',''' 
                       + ISNULL(@Slumps, '') + ''',''' 
                       + ISNULL(@CastingMode, '') + ''','''
                       + ISNULL(@MaterialRequirements, '') + ''','''
                       + ISNULL(@TabulationPerson, '') + ''','''
                       + ISNULL(@Sys_ConcreteStrength_ID, '') + ''',''' 
                       + ISNULL(@Stere, '') + ''',''' 
                       + ISNULL(@PlannedTime, '') + ''',''' 
                       + ISNULL(@Reviewer, '') + ''',''' 
                       + ISNULL(@address, '') + ''',''' 
                       + ISNULL(@Site_Contacter, '') + ''',''' 
                       + ISNULL(@Site_Contact_PhoneNumber, '') + ''','''
                       + ISNULL(@Remarks, '') + ''',''' 
                       + ISNULL(@CreatorID, '') + ''','''
                       + ISNULL(@distributcode, '') + ''','''
                       + ISNULL(@ContractProjectID, '') + ''''

            PRINT @str;
            EXEC sp_executesql @str;
            
            SELECT 1 tag, '管理审核通过，订单已生成并同步到CIM系统' msg
        END
        ELSE IF @CHECK = '2'  -- 管理审核不通过
        BEGIN
            UPDATE wx_order
            SET status = 5,  -- 管理审核不通过
                manageReviewerId = @tel,
                manageReviewTime = GETDATE(),
                manageReviewer = @name,
                RefuseNote = @refuseNote,
                IsValid = 0
            WHERE id = @orderId;
            
            SELECT 1 tag, '管理审核不通过，订单已拒绝' msg
        END
    END
    ELSE
    BEGIN
        SELECT 0 tag, '无效的角色ID，无法进行审核操作' msg
        RETURN
    END
END

/*
订单状态说明：
0 - 待审核（初始状态）
2 - 业务审核不通过
3 - 业务审核通过，等待管理审核
4 - 管理审核通过（最终通过状态）
5 - 管理审核不通过

审核流程：
1. 订单创建后状态为 0（待审核）
2. 业务人员（role_id=7）审核：
   - 通过 → 状态变为 3（等待管理审核）
   - 不通过 → 状态变为 2（业务拒绝）
3. 管理人员（role_id=1）审核（仅当状态为3时）：
   - 通过 → 状态变为 4（最终通过），同步到CIM系统
   - 不通过 → 状态变为 5（管理拒绝）

防重复审核机制：
- 业务人员只能审核状态为 0 的订单
- 管理人员只能审核状态为 3 的订单
- 已审核过的订单会被拒绝重复审核
*/ 