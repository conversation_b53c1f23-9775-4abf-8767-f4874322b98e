{"version": 3, "sources": ["inflect.js", "methods.js", "util.js", "inflections.js", "defaults.js", "native.js"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,ACHA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AFOA,AGTA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,AIZA,AHSA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;AELA,ADGA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ACFA,AFMA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA,ACHA;ADIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.js", "sourcesContent": ["// Requiring modules\n\nmodule.exports = function (attach) {\n  var methods = require('./methods');\n\n  if (attach) {\n    require('./native')(methods);\n  }\n\n  return methods\n};\n", "// The Inflector transforms words from singular to plural, class names to table names, modularized class names to ones without,\n// and class names to foreign keys. The default inflections for pluralization, singularization, and uncountable words are kept\n// in inflections.coffee\n//\n// If you discover an incorrect inflection and require it for your application, you'll need\n// to correct it yourself (explained below).\n\nvar util = require('./util');\n\nvar inflect = module.exports;\n\n// Import [inflections](inflections.html) instance\ninflect.inflections = require('./inflections')\n\n// Gives easy access to add inflections to this class\ninflect.inflect = function (fn) {\n  fn(inflect.inflections);\n};\n\n// By default, _camelize_ converts strings to UpperCamelCase. If the argument to _camelize_\n// is set to _false_ then _camelize_ produces lowerCamelCase.\n//\n// _camelize_ will also convert '/' to '.' which is useful for converting paths to namespaces.\n//\n//     \"bullet_record\".camelize()             // => \"BulletRecord\"\n//     \"bullet_record\".camelize(false)        // => \"bulletRecord\"\n//     \"bullet_record/errors\".camelize()      // => \"BulletRecord.Errors\"\n//     \"bullet_record/errors\".camelize(false) // => \"bulletRecord.Errors\"\n//\n// As a rule of thumb you can think of _camelize_ as the inverse of _underscore_,\n// though there are cases where that does not hold:\n//\n//     \"SSLError\".underscore.camelize // => \"SslError\"\ninflect.camelize = function(lower_case_and_underscored_word, first_letter_in_uppercase) {\n  var result;\n  if (first_letter_in_uppercase == null) first_letter_in_uppercase = true;\n  result = util.string.gsub(lower_case_and_underscored_word, /\\/(.?)/, function($) {\n    return \".\" + (util.string.upcase($[1]));\n  });\n  result = util.string.gsub(result, /(?:_)(.)/, function($) {\n    return util.string.upcase($[1]);\n  });\n  if (first_letter_in_uppercase) {\n    return util.string.upcase(result);\n  } else {\n    return util.string.downcase(result);\n  }\n};\n\n// Makes an underscored, lowercase form from the expression in the string.\n//\n// Changes '.' to '/' to convert namespaces to paths.\n//\n//     \"BulletRecord\".underscore()         // => \"bullet_record\"\n//     \"BulletRecord.Errors\".underscore()  // => \"bullet_record/errors\"\n//\n// As a rule of thumb you can think of +underscore+ as the inverse of +camelize+,\n// though there are cases where that does not hold:\n//\n//     \"SSLError\".underscore().camelize() // => \"SslError\"\ninflect.underscore = function (camel_cased_word) {\n  var self;\n  self = util.string.gsub(camel_cased_word, /\\./, '/');\n  self = util.string.gsub(self, /([A-Z]+)([A-Z][a-z])/, \"$1_$2\");\n  self = util.string.gsub(self, /([a-z\\d])([A-Z])/, \"$1_$2\");\n  self = util.string.gsub(self, /-/, '_');\n  return self.toLowerCase();\n};\n\n// Replaces underscores with dashes in the string.\n//\n//     \"puni_puni\".dasherize()   // => \"puni-puni\"\ninflect.dasherize = function (underscored_word) {\n  return util.string.gsub(underscored_word, /_/, '-');\n};\n\n// Removes the module part from the expression in the string.\n//\n//     \"BulletRecord.String.Inflections\".demodulize() // => \"Inflections\"\n//     \"Inflections\".demodulize()                     // => \"Inflections\"\ninflect.demodulize = function (class_name_in_module) {\n  return util.string.gsub(class_name_in_module, /^.*\\./, '');\n};\n\n// Creates a foreign key name from a class name.\n// _separate_class_name_and_id_with_underscore_ sets whether\n// the method should put '_' between the name and 'id'.\n//\n//     \"Message\".foreign_key()      // => \"message_id\"\n//     \"Message\".foreign_key(false) // => \"messageid\"\n//     \"Admin::Post\".foreign_key()  // => \"post_id\"\ninflect.foreign_key = function (class_name, separate_class_name_and_id_with_underscore) {\n  if (separate_class_name_and_id_with_underscore == null) {\n    separate_class_name_and_id_with_underscore = true;\n  }\n  return inflect.underscore(inflect.demodulize(class_name)) + (separate_class_name_and_id_with_underscore ? \"_id\" : \"id\");\n};\n\n// Turns a number into an ordinal string used to denote the position in an\n// ordered sequence such as 1st, 2nd, 3rd, 4th.\n//\n//     ordinalize(1)     // => \"1st\"\n//     ordinalize(2)     // => \"2nd\"\n//     ordinalize(1002)  // => \"1002nd\"\n//     ordinalize(1003)  // => \"1003rd\"\n//     ordinalize(-11)   // => \"-11th\"\n//     ordinalize(-1021) // => \"-1021st\"\ninflect.ordinalize = function (number) {\n  var _ref;\n  number = parseInt(number);\n  if ((_ref = Math.abs(number) % 100) === 11 || _ref === 12 || _ref === 13) {\n    return \"\" + number + \"th\";\n  } else {\n    switch (Math.abs(number) % 10) {\n      case 1:\n        return \"\" + number + \"st\";\n      case 2:\n        return \"\" + number + \"nd\";\n      case 3:\n        return \"\" + number + \"rd\";\n      default:\n        return \"\" + number + \"th\";\n    }\n  }\n};\n\n// Checks a given word for uncountability\n//\n//     \"money\".uncountability()     // => true\n//     \"my money\".uncountability()  // => true\ninflect.uncountability = function (word) {\n  return inflect.inflections.uncountables.some(function(ele, ind, arr) {\n    return word.match(new RegExp(\"(\\\\b|_)\" + ele + \"$\", 'i')) != null;\n  });\n};\n\n// Returns the plural form of the word in the string.\n//\n//     \"post\".pluralize()             // => \"posts\"\n//     \"octopus\".pluralize()          // => \"octopi\"\n//     \"sheep\".pluralize()            // => \"sheep\"\n//     \"words\".pluralize()            // => \"words\"\n//     \"CamelOctopus\".pluralize()     // => \"CamelOctopi\"\ninflect.pluralize = function (word) {\n  var plural, result;\n  result = word;\n  if (word === '' || inflect.uncountability(word)) {\n    return result;\n  } else {\n    for (var i = 0; i < inflect.inflections.plurals.length; i++) {\n      plural = inflect.inflections.plurals[i];\n      result = util.string.gsub(result, plural[0], plural[1]);\n      if (word.match(plural[0]) != null) break;\n    }\n    return result;\n  }\n};\n\n// The reverse of _pluralize_, returns the singular form of a word in a string.\n//\n//     \"posts\".singularize()            // => \"post\"\n//     \"octopi\".singularize()           // => \"octopus\"\n//     \"sheep\".singularize()            // => \"sheep\"\n//     \"word\".singularize()             // => \"word\"\n//     \"CamelOctopi\".singularize()      // => \"CamelOctopus\"\ninflect.singularize = function (word) {\n  var result, singular;\n  result = word;\n  if (word === '' || inflect.uncountability(word)) {\n    return result;\n  } else {\n    for (var i = 0; i < inflect.inflections.singulars.length; i++) {\n      singular = inflect.inflections.singulars[i];\n      result = util.string.gsub(result, singular[0], singular[1]);\n      if (word.match(singular[0])) break;\n    }\n    return result;\n  }\n};\n\n// Capitalizes the first word and turns underscores into spaces and strips a\n// trailing \"_id\", if any. Like _titleize_, this is meant for creating pretty output.\n//\n//     \"employee_salary\".humanize()   // => \"Employee salary\"\n//     \"author_id\".humanize()         // => \"Author\"\ninflect.humanize = function (lower_case_and_underscored_word) {\n  var human, result;\n  result = lower_case_and_underscored_word;\n  for (var i = 0; i < inflect.inflections.humans.length; i++) {\n    human = inflect.inflections.humans[i];\n    result = util.string.gsub(result, human[0], human[1]);\n  }\n  result = util.string.gsub(result, /_id$/, \"\");\n  result = util.string.gsub(result, /_/, \" \");\n  return util.string.capitalize(result, true);\n};\n\n// Capitalizes all the words and replaces some characters in the string to create\n// a nicer looking title. _titleize_ is meant for creating pretty output. It is not\n// used in the Bullet internals.\n//\n//\n//     \"man from the boondocks\".titleize()   // => \"Man From The Boondocks\"\n//     \"x-men: the last stand\".titleize()    // => \"X Men: The Last Stand\"\ninflect.titleize = function (word) {\n  var self;\n  self = inflect.humanize(inflect.underscore(word));\n  return util.string.capitalize(self);\n};\n\n// Create the name of a table like Bullet does for models to table names. This method\n// uses the _pluralize_ method on the last word in the string.\n//\n//     \"RawScaledScorer\".tableize()   // => \"raw_scaled_scorers\"\n//     \"egg_and_ham\".tableize()       // => \"egg_and_hams\"\n//     \"fancyCategory\".tableize()     // => \"fancy_categories\"\ninflect.tableize = function (class_name) {\n  return inflect.pluralize(inflect.underscore(class_name));\n};\n\n// Create a class name from a plural table name like Bullet does for table names to models.\n// Note that this returns a string and not a Class.\n//\n//     \"egg_and_hams\".classify()   // => \"EggAndHam\"\n//     \"posts\".classify()          // => \"Post\"\n//\n// Singular names are not handled correctly:\n//\n//     \"business\".classify()       // => \"Busines\"\ninflect.classify = function (table_name) {\n  return inflect.camelize(inflect.singularize(util.string.gsub(table_name, /.*\\./, '')));\n}\n", "// Some utility functions in js\n\nvar u = module.exports = {\n  array: {\n    // Returns a copy of the array with the value removed once\n    //\n    //     [1, 2, 3, 1].del 1 #=> [2, 3, 1]\n    //     [1, 2, 3].del 4    #=> [1, 2, 3]\n    del: function (arr, val) {\n      var index = arr.indexOf(val);\n\n      if (index != -1) {\n        if (index == 0) {\n         return arr.slice(1)\n        } else {\n          return arr.slice(0, index).concat(arr.slice(index+1));\n        }\n      } else {\n        return arr;\n      }\n    },\n\n    // Returns the first element of the array\n    //\n    //     [1, 2, 3].first() #=> 1\n    first: function(arr) {\n      return arr[0];\n    },\n\n    // Returns the last element of the array\n    //\n    //     [1, 2, 3].last()  #=> 3\n    last: function(arr) {\n      return arr[arr.length-1];\n    }\n  },\n  string: {\n    // Returns a copy of str with all occurrences of pattern replaced with either replacement or the return value of a function.\n    // The pattern will typically be a Regexp; if it is a String then no regular expression metacharacters will be interpreted\n    // (that is /\\d/ will match a digit, but ‘\\d’ will match a backslash followed by a ‘d’).\n    //\n    // In the function form, the current match object is passed in as a parameter to the function, and variables such as\n    // $[1], $[2], $[3] (where $ is the match object) will be set appropriately. The value returned by the function will be\n    // substituted for the match on each call.\n    //\n    // The result inherits any tainting in the original string or any supplied replacement string.\n    //\n    //     \"hello\".gsub /[aeiou]/, '*'      #=> \"h*ll*\"\n    //     \"hello\".gsub /[aeiou]/, '<$1>'   #=> \"h<e>ll<o>\"\n    //     \"hello\".gsub /[aeiou]/, ($) {\n    //       \"<#{$[1]}>\"                    #=> \"h<e>ll<o>\"\n    //\n    gsub: function (str, pattern, replacement) {\n      var i, match, matchCmpr, matchCmprPrev, replacementStr, result, self;\n      if (!((pattern != null) && (replacement != null))) return u.string.value(str);\n      result = '';\n      self = str;\n      while (self.length > 0) {\n        if ((match = self.match(pattern))) {\n          result += self.slice(0, match.index);\n          if (typeof replacement === 'function') {\n            match[1] = match[1] || match[0];\n            result += replacement(match);\n          } else if (replacement.match(/\\$[1-9]/)) {\n            matchCmprPrev = match;\n            matchCmpr = u.array.del(match, void 0);\n            while (matchCmpr !== matchCmprPrev) {\n              matchCmprPrev = matchCmpr;\n              matchCmpr = u.array.del(matchCmpr, void 0);\n            }\n            match[1] = match[1] || match[0];\n            replacementStr = replacement;\n            for (i = 1; i <= 9; i++) {\n              if (matchCmpr[i]) {\n                replacementStr = u.string.gsub(replacementStr, new RegExp(\"\\\\\\$\" + i), matchCmpr[i]);\n              }\n            }\n            result += replacementStr;\n          } else {\n            result += replacement;\n          }\n          self = self.slice(match.index + match[0].length);\n        } else {\n          result += self;\n          self = '';\n        }\n      }\n      return result;\n    },\n\n    // Returns a copy of the String with the first letter being upper case\n    //\n    //     \"hello\".upcase #=> \"Hello\"\n    upcase: function(str) {\n      var self = u.string.gsub(str, /_([a-z])/, function ($) {\n        return \"_\" + $[1].toUpperCase();\n      });\n\n      self = u.string.gsub(self, /\\/([a-z])/, function ($) {\n        return \"/\" + $[1].toUpperCase();\n      });\n\n      return self[0].toUpperCase() + self.substr(1);\n    },\n\n    // Returns a copy of capitalized string\n    //\n    //     \"employee salary\" #=> \"Employee Salary\"\n    capitalize: function (str, spaces) {\n      if (!str.length) {\n        return str;\n      }\n\n      var self = str.toLowerCase();\n\n      if (!spaces) {\n        self = u.string.gsub(self, /\\s([a-z])/, function ($) {\n          return \" \" + $[1].toUpperCase();\n        });\n      }\n\n      return self[0].toUpperCase() + self.substr(1);\n    },\n\n    // Returns a copy of the String with the first letter being lower case\n    //\n    //     \"HELLO\".downcase #=> \"hELLO\"\n    downcase: function(str) {\n      var self = u.string.gsub(str, /_([A-Z])/, function ($) {\n        return \"_\" + $[1].toLowerCase();\n      });\n\n      self = u.string.gsub(self, /\\/([A-Z])/, function ($) {\n        return \"/\" + $[1].toLowerCase();\n      });\n\n      return self[0].toLowerCase() + self.substr(1);\n    },\n\n    // Returns a string value for the String object\n    //\n    //     \"hello\".value() #=> \"hello\"\n    value: function (str) {\n      return str.substr(0);\n    }\n  }\n}\n", "// A singleton instance of this class is yielded by Inflector.inflections, which can then be used to specify additional\n// inflection rules. Examples:\n//\n//     BulletSupport.Inflector.inflect ($) ->\n//       $.plural /^(ox)$/i, '$1en'\n//       $.singular /^(ox)en/i, '$1'\n//\n//       $.irregular 'octopus', 'octopi'\n//\n//       $.uncountable \"equipment\"\n//\n// New rules are added at the top. So in the example above, the irregular rule for octopus will now be the first of the\n// pluralization and singularization rules that is runs. This guarantees that your rules run before any of the rules that may\n// already have been loaded.\n\nvar util = require('./util');\n\nvar Inflections = function () {\n  this.plurals = [];\n  this.singulars = [];\n  this.uncountables = [];\n  this.humans = [];\n  require('./defaults')(this);\n  return this;\n};\n\n// Specifies a new pluralization rule and its replacement. The rule can either be a string or a regular expression.\n// The replacement should always be a string that may include references to the matched data from the rule.\nInflections.prototype.plural = function (rule, replacement) {\n  if (typeof rule == 'string') {\n    this.uncountables = util.array.del(this.uncountables, rule);\n  }\n  this.uncountables = util.array.del(this.uncountables, replacement);\n  this.plurals.unshift([rule, replacement]);\n};\n\n// Specifies a new singularization rule and its replacement. The rule can either be a string or a regular expression.\n// The replacement should always be a string that may include references to the matched data from the rule.\nInflections.prototype.singular = function (rule, replacement) {\n  if (typeof rule == 'string') {\n    this.uncountables = util.array.del(this.uncountables, rule);\n  }\n  this.uncountables = util.array.del(this.uncountables, replacement);\n  this.singulars.unshift([rule, replacement]);\n};\n\n// Specifies a new irregular that applies to both pluralization and singularization at the same time. This can only be used\n// for strings, not regular expressions. You simply pass the irregular in singular and plural form.\n//\n//     irregular 'octopus', 'octopi'\n//     irregular 'person', 'people'\nInflections.prototype.irregular =  function (singular, plural, fullMatchRequired) {\n  this.uncountables = util.array.del(this.uncountables, singular);\n  this.uncountables = util.array.del(this.uncountables, plural);\n  var prefix = \"\";\n  if (fullMatchRequired) {\n    prefix = \"^\";\n  }\n  if (singular[0].toUpperCase() == plural[0].toUpperCase()) {\n    this.plural(new RegExp(\"(\" + prefix + singular[0] + \")\" + singular.slice(1) + \"$\", \"i\"), '$1' + plural.slice(1));\n    this.plural(new RegExp(\"(\" + prefix + plural[0] + \")\" + plural.slice(1) + \"$\", \"i\"), '$1' + plural.slice(1));\n    this.singular(new RegExp(\"(\" + prefix + plural[0] + \")\" + plural.slice(1) + \"$\", \"i\"), '$1' + singular.slice(1));\n  } else {\n    this.plural(new RegExp(prefix + (singular[0].toUpperCase()) + singular.slice(1) + \"$\"), plural[0].toUpperCase() + plural.slice(1));\n    this.plural(new RegExp(prefix + (singular[0].toLowerCase()) + singular.slice(1) + \"$\"), plural[0].toLowerCase() + plural.slice(1));\n    this.plural(new RegExp(prefix + (plural[0].toUpperCase()) + plural.slice(1) + \"$\"), plural[0].toUpperCase() + plural.slice(1));\n    this.plural(new RegExp(prefix + (plural[0].toLowerCase()) + plural.slice(1) + \"$\"), plural[0].toLowerCase() + plural.slice(1));\n    this.singular(new RegExp(prefix + (plural[0].toUpperCase()) + plural.slice(1) + \"$\"), singular[0].toUpperCase() + singular.slice(1));\n    this.singular(new RegExp(prefix + (plural[0].toLowerCase()) + plural.slice(1) + \"$\"), singular[0].toLowerCase() + singular.slice(1));\n  }\n};\n\n// Specifies a humanized form of a string by a regular expression rule or by a string mapping.\n// When using a regular expression based replacement, the normal humanize formatting is called after the replacement.\n// When a string is used, the human form should be specified as desired (example: 'The name', not 'the_name')\n//\n//     human /(.*)_cnt$/i, '$1_count'\n//     human \"legacy_col_person_name\", \"Name\"\nInflections.prototype.human = function (rule, replacement) {\n  this.humans.unshift([rule, replacement]);\n}\n\n// Add uncountable words that shouldn't be attempted inflected.\n//\n//     uncountable \"money\"\n//     uncountable [\"money\", \"information\"]\nInflections.prototype.uncountable = function (words) {\n  this.uncountables = this.uncountables.concat(words);\n}\n\n// Clears the loaded inflections within a given scope (default is _'all'_).\n// Give the scope as a symbol of the inflection type, the options are: _'plurals'_,\n// _'singulars'_, _'uncountables'_, _'humans'_.\n//\n//     clear 'all'\n//     clear 'plurals'\nInflections.prototype.clear = function (scope) {\n  if (scope == null) scope = 'all';\n  switch (scope) {\n    case 'all':\n      this.plurals = [];\n      this.singulars = [];\n      this.uncountables = [];\n      this.humans = [];\n    default:\n      this[scope] = [];\n  }\n}\n\n// Clears the loaded inflections and initializes them to [default](../inflections.html)\nInflections.prototype.default = function () {\n  this.plurals = [];\n  this.singulars = [];\n  this.uncountables = [];\n  this.humans = [];\n  require('./defaults')(this);\n  return this;\n};\n\nmodule.exports = new Inflections();\n", "// Default inflections\nmodule.exports = function (inflect) {\n\n  inflect.plural(/$/, 's');\n  inflect.plural(/s$/i, 's');\n  inflect.plural(/(ax|test)is$/i, '$1es');\n  inflect.plural(/(octop|vir)us$/i, '$1i');\n  inflect.plural(/(octop|vir)i$/i, '$1i');\n  inflect.plural(/(alias|status)$/i, '$1es');\n  inflect.plural(/(bu)s$/i, '$1ses');\n  inflect.plural(/(buffal|tomat)o$/i, '$1oes');\n  inflect.plural(/([ti])um$/i, '$1a');\n  inflect.plural(/([ti])a$/i, '$1a');\n  inflect.plural(/sis$/i, 'ses');\n  inflect.plural(/(?:([^fa])fe|(?:(oa)f)|([lr])f)$/i, '$1ves');\n  inflect.plural(/(hive)$/i, '$1s');\n  inflect.plural(/([^aeiouy]|qu)y$/i, '$1ies');\n  inflect.plural(/(x|ch|ss|sh)$/i, '$1es');\n  inflect.plural(/(matr|vert|ind)(?:ix|ex)$/i, '$1ices');\n  inflect.plural(/([m|l])ouse$/i, '$1ice');\n  inflect.plural(/([m|l])ice$/i, '$1ice');\n  inflect.plural(/^(ox)$/i, '$1en');\n  inflect.plural(/^(oxen)$/i, '$1');\n  inflect.plural(/(quiz)$/i, '$1zes');\n\n  inflect.singular(/s$/i, '');\n  inflect.singular(/(n)ews$/i, '$1ews');\n  inflect.singular(/([ti])a$/i, '$1um');\n  inflect.singular(/((a)naly|(b)a|(d)iagno|(p)arenthe|(p)rogno|(s)ynop|(t)he)ses$/i, '$1sis');\n  inflect.singular(/(^analy)ses$/i, '$1sis');\n  inflect.singular(/([^f])ves$/i, '$1fe');\n  inflect.singular(/(hive)s$/i, '$1');\n  inflect.singular(/(tive)s$/i, '$1');\n  inflect.singular(/(oave)s$/i, 'oaf');\n  inflect.singular(/([lr])ves$/i, '$1f');\n  inflect.singular(/([^aeiouy]|qu)ies$/i, '$1y');\n  inflect.singular(/(s)eries$/i, '$1eries');\n  inflect.singular(/(m)ovies$/i, '$1ovie');\n  inflect.singular(/(x|ch|ss|sh)es$/i, '$1');\n  inflect.singular(/([m|l])ice$/i, '$1ouse');\n  inflect.singular(/(bus)es$/i, '$1');\n  inflect.singular(/(o)es$/i, '$1');\n  inflect.singular(/(shoe)s$/i, '$1');\n  inflect.singular(/(cris|ax|test)es$/i, '$1is');\n  inflect.singular(/(octop|vir)i$/i, '$1us');\n  inflect.singular(/(alias|status)es$/i, '$1');\n  inflect.singular(/^(ox)en/i, '$1');\n  inflect.singular(/(vert|ind)ices$/i, '$1ex');\n  inflect.singular(/(matr)ices$/i, '$1ix');\n  inflect.singular(/(quiz)zes$/i, '$1');\n  inflect.singular(/(database)s$/i, '$1');\n\n  inflect.irregular('child', 'children');\n  inflect.irregular('person', 'people');\n  inflect.irregular('man', 'men');\n  inflect.irregular('child', 'children');\n  inflect.irregular('sex', 'sexes');\n  inflect.irregular('move', 'moves');\n  inflect.irregular('cow', 'kine');\n  inflect.irregular('zombie', 'zombies');\n  inflect.irregular('oaf', 'oafs', true);\n  inflect.irregular('jefe', 'jefes');\n  inflect.irregular('save', 'saves');\n  inflect.irregular('safe', 'safes');\n  inflect.irregular('fife', 'fifes');\n\n  inflect.uncountable(['equipment', 'information', 'rice', 'money', 'species', 'series', 'fish', 'sheep', 'jeans', 'sushi']);\n}\n", "module.exports = function (obj) {\n\n  var addProperty = function (method, func) {\n    String.prototype.__defineGetter__(method, func);\n  }\n\n  var stringPrototypeBlacklist = [\n    '__defineGetter__', '__defineSetter__', '__lookupGetter__', '__lookupSetter__', 'charAt', 'constructor',\n    'hasOwnProperty', 'isPrototypeOf', 'propertyIsEnumerable', 'toLocaleString', 'toString', 'valueOf', 'charCodeAt',\n    'indexOf', 'lastIndexof', 'length', 'localeCompare', 'match', 'replace', 'search', 'slice', 'split', 'substring',\n    'toLocaleLowerCase', 'toLocaleUpperCase', 'toLowerCase', 'toUpperCase', 'trim', 'trimLeft', 'trimRight', 'gsub'\n  ];\n\n  Object.keys(obj).forEach(function (key) {\n    if (key != 'inflect' && key != 'inflections') {\n      if (stringPrototypeBlacklist.indexOf(key) !== -1) {\n        console.log('warn: You should not override String.prototype.' + key);\n      } else {\n        addProperty(key, function () {\n          return obj[key](this);\n        });\n      }\n    }\n  });\n\n}\n"]}