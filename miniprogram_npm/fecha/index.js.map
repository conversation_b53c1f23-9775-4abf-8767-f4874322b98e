{"version": 3, "sources": ["fecha.umd.js"], "names": [], "mappings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file": "index.js", "sourcesContent": ["(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :\n  typeof define === 'function' && define.amd ? define(['exports'], factory) :\n  (factory((global.fecha = {})));\n}(this, (function (exports) { \n\n  var token = /d{1,4}|M{1,4}|YY(?:YY)?|S{1,3}|Do|ZZ|Z|([HhMsDm])\\1?|[aA]|\"[^\"]*\"|'[^']*'/g;\n  var twoDigitsOptional = \"[1-9]\\\\d?\";\n  var twoDigits = \"\\\\d\\\\d\";\n  var threeDigits = \"\\\\d{3}\";\n  var fourDigits = \"\\\\d{4}\";\n  var word = \"[^\\\\s]+\";\n  var literal = /\\[([^]*?)\\]/gm;\n  function shorten(arr, sLen) {\n      var newArr = [];\n      for (var i = 0, len = arr.length; i < len; i++) {\n          newArr.push(arr[i].substr(0, sLen));\n      }\n      return newArr;\n  }\n  var monthUpdate = function (arrName) { return function (v, i18n) {\n      var lowerCaseArr = i18n[arrName].map(function (v) { return v.toLowerCase(); });\n      var index = lowerCaseArr.indexOf(v.toLowerCase());\n      if (index > -1) {\n          return index;\n      }\n      return null;\n  }; };\n  function assign(origObj) {\n      var args = [];\n      for (var _i = 1; _i < arguments.length; _i++) {\n          args[_i - 1] = arguments[_i];\n      }\n      for (var _a = 0, args_1 = args; _a < args_1.length; _a++) {\n          var obj = args_1[_a];\n          for (var key in obj) {\n              // @ts-ignore ex\n              origObj[key] = obj[key];\n          }\n      }\n      return origObj;\n  }\n  var dayNames = [\n      \"Sunday\",\n      \"Monday\",\n      \"Tuesday\",\n      \"Wednesday\",\n      \"Thursday\",\n      \"Friday\",\n      \"Saturday\"\n  ];\n  var monthNames = [\n      \"January\",\n      \"February\",\n      \"March\",\n      \"April\",\n      \"May\",\n      \"June\",\n      \"July\",\n      \"August\",\n      \"September\",\n      \"October\",\n      \"November\",\n      \"December\"\n  ];\n  var monthNamesShort = shorten(monthNames, 3);\n  var dayNamesShort = shorten(dayNames, 3);\n  var defaultI18n = {\n      dayNamesShort: dayNamesShort,\n      dayNames: dayNames,\n      monthNamesShort: monthNamesShort,\n      monthNames: monthNames,\n      amPm: [\"am\", \"pm\"],\n      DoFn: function (dayOfMonth) {\n          return (dayOfMonth +\n              [\"th\", \"st\", \"nd\", \"rd\"][dayOfMonth % 10 > 3\n                  ? 0\n                  : ((dayOfMonth - (dayOfMonth % 10) !== 10 ? 1 : 0) * dayOfMonth) % 10]);\n      }\n  };\n  var globalI18n = assign({}, defaultI18n);\n  var setGlobalDateI18n = function (i18n) {\n      return (globalI18n = assign(globalI18n, i18n));\n  };\n  var regexEscape = function (str) {\n      return str.replace(/[|\\\\{()[^$+*?.-]/g, \"\\\\$&\");\n  };\n  var pad = function (val, len) {\n      if (len === void 0) { len = 2; }\n      val = String(val);\n      while (val.length < len) {\n          val = \"0\" + val;\n      }\n      return val;\n  };\n  var formatFlags = {\n      D: function (dateObj) { return String(dateObj.getDate()); },\n      DD: function (dateObj) { return pad(dateObj.getDate()); },\n      Do: function (dateObj, i18n) {\n          return i18n.DoFn(dateObj.getDate());\n      },\n      d: function (dateObj) { return String(dateObj.getDay()); },\n      dd: function (dateObj) { return pad(dateObj.getDay()); },\n      ddd: function (dateObj, i18n) {\n          return i18n.dayNamesShort[dateObj.getDay()];\n      },\n      dddd: function (dateObj, i18n) {\n          return i18n.dayNames[dateObj.getDay()];\n      },\n      M: function (dateObj) { return String(dateObj.getMonth() + 1); },\n      MM: function (dateObj) { return pad(dateObj.getMonth() + 1); },\n      MMM: function (dateObj, i18n) {\n          return i18n.monthNamesShort[dateObj.getMonth()];\n      },\n      MMMM: function (dateObj, i18n) {\n          return i18n.monthNames[dateObj.getMonth()];\n      },\n      YY: function (dateObj) {\n          return pad(String(dateObj.getFullYear()), 4).substr(2);\n      },\n      YYYY: function (dateObj) { return pad(dateObj.getFullYear(), 4); },\n      h: function (dateObj) { return String(dateObj.getHours() % 12 || 12); },\n      hh: function (dateObj) { return pad(dateObj.getHours() % 12 || 12); },\n      H: function (dateObj) { return String(dateObj.getHours()); },\n      HH: function (dateObj) { return pad(dateObj.getHours()); },\n      m: function (dateObj) { return String(dateObj.getMinutes()); },\n      mm: function (dateObj) { return pad(dateObj.getMinutes()); },\n      s: function (dateObj) { return String(dateObj.getSeconds()); },\n      ss: function (dateObj) { return pad(dateObj.getSeconds()); },\n      S: function (dateObj) {\n          return String(Math.round(dateObj.getMilliseconds() / 100));\n      },\n      SS: function (dateObj) {\n          return pad(Math.round(dateObj.getMilliseconds() / 10), 2);\n      },\n      SSS: function (dateObj) { return pad(dateObj.getMilliseconds(), 3); },\n      a: function (dateObj, i18n) {\n          return dateObj.getHours() < 12 ? i18n.amPm[0] : i18n.amPm[1];\n      },\n      A: function (dateObj, i18n) {\n          return dateObj.getHours() < 12\n              ? i18n.amPm[0].toUpperCase()\n              : i18n.amPm[1].toUpperCase();\n      },\n      ZZ: function (dateObj) {\n          var offset = dateObj.getTimezoneOffset();\n          return ((offset > 0 ? \"-\" : \"+\") +\n              pad(Math.floor(Math.abs(offset) / 60) * 100 + (Math.abs(offset) % 60), 4));\n      },\n      Z: function (dateObj) {\n          var offset = dateObj.getTimezoneOffset();\n          return ((offset > 0 ? \"-\" : \"+\") +\n              pad(Math.floor(Math.abs(offset) / 60), 2) +\n              \":\" +\n              pad(Math.abs(offset) % 60, 2));\n      }\n  };\n  var monthParse = function (v) { return +v - 1; };\n  var emptyDigits = [null, twoDigitsOptional];\n  var emptyWord = [null, word];\n  var amPm = [\n      \"isPm\",\n      word,\n      function (v, i18n) {\n          var val = v.toLowerCase();\n          if (val === i18n.amPm[0]) {\n              return 0;\n          }\n          else if (val === i18n.amPm[1]) {\n              return 1;\n          }\n          return null;\n      }\n  ];\n  var timezoneOffset = [\n      \"timezoneOffset\",\n      \"[^\\\\s]*?[\\\\+\\\\-]\\\\d\\\\d:?\\\\d\\\\d|[^\\\\s]*?Z?\",\n      function (v) {\n          var parts = (v + \"\").match(/([+-]|\\d\\d)/gi);\n          if (parts) {\n              var minutes = +parts[1] * 60 + parseInt(parts[2], 10);\n              return parts[0] === \"+\" ? minutes : -minutes;\n          }\n          return 0;\n      }\n  ];\n  var parseFlags = {\n      D: [\"day\", twoDigitsOptional],\n      DD: [\"day\", twoDigits],\n      Do: [\"day\", twoDigitsOptional + word, function (v) { return parseInt(v, 10); }],\n      M: [\"month\", twoDigitsOptional, monthParse],\n      MM: [\"month\", twoDigits, monthParse],\n      YY: [\n          \"year\",\n          twoDigits,\n          function (v) {\n              var now = new Date();\n              var cent = +(\"\" + now.getFullYear()).substr(0, 2);\n              return +(\"\" + (+v > 68 ? cent - 1 : cent) + v);\n          }\n      ],\n      h: [\"hour\", twoDigitsOptional, undefined, \"isPm\"],\n      hh: [\"hour\", twoDigits, undefined, \"isPm\"],\n      H: [\"hour\", twoDigitsOptional],\n      HH: [\"hour\", twoDigits],\n      m: [\"minute\", twoDigitsOptional],\n      mm: [\"minute\", twoDigits],\n      s: [\"second\", twoDigitsOptional],\n      ss: [\"second\", twoDigits],\n      YYYY: [\"year\", fourDigits],\n      S: [\"millisecond\", \"\\\\d\", function (v) { return +v * 100; }],\n      SS: [\"millisecond\", twoDigits, function (v) { return +v * 10; }],\n      SSS: [\"millisecond\", threeDigits],\n      d: emptyDigits,\n      dd: emptyDigits,\n      ddd: emptyWord,\n      dddd: emptyWord,\n      MMM: [\"month\", word, monthUpdate(\"monthNamesShort\")],\n      MMMM: [\"month\", word, monthUpdate(\"monthNames\")],\n      a: amPm,\n      A: amPm,\n      ZZ: timezoneOffset,\n      Z: timezoneOffset\n  };\n  // Some common format strings\n  var globalMasks = {\n      default: \"ddd MMM DD YYYY HH:mm:ss\",\n      shortDate: \"M/D/YY\",\n      mediumDate: \"MMM D, YYYY\",\n      longDate: \"MMMM D, YYYY\",\n      fullDate: \"dddd, MMMM D, YYYY\",\n      isoDate: \"YYYY-MM-DD\",\n      isoDateTime: \"YYYY-MM-DDTHH:mm:ssZ\",\n      shortTime: \"HH:mm\",\n      mediumTime: \"HH:mm:ss\",\n      longTime: \"HH:mm:ss.SSS\"\n  };\n  var setGlobalDateMasks = function (masks) { return assign(globalMasks, masks); };\n  /***\n   * Format a date\n   * @method format\n   * @param {Date|number} dateObj\n   * @param {string} mask Format of the date, i.e. 'mm-dd-yy' or 'shortDate'\n   * @returns {string} Formatted date string\n   */\n  var format = function (dateObj, mask, i18n) {\n      if (mask === void 0) { mask = globalMasks[\"default\"]; }\n      if (i18n === void 0) { i18n = {}; }\n      if (typeof dateObj === \"number\") {\n          dateObj = new Date(dateObj);\n      }\n      if (Object.prototype.toString.call(dateObj) !== \"[object Date]\" ||\n          isNaN(dateObj.getTime())) {\n          throw new Error(\"Invalid Date pass to format\");\n      }\n      mask = globalMasks[mask] || mask;\n      var literals = [];\n      // Make literals inactive by replacing them with @@@\n      mask = mask.replace(literal, function ($0, $1) {\n          literals.push($1);\n          return \"@@@\";\n      });\n      var combinedI18nSettings = assign(assign({}, globalI18n), i18n);\n      // Apply formatting rules\n      mask = mask.replace(token, function ($0) {\n          return formatFlags[$0](dateObj, combinedI18nSettings);\n      });\n      // Inline literal values back into the formatted value\n      return mask.replace(/@@@/g, function () { return literals.shift(); });\n  };\n  /**\n   * Parse a date string into a Javascript Date object /\n   * @method parse\n   * @param {string} dateStr Date string\n   * @param {string} format Date parse format\n   * @param {i18n} I18nSettingsOptional Full or subset of I18N settings\n   * @returns {Date|null} Returns Date object. Returns null what date string is invalid or doesn't match format\n   */\n  function parse(dateStr, format, i18n) {\n      if (i18n === void 0) { i18n = {}; }\n      if (typeof format !== \"string\") {\n          throw new Error(\"Invalid format in fecha parse\");\n      }\n      // Check to see if the format is actually a mask\n      format = globalMasks[format] || format;\n      // Avoid regular expression denial of service, fail early for really long strings\n      // https://www.owasp.org/index.php/Regular_expression_Denial_of_Service_-_ReDoS\n      if (dateStr.length > 1000) {\n          return null;\n      }\n      // Default to the beginning of the year.\n      var today = new Date();\n      var dateInfo = {\n          year: today.getFullYear(),\n          month: 0,\n          day: 1,\n          hour: 0,\n          minute: 0,\n          second: 0,\n          millisecond: 0,\n          isPm: null,\n          timezoneOffset: null\n      };\n      var parseInfo = [];\n      var literals = [];\n      // Replace all the literals with @@@. Hopefully a string that won't exist in the format\n      var newFormat = format.replace(literal, function ($0, $1) {\n          literals.push(regexEscape($1));\n          return \"@@@\";\n      });\n      var specifiedFields = {};\n      var requiredFields = {};\n      // Change every token that we find into the correct regex\n      newFormat = regexEscape(newFormat).replace(token, function ($0) {\n          var info = parseFlags[$0];\n          var field = info[0], regex = info[1], requiredField = info[3];\n          // Check if the person has specified the same field twice. This will lead to confusing results.\n          if (specifiedFields[field]) {\n              throw new Error(\"Invalid format. \" + field + \" specified twice in format\");\n          }\n          specifiedFields[field] = true;\n          // Check if there are any required fields. For instance, 12 hour time requires AM/PM specified\n          if (requiredField) {\n              requiredFields[requiredField] = true;\n          }\n          parseInfo.push(info);\n          return \"(\" + regex + \")\";\n      });\n      // Check all the required fields are present\n      Object.keys(requiredFields).forEach(function (field) {\n          if (!specifiedFields[field]) {\n              throw new Error(\"Invalid format. \" + field + \" is required in specified format\");\n          }\n      });\n      // Add back all the literals after\n      newFormat = newFormat.replace(/@@@/g, function () { return literals.shift(); });\n      // Check if the date string matches the format. If it doesn't return null\n      var matches = dateStr.match(new RegExp(newFormat, \"i\"));\n      if (!matches) {\n          return null;\n      }\n      var combinedI18nSettings = assign(assign({}, globalI18n), i18n);\n      // For each match, call the parser function for that date part\n      for (var i = 1; i < matches.length; i++) {\n          var _a = parseInfo[i - 1], field = _a[0], parser = _a[2];\n          var value = parser\n              ? parser(matches[i], combinedI18nSettings)\n              : +matches[i];\n          // If the parser can't make sense of the value, return null\n          if (value == null) {\n              return null;\n          }\n          dateInfo[field] = value;\n      }\n      if (dateInfo.isPm === 1 && dateInfo.hour != null && +dateInfo.hour !== 12) {\n          dateInfo.hour = +dateInfo.hour + 12;\n      }\n      else if (dateInfo.isPm === 0 && +dateInfo.hour === 12) {\n          dateInfo.hour = 0;\n      }\n      var dateWithoutTZ = new Date(dateInfo.year, dateInfo.month, dateInfo.day, dateInfo.hour, dateInfo.minute, dateInfo.second, dateInfo.millisecond);\n      var validateFields = [\n          [\"month\", \"getMonth\"],\n          [\"day\", \"getDate\"],\n          [\"hour\", \"getHours\"],\n          [\"minute\", \"getMinutes\"],\n          [\"second\", \"getSeconds\"]\n      ];\n      for (var i = 0, len = validateFields.length; i < len; i++) {\n          // Check to make sure the date field is within the allowed range. Javascript dates allows values\n          // outside the allowed range. If the values don't match the value was invalid\n          if (specifiedFields[validateFields[i][0]] &&\n              dateInfo[validateFields[i][0]] !== dateWithoutTZ[validateFields[i][1]]()) {\n              return null;\n          }\n      }\n      if (dateInfo.timezoneOffset == null) {\n          return dateWithoutTZ;\n      }\n      return new Date(Date.UTC(dateInfo.year, dateInfo.month, dateInfo.day, dateInfo.hour, dateInfo.minute - dateInfo.timezoneOffset, dateInfo.second, dateInfo.millisecond));\n  }\n  var fecha = {\n      format: format,\n      parse: parse,\n      defaultI18n: defaultI18n,\n      setGlobalDateI18n: setGlobalDateI18n,\n      setGlobalDateMasks: setGlobalDateMasks\n  };\n\n  exports.assign = assign;\n  exports.default = fecha;\n  exports.format = format;\n  exports.parse = parse;\n  exports.defaultI18n = defaultI18n;\n  exports.setGlobalDateI18n = setGlobalDateI18n;\n  exports.setGlobalDateMasks = setGlobalDateMasks;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n})));\n//# sourceMappingURL=fecha.umd.js.map\n"]}