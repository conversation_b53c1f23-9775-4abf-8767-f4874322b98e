<wxs src="./badge.wxs" module="this" />

<view class="{{this.getBadgeOuterClass({shape})}} t-class">
  <view class="{{classPrefix}}__content t-class-content">
    <slot wx:if="{{!content}}" class="{{classPrefix}}__content-slot" />
    <text wx:else class="{{classPrefix}}__content-text">{{content}}</text>
  </view>
  <view
    wx:if="{{count !== 'slot' && this.isShowBadge({dot,count,showZero})}}"
    class="{{this.getBadgeInnerClass({dot, size, shape, count})}} t-has-count t-class-count"
    style="{{this.getBadgeStyles({color, offset})}}"
    >{{ this.getBadgeValue({dot, count, maxCount}) }}
  </view>
  <slot name="count" wx:if="{{count === 'slot' || !count}}" />
</view>
