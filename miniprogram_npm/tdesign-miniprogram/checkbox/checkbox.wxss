.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-checkbox {
  display: flex;
  flex-direction: row;
  font-size: 32rpx;
  padding: 32rpx 32rpx;
  position: relative;
  background: white;
}
.t-checkbox--right {
  flex-direction: row-reverse;
}
.t-checkbox .limit-title-row {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.t-checkbox .image-center {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.t-checkbox__icon-left {
  margin-right: 20rpx;
  width: 40rpx;
}
.t-checkbox__icon-right {
  right: 0px;
  display: contents;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.t-checkbox__icon-image {
  width: 48rpx;
  height: 48rpx;
  vertical-align: sub;
}
.t-checkbox__icon {
  line-height: 48rpx;
}
.t-checkbox__btn {
  font-size: 48rpx;
  display: block;
  line-height: 40rpx;
  color: #dcdcdc;
  width: 48rpx;
}
.t-checkbox__content {
  flex: 1;
  line-height: 48rpx;
  margin-right: 10px;
}
.t-checkbox__title {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.t-checkbox__description {
  color: rgba(0, 0, 0, 0.4);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 28rpx;
  line-height: 44rpx;
}
.t-checkbox__btn.t-is-checked {
  color: #0052d9;
}
.t-checkbox__btn.t-is-disabled {
  cursor: not-allowed;
  color: #dcdcdc;
}
.t-checkbox__content.t-is-disabled {
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.26);
}
.t-checkbox__border {
  position: absolute;
  bottom: 0;
  border-top: 1rpx solid #e7e7e7;
  width: 100%;
}
.t-checkbox__border--left {
  left: 80rpx;
  width: calc(100% - 80rpx);
}
.t-checkbox__border--right {
  right: 80rpx;
  width: calc(100% - 80rpx);
}
.t-checkbox--tag {
  font-size: 28rpx;
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  text-align: center;
  background-color: #f3f3f3;
  border-radius: 8rpx;
}
.t-checkbox--tag.t-is-actived {
  color: #0052d9;
  background-color: #ecf2fe;
}
.t-checkbox--tag .t-checkbox__content {
  margin-right: 0;
}
