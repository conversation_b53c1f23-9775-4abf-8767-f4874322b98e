<view
  class="{{classPrefix}} {{prefix}}-class {{classPrefix}}--{{align}} {{classPrefix}}--{{theme}} {{checked ? prefix + '-is-actived' : ''}}"
>
  <!-- icon -->
  <view
    wx:if="{{theme == 'default'}}"
    class="{{classPrefix}}__icon-{{align}} {{prefix}}-class-icon"
    data-target="icon"
    bind:tap="onChange"
  >
    <block wx:if="{{icon.length > 0}}">
      <view class="{{classPrefix}}__icon">
        <image src="{{checked ? icon[0] : icon[1]}}" class="{{classPrefix}}__icon-image" webp />
      </view>
    </block>
    <block wx:else>
      <t-icon
        color="{{checked && !disabled ? color : ''}}"
        name="{{checked ? (indeterminate ? 'minus-circle-filled' : 'check-circle-filled') : 'circle'}}"
        class="{{classPrefix}}__btn {{checked ? prefix + '-is-checked' : ''}} {{disabled ? prefix + '-is-disabled' : ''}}"
      />
    </block>
  </view>
  <!-- 文本内容 -->
  <view
    class="{{classPrefix}}__content {{disabled ? prefix + '-is-disabled' : ''}}"
    data-target="text"
    bind:tap="onChange"
  >
    <!-- title -->
    <view class="{{classPrefix}}__title {{prefix}}-class-label" style="-webkit-line-clamp:{{maxLabelRow}}">
      {{label}}
      <slot />
      <slot name="label" />
    </view>
    <!-- content -->
    <view class="{{classPrefix}}__description {{prefix}}-class-content " style="-webkit-line-clamp:{{maxContentRow}}">
      {{content}}
      <slot name="content" />
    </view>
  </view>
  <!-- 内置下边框 -->
  <view
    wx:if="{{theme == 'default' && !borderless}}"
    class="{{classPrefix}}__border {{classPrefix}}__border--{{align}} {{prefix}}-class-border"
  />
</view>
