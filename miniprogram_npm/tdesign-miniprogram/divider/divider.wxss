.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
page {
  --td-divider-color: #e7e7e7;
  --td-divider-content-color: rgba(0, 0, 0, 0.4);
}
.t-divider--horizontal {
  display: flex;
  align-items: center;
  color: var(--td-divider-color);
  border-color: var(--td-divider-color);
  border-style: solid;
  border-width: 0;
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}
.t-divider--horizontal::before,
.t-divider--horizontal::after {
  display: block;
  flex: 1;
  box-sizing: border-box;
  border-color: inherit;
  border-style: inherit;
  border-width: 1rpx 0 0;
}
.t-divider--horizontal::before {
  content: '';
}
.t-divider--horizontal .t-divider__content:not(:empty) {
  margin: 0 24rpx;
}
.t-divider--vertical {
  display: flex;
  flex-direction: column;
  height: 1em;
  margin: 0 16rpx;
  color: var(--td-divider-color);
  border-color: var(--td-divider-color);
  border-style: solid;
  border-width: 0;
}
.t-divider--vertical::before,
.t-divider--vertical::after {
  content: '';
  display: block;
  flex: 1;
  box-sizing: border-box;
  border-color: inherit;
  border-style: inherit;
  border-width: 0 1rpx 0 0;
}
.t-divider--vertical-center {
  display: flex;
  align-items: center;
  height: 100%;
}
.t-divider--dashed {
  border-style: dashed;
}
.t-divider__content {
  font-size: 24rpx;
  line-height: 40rpx;
  color: var(--td-divider-content-color);
}
.t-divider--center::after,
.t-divider--left::after,
.t-divider--right::after {
  content: '';
}
.t-divider--left::before {
  max-width: 60rpx;
}
.t-divider--right::after {
  max-width: 60rpx;
}
