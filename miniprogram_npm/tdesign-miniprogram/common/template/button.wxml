<template name="button">
  <t-button
    block="{{block}}"
    class="{{class}}"
    t-class="{{externalClass}}"
    disabled="{{disabled}}"
    data-type="{{type}}"
    icon="{{icon}}"
    loading="{{loading}}"
    theme="{{theme}}"
    shape="{{shape}}"
    size="{{size}}"
    variant="{{variant}}"
    open-type="{{openType}}"
    hover-stop-propagation="{{hoverStopPropagation}}"
    hover-start-time="{{hoverStartTime}}"
    hover-stay-time="{{hoverStayTime}}"
    lang="{{lang}}"
    session-from="{{sessionFrom}}"
    send-message-title="{{sendMessageTitle}}"
    send-message-path="{{sendMessagePath}}"
    send-message-img="{{sendMessageImg}}"
    app-parameter="{{appParameter}}"
    show-message-card="{{showMessageCard}}"
    bind:tap="onTplButtonTap"
    bind:getuserinfo="onTplButtonTap"
    bind:contact="onTplButtonTap"
    bind:getphonenumber="onTplButtonTap"
    bind:error="onTplButtonTap"
    bind:opensetting="onTplButtonTap"
    bind:launchapp="onTplButtonTap"
    >{{content}}</t-button
  >
</template>
