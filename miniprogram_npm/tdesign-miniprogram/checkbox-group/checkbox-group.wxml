<view class="{{ classPrefix }} {{prefix}}-class" style="{{customStyle}}">
  <slot />
  <block wx:for="{{checkboxOptions}}" wx:key="value">
    <t-checkbox
      class="{{prefix}}-checkbox-option"
      label="{{item.label || item.text || ''}}"
      value="{{item.value || ''}}"
      check-all="{{item.checkAll}}"
      disabled="{{item.disabled}}"
      data-item="{{item}}"
      bind:change="handleInnerChildChange"
    ></t-checkbox>
  </block>
</view>
