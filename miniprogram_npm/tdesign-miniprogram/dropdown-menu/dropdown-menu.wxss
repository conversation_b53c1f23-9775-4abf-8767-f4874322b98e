.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
/** dropdwon-menu */
.t-dropdown-menu__bar {
  position: relative;
  display: flex;
  height: 48px;
  background: #fff;
}
.t-dropdown-menu__bar::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  left: 0;
  bottom: 0;
  border-bottom: 1px solid #e6e6e6;
  transform: scaleY(0.5);
}
.t-dropdown-menu__item {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
  position: relative;
  overflow: hidden;
  color: #000000;
}
.t-dropdown-menu__item.t-is-active {
  color: #0052d9;
}
.t-dropdown-menu__item.t-is-disabled .t-dropdown-menu__icon,
.t-dropdown-menu__item.t-is-disabled .t-dropdown-menu__title {
  color: #bbbbbb;
}
.t-dropdown-menu__icon {
  transition: transform 240ms ease;
}
.t-dropdown-menu__icon--active {
  transform: rotate(180deg);
}
.t-dropdown-menu__title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 32rpx;
}
