<wxs module="_" src="./util.wxs" />

<view wx:if="{{wrapperVisible}}" class="{{classPrefix}}" style="top:{{top}}px; z-index: {{zIndex + 1}}">
  <t-popup
    visible="{{show}}"
    z-index="{{zIndex + 1}}"
    duration="{{duration}}"
    show-overlay="{{showOverlay}}"
    custom-style="position: absolute;"
    overlayProps="{{ { customStyle: 'position: absolute' } }}"
    bind:leaved="onLeaved"
    bind:visible-change="handleMaskClick"
    class="{{classPrefix}}__popup-host"
    t-class-content="{{contentClasses}} {{classPrefix}}__content"
  >
    <view class="{{classPrefix}}__body">
      <block wx:if="{{optionsLayout === 'columns'}}">
        <!-- 单选列表 -->
        <t-cell-group wx:if="{{!multiple}}">
          <t-radio-group class="{{classPrefix}}__radio" value="{{value}}" bind:change="handleRadioChange">
            <t-radio
              wx:for="{{options}}"
              wx:key="index"
              class="{{classPrefix}}__radio-item"
              icon="stroke-line"
              align="right"
              t-class="radio"
              t-class-label="radio__label radio__label--{{value == item[valueAlias] ? 'active' : ''}}"
              value="{{item[valueAlias]}}"
              label="{{item[labelAlias]}}"
              disabled="{{item.disabled}}"
            ></t-radio>
          </t-radio-group>
        </t-cell-group>
        <!-- 多选列表 -->
        <block wx:else>
          <t-checkbox-group
            class="{{classPrefix}}__checkbox"
            t-class="{{classPrefix}}__checkbox-group"
            custom-style="grid-template-columns: repeat({{optionsColumns}}, 1fr)"
            value="{{value}}"
            bind:change="handleRadioChange"
          >
            <block wx:for="{{options}}" wx:key="index">
              <t-checkbox
                class="{{classPrefix}}__checkbox-item"
                theme="tag"
                value="{{item[valueAlias]}}"
                label="{{item[labelAlias]}}"
                disabled="{{item.disabled}}"
              ></t-checkbox>
            </block>
          </t-checkbox-group>
        </block>
      </block>
      <block wx:elif="{{optionsLayout === 'tree'}}">
        <scroll-view
          scroll-y
          class="{{classPrefix}}__column {{classPrefix}}__column--{{_.getTreeClass(leafLevel - level)}}"
          wx:for="{{treeOptions}}"
          wx:key="level"
          wx:for-index="level"
        >
          <block wx:if="{{level < leafLevel}}">
            <view
              wx:for="{{treeOptions[level]}}"
              wx:key="index"
              bind:tap="handleTreeClick"
              data-level="{{level}}"
              data-value="{{item[valueAlias]}}"
              class="{{classPrefix}}__tree-item {{item[valueAlias] === value[level] ? classPrefix + '__tree-item--active' : '' }}"
            >
              {{item[labelAlias]}}
            </view>
          </block>

          <block wx:else>
            <block wx:if="{{!multiple}}">
              <t-radio-group
                class="{{classPrefix}}__radio"
                data-level="{{level}}"
                value="{{value[level]}}"
                bind:change="handleRadioChange"
              >
                <t-radio
                  wx:for="{{treeOptions[level]}}"
                  wx:key="value"
                  class="{{classPrefix}}__radio-item"
                  t-class="radio"
                  t-class-label="radio__label radio__label--{{value[level] === item[valueAlias] ? 'active' : ''}}"
                  value="{{item[valueAlias]}}"
                  borderless
                  align="right"
                  >{{item[labelAlias]}}</t-radio
                >
              </t-radio-group>
            </block>
            <block wx:elif="{{multiple}}">
              <t-checkbox-group
                class="{{classPrefix}}__checkbox"
                value="{{value[level]}}"
                bind:change="handleRadioChange"
                data-level="{{level}}"
              >
                <t-checkbox
                  wx:for="{{treeOptions[level]}}"
                  wx:key="value"
                  align="right"
                  t-class="radio"
                  t-class-label="radio__label radio__label--{{value[level] === item[valueAlias] ? 'active' : ''}}"
                  borderless
                  value="{{item[valueAlias]}}"
                  >{{item[labelAlias]}}</t-checkbox
                >
              </t-checkbox-group>
            </block>
          </block>
        </scroll-view>
      </block>

      <slot />
    </view>
    <view class="{{classPrefix}}__foot" wx:if="{{multiple || optionsLayout === 'tree'}}">
      <t-button-group>
        <t-button
          block
          size="large"
          class="{{classPrefix}}__reset-btn"
          t-class="button button--reset"
          theme="default"
          disabled="{{!hasChanged}}"
          bindtap="handleReset"
          >重置</t-button
        >
        <t-button
          block
          size="large"
          class="{{classPrefix}}__confirm-btn"
          t-class="button button--confirm"
          theme="primary"
          disabled="{{!hasChanged}}"
          bindtap="handleConfirm"
          >确定</t-button
        >
      </t-button-group>
    </view>
  </t-popup>
</view>
