.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
/** dropdown-item */
.t-dropdown-item {
  position: fixed;
  right: 0;
  left: 0;
  top: 0;
  overflow: hidden;
  bottom: 0;
  /** 树形结构 */
  /** multi 多选多列情况 */
  /** dropRadio */
}
.t-dropdown-item__content {
  max-height: 324px;
  display: flex;
  flex-direction: column;
  z-index: 11600;
  overflow: hidden;
}
.t-dropdown-item__popup-host {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  left: 0;
  top: 0;
}
.t-dropdown-item__cell {
  position: relative;
  width: 100%;
  padding: 12px 16px;
  line-height: 24px;
  font-size: 16px;
  text-align: left;
}
.t-dropdown-item__cell .t-icon {
  float: left;
}
.t-dropdown-item__content.t-is-single .t-dropdown-item__cell::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  left: 0;
  bottom: 0;
  border-bottom: 1px solid #e6e6e6;
  transform: scaleY(0.5);
  left: 16px;
}
.t-dropdown-item__body {
  flex: 1;
  background: #fff;
  overflow: auto;
}
.t-dropdown-item__foot {
  background: #fff;
  position: relative;
}
.t-dropdown-item__foot::before {
  z-index: 1;
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  left: 0;
  top: 0;
  border-top: 1px solid #e6e6e6;
  transform: scaleY(0.5);
}
.t-dropdown-item__foot .t-button {
  flex: 1;
  height: 100%;
  border-radius: 0;
  border: 0 none;
}
.t-dropdown-item__foot .t-button::after {
  display: none;
}
.t-dropdown-item__foot .t-button--text {
  color: #000000;
}
.t-dropdown-item__column {
  flex: 1;
}
.t-dropdown-item__column--ancestor {
  background: #eee;
}
.t-dropdown-item__column--parent {
  background-color: #f3f3f3;
}
.t-dropdown-item .t-radio,
.t-dropdown-item .t-checkbox {
  flex-direction: row-reverse;
  white-space: nowrap;
  padding-left: 0;
}
.t-dropdown-item .t-radio__content-wrap,
.t-dropdown-item .t-checkbox__content-wrap {
  width: 100%;
  display: flex;
  flex: 1;
  justify-content: space-between;
}
.t-dropdown-item .t-radio .t-radio__label,
.t-dropdown-item .t-checkbox .t-radio__label {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.t-dropdown-item__checkbox-group {
  display: grid;
  padding: 32rpx;
  grid-gap: 32rpx;
}
.t-dropdown-item__checkbox-item {
  display: block;
}
.t-dropdown-item .t-checkbox__icon-left {
  margin-right: 4px;
}
.t-dropdown-item .t-checkbox.t-is-checked .t-checkbox__label {
  color: #0052d9;
}
.t-dropdown-item .t-checkbox__label {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.t-dropdown-item .t-dropdown-item__tree-group {
  flex: 1;
}
.t-dropdown-item .t-radio__content-wrap {
  margin-left: 0;
  align-items: center;
}
.t-dropdown-item .t-radio__content-inner {
  margin-top: 0;
}
.t-dropdown-item .t-radio__icon-wrap {
  flex-shrink: 0;
}
.t-dropdown-item__tree-item {
  height: 96rpx;
  line-height: 96rpx;
  font-size: 32rpx;
  padding-left: 32rpx;
}
.t-dropdown-item__tree-item--active {
  color: #0052d9;
}
.t-dropdown-item .t-is-tree {
  max-height: 324px;
}
.t-dropdown-item .t-is-tree .t-dropdown-item__body {
  display: flex;
  overflow: hidden;
}
.t-dropdown-item .t-is-tree .t-dropdown-item__body .t-dropdown-item__tree-group {
  flex: 1;
  margin: 0;
}
.t-dropdown-item .t-is-tree .t-checkbox-group .t-dropdown-item__cell:not(:last-child)::after {
  display: none;
}
.t-dropdown-item .t-is-tree .t-checkbox__content-wrap {
  overflow: hidden;
  align-items: center;
}
.t-dropdown-item .t-is-tree .t-dropdown-item__cell {
  background: none;
}
.t-dropdown-item .t-is-tree .t-dropdown-item__cell::after {
  display: none;
}
.t-dropdown-item .t-is-tree.t-is-col2 .t-dropdown-item__tree-group,
.t-dropdown-item .t-is-tree.t-is-col3 .t-dropdown-item__tree-group {
  width: 33.33%;
}
.t-dropdown-item .t-is-tree.t-is-col2 .t-dropdown-item__tree-group:nth-child(1),
.t-dropdown-item .t-is-tree.t-is-col3 .t-dropdown-item__tree-group:nth-child(1) {
  background: #f5f5f5;
}
.t-dropdown-item .t-is-tree.t-is-col2 .t-dropdown-item__tree-group:nth-child(1) .t-checkbox__icon-left,
.t-dropdown-item .t-is-tree.t-is-col3 .t-dropdown-item__tree-group:nth-child(1) .t-checkbox__icon-left,
.t-dropdown-item .t-is-tree.t-is-col2 .t-dropdown-item__tree-group:nth-child(1) .t-radio__default-shape,
.t-dropdown-item .t-is-tree.t-is-col3 .t-dropdown-item__tree-group:nth-child(1) .t-radio__default-shape {
  display: none;
}
.t-dropdown-item .t-is-tree.t-is-col3 .t-dropdown-item__tree-group {
  width: 33.33%;
}
.t-dropdown-item .t-is-tree.t-is-col3 .t-dropdown-item__tree-group:nth-child(2) {
  background: #f9f9f9;
}
.t-dropdown-item .t-is-tree.t-is-col3 .t-dropdown-item__tree-group:nth-child(2) .t-checkbox__icon-left {
  display: none;
}
.t-dropdown-item .t-is-multi .t-dropdown-item__body {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  overflow-y: auto;
}
.t-dropdown-item .t-is-multi .t-dropdown-item__cell {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}
.t-dropdown-item .t-is-multi .t-dropdown-item__cell:not(:last-child)::after {
  display: none;
}
.t-dropdown-item .t-is-multi .t-checkbox .t-checkbox__icon-left {
  display: none;
}
.t-dropdown-item .t-is-multi .t-checkbox .t-checkbox__label {
  font-size: 28rpx;
}
.t-dropdown-item .t-is-multi .t-checkbox .t-checkbox__content-wrap {
  background-color: #f5f5f5;
  border-radius: 16rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 16rpx;
  box-sizing: border-box;
  height: 38px;
  line-height: 38px;
}
.t-dropdown-item .t-is-multi .t-checkbox.t-is-checked .t-checkbox__content-wrap {
  background-color: #ecf2fe;
}
.t-dropdown-item .t-is-multi .t-check-group {
  display: flex;
  flex-wrap: wrap;
}
.t-dropdown-item .t-is-multi.t-is-col2 .t-dropdown-item__cell {
  width: 50%;
  display: inline-block;
  padding-left: 16rpx;
  padding-right: 16rpx;
}
.t-dropdown-item .t-is-multi.t-is-col2 .t-dropdown-item__cell-group {
  padding-left: 16rpx;
  padding-right: 16rpx;
}
.t-dropdown-item .t-is-multi.t-is-col3 .t-dropdown-item__cell {
  width: 33.33%;
  display: inline-block;
  padding-left: 16rpx;
  padding-right: 16rpx;
}
.t-dropdown-item .t-is-multi.t-is-col3 .t-dropdown-item__cell-group {
  padding-left: 16rpx;
  padding-right: 16rpx;
}
.t-dropdown-item__radio .t-radio__default-shape {
  display: none;
}
.t-dropdown-item__radio.t-is-tick .t-icon-check {
  color: #0052d9;
}
.t-dropdown-item__radio.t-is-checked .t-radio__label {
  color: #0052d9;
}
.t-dropdown-item__radio.t-is-checked .t-radio__content-inner {
  font-size: 24px;
}
.button {
  border-radius: 0 !important;
  border: 0 !important;
}
.radio {
  padding-top: 24rpx !important;
  padding-bottom: 24rpx !important;
}
.radio__label--active {
  color: #0052d9 !important;
}
