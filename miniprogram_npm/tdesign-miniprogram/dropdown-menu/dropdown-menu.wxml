<view class="{{ classPrefix }}">
  <view class="{{ classPrefix }}__bar" id="t-bar">
    <view
      wx:for="{{menus}}"
      wx:key="index"
      bindtap="handleToggle"
      data-index="{{index}}"
      class="{{ classPrefix }}__item {{ activeIdx == index ? prefix + '-is-active' : ''}} {{ item.disabled ? prefix + '-is-disabled' : ''}}"
    >
      <view class="{{ classPrefix }}__title">{{item.label}}</view>
      <t-icon
        name="caret-down-small"
        size="48rpx"
        class="{{classPrefix}}__icon {{classPrefix}}__icon--{{activeIdx == index ? 'active' : ''}}"
      />
    </view>
  </view>
  <slot />
</view>
