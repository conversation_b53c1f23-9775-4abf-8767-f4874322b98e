.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-back-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
  transition: height 0.2s;
  height: auto;
}
.t-back-top.t-is-fixed {
  position: fixed;
  right: 32rpx;
  bottom: 133rpx;
}
.t-back-top.t-is-round,
.t-back-top.t-is-round-dark {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
}
.t-back-top.t-is-round .t-back-top__icon,
.t-back-top.t-is-round-dark .t-back-top__icon {
  margin: 0;
  border-radius: 0;
  background-color: transparent;
}
.t-back-top.t-is-round .t-back-top__text,
.t-back-top.t-is-round-dark .t-back-top__text {
  padding: 0;
  margin-top: 0;
}
.t-back-top.t-is-round,
.t-back-top.t-is-half-round {
  background-color: #ffffff;
  color: #333;
  border: 1rpx solid;
  border-color: #ddd;
}
.t-back-top.t-is-round .t-back-top__icon,
.t-back-top.t-is-half-round .t-back-top__icon,
.t-back-top.t-is-round .t-back-top__text,
.t-back-top.t-is-half-round .t-back-top__text {
  color: #333;
}
.t-back-top.t-is-round-dark,
.t-back-top.t-is-half-round-dark {
  background-color: #000;
  color: #fff;
}
.t-back-top.t-is-round-dark .t-back-top__icon,
.t-back-top.t-is-half-round-dark .t-back-top__icon,
.t-back-top.t-is-round-dark .t-back-top__text,
.t-back-top.t-is-half-round-dark .t-back-top__text {
  color: #fff;
}
.t-back-top.t-is-half-round,
.t-back-top.t-is-half-round-dark {
  width: 120rpx;
  height: 80rpx;
  border-radius: 120rpx 0 0 120rpx;
  flex-direction: row;
  right: 0;
}
.t-back-top.t-is-half-round .t-back-top__icon,
.t-back-top.t-is-half-round-dark .t-back-top__icon {
  margin: 0;
  border-radius: 0;
  background-color: transparent;
}
.t-back-top.t-is-half-round .t-back-top__text,
.t-back-top.t-is-half-round-dark .t-back-top__text {
  padding: 0;
  margin-left: 8rpx;
  width: 2em;
}
.t-back-top__text {
  font-size: 20rpx;
  color: #666;
  line-height: 24rpx;
}
.t-back-top__icon {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #666;
  font-size: 32rpx;
}
