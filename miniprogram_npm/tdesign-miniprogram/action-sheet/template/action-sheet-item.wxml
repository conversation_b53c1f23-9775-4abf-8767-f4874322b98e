<wxs src="../action-sheet.wxs" module="this" />
<template name="item">
  <block>
    <t-image
      slot="image"
      wx:if="{{ this.isImage(item.icon) }}"
      lazy
      class="{{classPrefix}}__square-image"
      src="{{item.icon}}"
      mode="aspectFill"
    />
    <t-icon slot="image" wx:else name="{{item.icon}}" class="{{classPrefix}}__square-image" size="72rpx" />
  </block>
  <view slot="text" style="{{ item.color ? 'color: ' + item.color : '' }}" class="{{classPrefix}}__square-text">
    {{item.label}}
  </view>
</template>
