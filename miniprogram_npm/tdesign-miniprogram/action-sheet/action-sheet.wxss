.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-action-sheet .flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.t-action-sheet .ellipsis {
  word-wrap: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.t-action-sheet__grid {
  padding: 48rpx 0 16rpx 0;
}
.t-action-sheet__grid-item {
  margin-bottom: 32rpx;
}
.t-action-sheet__list {
  background-color: #fff;
  border-bottom: 1rpx solid #f6f6f6;
}
.t-action-sheet__list:last-child {
  border-bottom: none;
}
.t-action-sheet__list-item {
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.t-action-sheet__list-item.t-is-disabled {
  color: rgba(0, 0, 0, 0.26);
}
.t-action-sheet__list-item-text {
  font-size: 32rpx;
  word-wrap: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.t-action-sheet__list-item-icon {
  margin-right: 16rpx;
}
.t-action-sheet__swiper-wrap {
  position: relative;
  background-color: #fff;
}
.t-action-sheet__square {
  height: 148rpx;
  margin-bottom: 32rpx;
}
.t-action-sheet__square-image {
  width: 72rpx;
  height: 72rpx;
  padding: 10rpx;
}
.t-action-sheet__square-text {
  width: 100%;
  margin-top: 10rpx;
  font-size: 28rpx;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
  word-wrap: normal;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.t-action-sheet__footer {
  background-color: #fff;
}
.t-action-sheet__gap-list {
  height: 16rpx;
  background-color: #f3f3f3;
}
.t-action-sheet__gap-grid {
  height: 1rpx;
  background-color: #f3f3f3;
}
.t-action-sheet__cancel {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 96rpx;
}
.t-action-sheet__dots {
  position: absolute;
  left: 50%;
  bottom: 32rpx;
  transform: translateX(-50%);
  display: flex;
  flex-direction: row;
}
.t-action-sheet__dots-item {
  width: 16rpx;
  height: 16rpx;
  background-color: #dcdcdc;
  border-radius: 50%;
  margin: 0 16rpx;
  transition: all 0.4s ease-in;
}
.t-action-sheet__dots-item.t-is-active {
  background-color: #0052d9;
}
.t-action-sheet__safe {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
