.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-cell {
  position: relative;
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  line-height: 48rpx;
  color: rgba(0, 0, 0, 0.9);
  background-color: #ffffff;
}
.t-cell::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  left: 0;
  bottom: 0;
  border-bottom: 1px solid #e7e7e7;
  transform: scaleY(0.5);
  left: 32rpx;
}
.t-cell--borderless::after {
  display: none;
}
.t-cell__description {
  font-size: 28rpx;
  line-height: 44rpx;
  color: rgba(0, 0, 0, 0.4);
}
.t-cell__description-text {
  margin-top: 8rpx;
}
.t-cell__note {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  overflow: hidden;
  color: rgba(0, 0, 0, 0.4);
}
.t-cell__title,
.t-cell__note {
  flex: 1 1 auto;
}
.t-cell__title:empty,
.t-cell__note:empty {
  display: none;
}
.t-cell__title-text {
  display: flex;
}
.t-cell__left,
.t-cell__right {
  display: flex;
  align-items: center;
  font-size: 48rpx;
  line-height: 48rpx;
}
.t-cell__left :not(:empty) {
  margin-right: 16rpx;
}
.t-cell__left-icon {
  font-size: 48rpx;
}
.t-cell__left-image {
  height: 112rpx;
  width: 112rpx;
}
.t-cell__right {
  margin-left: 8rpx;
  color: #bbb;
}
.t-cell__right-icon {
  color: #bbb;
  font-size: 48rpx;
  line-height: 48rpx;
}
.t-cell--hover.t-cell--hover-class {
  background-color: #f2f3f5;
}
.t-cell--required {
  font-size: 32rpx;
  color: #e34d59;
}
.t-cell--middle {
  align-items: center;
}
.t-cell--top {
  align-items: flex-start;
}
.t-cell--bottom {
  align-items: flex-end;
}
