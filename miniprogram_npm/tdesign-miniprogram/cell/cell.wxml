<view
  class="{{prefix}}-class {{classPrefix}} {{ hover ? classPrefix + '--hover' : ''}} {{ !bordered ? classPrefix + '--borderless' : ''}} {{classPrefix}}--{{align}}"
  hover-class="{{classPrefix}}--hover-class"
  hover-stay-time="70"
  bind:tap="onClick"
>
  <view class="{{classPrefix}}__left {{prefix}}-class-left">
    <t-icon wx:if="{{ leftIcon }}" name="{{leftIcon}}" class="{{classPrefix}}__left-icon {{prefix}}-class-left-icon" />
    <slot name="left-icon" />
    <image wx:if="{{ image }}" class="{{classPrefix}}__left-image {{prefix}}-class-image" src="{{ image }}" />
    <slot name="image" />
  </view>
  <view class="{{classPrefix}}__title {{prefix}}-class-title">
    <view class="{{classPrefix}}__title-text">
      <block wx:if="{{ title }}"> {{ title}} </block>
      <slot name="title" />
      <block wx:if="{{required}}">
        <text decode class="{{classPrefix}}--required">&nbsp;*</text>
      </block>
    </view>

    <view class="{{classPrefix}}__description {{prefix}}-class-description">
      <view wx:if="{{ description }}" class="{{classPrefix}}__description-text">{{description}}</view>
      <slot name="description" />
    </view>
  </view>

  <view class="{{classPrefix}}__note {{prefix}}-class-note">
    <text wx:if="{{ note }}">{{note}}</text>
    <slot name="note" />
  </view>

  <view class="{{classPrefix}}__right {{prefix}}-class-right">
    <t-icon wx:if="{{ arrow }}" name="chevron-right" class="{{classPrefix}}__right-icon {{prefix}}-class-right-icon" />
    <block wx:else>
      <t-icon name="{{rightIcon}}" class="{{classPrefix}}__right-icon {{prefix}}-class-right-icon" />
      <slot name="right-icon" />
    </block>
  </view>
</view>
