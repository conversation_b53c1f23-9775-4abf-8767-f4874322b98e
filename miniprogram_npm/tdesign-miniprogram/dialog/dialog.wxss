.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-dialog__wrapper .t-dialog {
  overflow: hidden;
  width: 644rpx;
  border-radius: 16rpx;
  background-color: #fff;
}
.t-dialog__wrapper .t-dialog__content {
  padding: 64rpx 48rpx;
  min-height: 176rpx;
  max-height: 776rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-size: 32rpx;
  line-height: 1.5;
}
.t-dialog__wrapper .t-dialog__header {
  text-align: center;
  font-weight: bold;
  font-size: 32rpx;
  line-height: 1.5;
  color: #000000;
}
.t-dialog__wrapper .t-dialog__body {
  overflow-y: scroll;
  text-align: center;
  -webkit-overflow-scrolling: touch;
  font-size: 32rpx;
  color: #888888;
  margin-top: 32rpx;
}
.t-dialog__wrapper .t-dialog__body--without-title {
  margin-top: 0;
}
.t-dialog__wrapper .t-dialog__body-text {
  word-wrap: break-word;
}
.t-dialog__wrapper .t-dialog__body--left {
  text-align: left;
}
.t-dialog__wrapper .t-dialog__body--right {
  text-align: right;
}
.t-dialog__wrapper .t-dialog__footer {
  display: flex;
}
.t-dialog__wrapper .t-dialog__footer-button-host {
  width: 100%;
}
.t-dialog__wrapper .t-dialog__footer--column {
  flex-flow: column-reverse;
}
.t-dialog__wrapper .t-dialog__footer--column .t-dialog__button {
  width: 100%;
}
.t-dialog__wrapper .t-dialog__button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  font-size: 32rpx;
  padding: 0 48rpx;
  background-color: #fff;
  width: 644rpx;
  height: 112rpx;
  line-height: 112rpx;
  border-radius: 0;
  border: 0;
}
.t-dialog__wrapper .t-dialog__button:before {
  content: ' ';
  position: absolute;
  box-sizing: border-box;
  top: 0;
  left: 0;
  border-top: 1px solid #e7e7e7;
  border-left: 1px solid #e7e7e7;
  transform: scale(0.5);
  transform-origin: 0 0;
  width: 200%;
  height: 200%;
}
.t-dialog__wrapper .t-dialog__button-half {
  width: 322rpx;
}
.t-dialog__wrapper .t-dialog__button-confirm {
  color: #0052d9;
  font-weight: bold;
}
.t-dialog__wrapper .t-dialog__button-cancel {
  color: #000000;
  font-weight: normal;
}
.t-dialog__wrapper .t-dialog__button-bounce-enter {
  transform: translate3d(-50%, -50%, 0) scale(0.7);
  opacity: 0;
}
.t-dialog__wrapper .t-dialog__button-bounce-leave-active {
  transform: translate3d(-50%, -50%, 0) scale(0.9);
  opacity: 0;
}
.t-dialog__wrapper .t-dialog__button::after {
  border-radius: 0;
  border-color: rgba(0, 0, 0, 0.05);
}
.t-dialog__wrapper .t-dialog__text + .t-dialog__input {
  margin-top: 48rpx;
}
.t-dialog__wrapper .t-dialog__input {
  width: 100%;
  background-color: #f0f0f0;
  font-size: 32rpx;
  color: #000000;
  border-radius: 16rpx / 2;
  height: 96rpx;
  line-height: 1.5;
  padding: 24rpx;
  margin-top: 24rpx;
  box-sizing: border-box;
  text-align: left;
}
.t-dialog__wrapper .t-dialog__input-placeholder {
  color: #bbbbbb;
}
