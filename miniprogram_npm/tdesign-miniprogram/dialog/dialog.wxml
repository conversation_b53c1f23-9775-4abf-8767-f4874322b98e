<import src="../common/template/button.wxml" />
<wxs src="./dialog.wxs" module="_" />

<t-popup
  name="dialog"
  class="{{classPrefix}}__wrapper"
  visible="{{visible}}"
  showOverlay="{{showOverlay}}"
  closeOnOverlayClick="{{closeOnOverlayClick}}"
  preventScrollThrough="{{preventScrollThrough}}"
  overlayProps="{{overlayProps}}"
  zIndex="{{zIndex}}"
  placement="center"
  bind:visible-change="overlayClick"
>
  <view slot="content" class="{{classPrefix}} {{prefix}}-class">
    <view class="{{classPrefix}}__content {{prefix}}-class-content">
      <view wx:if="{{title}}" class="{{classPrefix}}__header">{{title}}</view>
      <slot name="title" />
      <view wx:if="{{content}}" class="{{classPrefix}}__body {{title ? '' : classPrefix + '__body--without-title'}}">
        <text class="{{classPrefix}}__body-text">{{content}}</text>
      </view>
      <slot name="content" />
    </view>
    <view
      class="{{classPrefix}}__footer {{classPrefix}}__footer--default {{buttonLayout === 'vertical' ? classPrefix + '__footer--column' : ''}}"
    >
      <t-button
        block
        class="{{classPrefix}}__footer-button-host"
        wx:if="{{actions}}"
        wx:for="{{actions}}"
        wx:key="action"
        data-index="{{index}}"
        t-class="{{classPrefix}}__button {{classPrefix}}__button-{{item.primary ? 'confirm' : 'cancel'}} {{prefix}}-class-action"
        bindtap="onActionTap"
      >
        {{item.name}}
      </t-button>
      <slot name="actions" />
      <t-button
        block
        class="{{classPrefix}}__footer-button-host {{classPrefix}}__footer-button--cancel"
        wx:if="{{cancelBtn && _.getTypeof(cancelBtn) == 'string'}}"
        t-class="{{classPrefix}}__button {{classPrefix}}__button-cancel {{confirmBtn ? classPrefix + '__button-half' : ''}} {{prefix}}-class-cancel"
        bindtap="onCancel"
      >
        {{cancelBtn}}
      </t-button>
      <block wx:elif="{{_.getTypeof(cancelBtn) == 'object'}}">
        <template
          is="button"
          data="{{...cancelBtn, block: true, type: 'cancel', externalClass: classPrefix + '__button ' + classPrefix + '__button-cancel ' + (cancelBtn ? classPrefix + '__button-half ' : ' ') + prefix + '-class-cancel',class: classPrefix + '__footer-button-host ' + classPrefix + '__footer-button--cancel' }}"
        ></template>
      </block>
      <slot name="cancelBtn" />
      <t-button
        block
        class="{{classPrefix}}__footer-button-host {{classPrefix}}__footer-button--confirm"
        wx:if="{{confirmBtn && _.getTypeof(confirmBtn) == 'string'}}"
        t-class="{{classPrefix}}__button {{classPrefix}}__button-confirm {{cancelBtn ? classPrefix + '__button-half' : ''}} {{prefix}}-class-confirm"
        bind:tap="onConfirm"
        bind:getuserinfo="openValueCBHandle"
        bind:contact="openValueCBHandle"
        bind:getphonenumber="openValueCBHandle"
        bind:opensetting="openValueCBHandle"
        bind:launchapp="openValueCBHandle"
        bind:error="openValueErrCBHandle"
      >
        {{confirmBtn}}
      </t-button>
      <block wx:elif="{{_.getTypeof(confirmBtn) == 'object'}}">
        <template
          is="button"
          data="{{...confirmBtn, block: true,  type: 'confirm', externalClass: classPrefix + '__button ' + classPrefix + '__button-confirm ' + (cancelBtn ? classPrefix + '__button-half ' : ' ') + prefix + '-class-confirm', class: classPrefix + '__footer-button-host ' + classPrefix + '__footer-button--confirm'}}"
        ></template>
      </block>
      <slot name="confirmBtn" />
    </view>
  </view>
</t-popup>
