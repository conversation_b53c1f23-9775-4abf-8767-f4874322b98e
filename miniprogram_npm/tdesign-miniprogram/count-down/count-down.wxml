<view class="t-class {{classPrefix}} {{classPrefix}}--{{size}} {{classPrefix}}--{{theme}}">
  <slot wx:if="{{content !== 'default'}}" />
  <block wx:elif="{{theme == 'default'}}">{{formattedTime}}</block>
  <block wx:else>
    <text class="{{classPrefix}}__item">{{_.format(timeData.hours)}}</text>
    <text class="{{classPrefix}}__split {{classPrefix}}__split--{{splitWithUnit ? 'text' : 'dot'}}"
      >{{splitWithUnit ? '时' : ':'}}</text
    >
    <text class="{{classPrefix}}__item">{{_.format(timeData.minutes)}}</text>
    <text class="{{classPrefix}}__split {{classPrefix}}__split--{{splitWithUnit ? 'text' : 'dot'}}"
      >{{splitWithUnit ? '分' : ':'}}</text
    >
    <text class="{{classPrefix}}__item">{{_.format(timeData.seconds)}}</text>
    <text
      wx:if="{{splitWithUnit || millisecond}}"
      class="{{classPrefix}}__split {{classPrefix}}__split--{{splitWithUnit ? 'text' : 'dot'}}"
      >{{splitWithUnit ? '秒' : ':'}}</text
    >
  </block>
</view>

<wxs module="_"> module.exports.format = function(num) { return num < 10 ? '0' + num : num; } </wxs>
