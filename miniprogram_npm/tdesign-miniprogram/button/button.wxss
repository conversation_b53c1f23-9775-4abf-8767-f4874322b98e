.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.3s;
  user-select: none;
  touch-action: manipulation;
  font-size: 28rpx;
  height: 80rpx;
  border-radius: 8rpx;
  color: rgba(0, 0, 0, 0.9);
  border-color: #dcdcdc;
  background-color: #fff;
  outline: none;
  font-family: PingFang SC, Microsoft YaHei, Arial Regular;
  /* stylelint-disable-next-line */
  -webkit-appearance: none;
}
.t-button::after {
  background-color: #000;
  content: ' ';
  opacity: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: absolute;
}
.t-button:not(.t-is-disabled):active::after {
  opacity: 0.1;
}
.t-button--default {
  color: rgba(0, 0, 0, 0.9);
  background-color: #ffffff;
  border: 1px solid #dcdcdc;
}
.t-button--default.t-is-disabled {
  color: rgba(0, 0, 0, 0.26);
}
.t-button--primary {
  color: #fff;
  background-color: #0052d9;
  border: 1px solid #0052d9;
}
.t-button--primary.t-is-disabled {
  background-color: #bbd3fb;
  border-color: #bbd3fb;
}
.t-button--danger {
  color: #fff;
  background-color: #e34d59;
  border: 1px solid #e34d59;
}
.t-button--danger.t-is-disabled {
  background-color: #f8b9be;
  border-color: #f8b9be;
}
.t-button--text {
  color: #0052d9;
  background: none;
  border: 0;
}
.t-button--text.t-button--size-default {
  width: auto;
  height: auto;
  line-height: normal;
  padding: 0;
}
.t-button--text.t-is-disabled {
  color: #bbd3fb;
}
.t-button--ghost {
  background-color: transparent;
  border: 1px solid #fff;
  color: #fff;
}
.t-button--ghost.t-is-disabled {
  color: rgba(255, 255, 255, 0.35);
  border-color: rgba(255, 255, 255, 0.35);
}
.t-button--outline {
  background-color: transparent;
}
.t-button--outline.t-button--primary {
  color: #0052d9;
}
.t-button--outline.t-button--primary.t-is-disabled {
  background-color: transparent;
  color: #bbd3fb;
}
.t-button--outline.t-button--danger {
  color: #e34d59;
}
.t-button--outline.t-button--danger.t-is-disabled {
  background-color: transparent;
  color: #f8b9be;
}
.t-button--dashed {
  background-color: transparent;
  border-style: dashed;
}
.t-button--dashed.t-button--primary {
  color: #0052d9;
}
.t-button--dashed.t-button--primary.t-is-disabled {
  background-color: transparent;
  color: #bbd3fb;
}
.t-button--dashed.t-button--danger {
  color: #e34d59;
}
.t-button--dashed.t-button--danger.t-is-disabled {
  background-color: transparent;
  color: #f8b9be;
}
.t-button--base {
  height: 80rpx;
  line-height: 80rpx;
  padding-left: 31rpx;
  padding-right: 31rpx;
  font-size: 28rpx;
}
.t-button--size-l {
  height: 88rpx;
  line-height: 88rpx;
}
.t-button--size-l .t-button__icon {
  font-size: 48rpx;
}
.t-button--size-l .t-button__loading + .t-button__content:not(:empty),
.t-button--size-l .t-button__icon + .t-button__content:not(:empty) {
  margin-left: 16rpx;
}
.t-button--size-m .t-button__icon {
  font-size: 44rpx;
}
.t-button--size-m .t-button__loading + .t-button__content:not(:empty),
.t-button--size-m .t-button__icon + .t-button__content:not(:empty) {
  margin-left: 8rpx;
}
.t-button--size-s {
  height: 72rpx;
  line-height: 72rpx;
}
.t-button--size-s .t-button__icon {
  font-size: 40rpx;
}
.t-button--size-s .t-button__loading + .t-button__content:not(:empty),
.t-button--size-s .t-button__icon + .t-button__content:not(:empty) {
  margin-left: 8rpx;
}
.t-button__icon {
  border-radius: 8rpx;
}
.t-button--round.t-button--size-l {
  border-radius: 44rpx;
}
.t-button--round.t-button--size-m {
  border-radius: 40rpx;
}
.t-button--round.t-button--size-s {
  border-radius: 36rpx;
}
.t-button--square {
  padding: 0;
}
.t-button--square.t-button--size-l {
  width: 88rpx;
}
.t-button--square.t-button--size-m {
  width: 80rpx;
}
.t-button--square.t-button--size-s {
  width: 72rpx;
}
.t-button--circle {
  padding: 0;
}
.t-button--circle.t-button--size-l {
  border-radius: 50%;
  width: 88rpx;
}
.t-button--circle.t-button--size-m {
  border-radius: 50%;
  width: 80rpx;
}
.t-button--circle.t-button--size-s {
  border-radius: 50%;
  width: 72rpx;
}
.t-button.t-is-block {
  display: flex;
  width: 100%;
}
.t-button.t-is-disabled {
  cursor: not-allowed;
}
.t-button.button-hover:after {
  border-radius: 8rpx;
}
.t-button .position-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.t-button .indicator-blue {
  color: white;
}
.t-button-group .t-button {
  border: 0;
  border-radius: 0;
  box-shadow: 0;
  width: 100%;
  height: 100%;
}
