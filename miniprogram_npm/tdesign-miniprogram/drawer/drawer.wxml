<t-popup
  bind:visible-change="visibleChange"
  visible="{{visible}}"
  zIndex="{{zIndex}}"
  placement="{{placement == 'right' ? 'right' : 'left'}}"
  showOverlay="{{showOverlay}}"
  destroyOnClose="{{destroyOnClose}}"
  closeOnOverlayClick="{{closeOnOverlayClick}}"
>
  <view class="{{classPrefix}}__sidebar">
    <view
      class="{{classPrefix}}__sidebar-item"
      wx:for="{{items}}"
      wx:item="item"
      wx:key="index"
      data-item="{{item}}"
      data-index="{{index}}"
      bindtap="itemClick"
    >
      <view wx:if="{{item.icon}}" class="{{classPrefix}}__sidebar-item-icon">
        <t-icon name="{{item.icon}}" size="24px" />
      </view>
      <view class="{{classPrefix}}__sidebar-item-title"> {{item.title}} </view>
    </view>
  </view>
</t-popup>
