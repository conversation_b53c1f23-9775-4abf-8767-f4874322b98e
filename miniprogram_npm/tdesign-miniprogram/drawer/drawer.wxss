.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
page {
  --td-drawer-sidebar-bg-color: #fff;
  --td-drawer-sidebar-border-color: #e7e7e7;
  --td-drawer-sidebar-title-color: rgba(0, 0, 0, 0.9);
  --td-drawer-sidebar-icon-color: rgba(0, 0, 0, 0.9);
}
.t-drawer__sidebar {
  width: calc(100vw * 0.82);
  height: 100vh;
  background: var(--td-drawer-sidebar-bg-color);
}
.t-drawer__sidebar-item {
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid var(--td-drawer-sidebar-border-color);
  padding: 24rpx 0;
  margin-left: 32rpx;
}
.t-drawer__sidebar-item-title {
  flex: 1;
  color: var(--td-drawer-sidebar-title-color);
}
.t-drawer__sidebar-item-icon {
  width: 48rpx;
  height: 48rpx;
  padding-right: 16rpx;
  color: var(--td-drawer-sidebar-icon-color);
}
