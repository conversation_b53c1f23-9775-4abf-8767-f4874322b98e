.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-button-group {
  display: inline-flex;
  width: 100%;
  background: #fff;
}
.t-button-group--menu t-button:not(:first-child)::before {
  position: absolute;
  top: 20px/2;
  height: 24px;
  left: 0;
  content: '';
  width: 0;
  border-left: solid #e6e6e6 1px;
}
.t-button-group t-button {
  flex: 1;
  /* stylelint-disable-next-line */
  -webkit-flex: 1;
  position: relative;
}
