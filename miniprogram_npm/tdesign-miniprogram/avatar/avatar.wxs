module.exports = {
  getAvatarOuterClass: function (classPrefix, size, shape) {
    var isIncludePx = size.indexOf('px') > -1;
    var classNames = [
      classPrefix,
      classPrefix + (shape === 'round' ? '--round' : '--circle'),
      isIncludePx ? '' : 't-size-' + (size || 'medium').slice(0, 1),
    ];
    return classNames.join(' ');
  },
  getAvatarSizePx: function (size = 'medium') {
    var pxIndex = size.indexOf('px');
    if (pxIndex > -1) {
      return 'width:' + size + ';height:' + size + ';font-size:' + ((size.slice(0, pxIndex) / 8) * 3 + 2) + 'px;';
    }
  },
  getStyles: function (isShow, zIndex) {
    var styles = 'z-index:' + zIndex + ';';
    return styles + (isShow ? '' : 'display: none;');
  },
  getIconClass: function (classPrefix, size) {
    if (size.indexOf('px') > -1) return;
    return classPrefix + '__icon--default-' + (size || 'medium').slice(0, 1);
  },
};
