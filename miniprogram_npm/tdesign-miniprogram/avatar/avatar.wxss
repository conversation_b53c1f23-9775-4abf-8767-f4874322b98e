.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
page {
  --td-avatar-group-border-color: #fff;
}
.border--small {
  border: 4rpx solid var(--td-avatar-group-border-color, #fff);
}
.border--medium {
  border: 6rpx solid var(--td-avatar-group-border-color, #fff);
}
.border--large {
  border: 8rpx solid var(--td-avatar-group-border-color, #fff);
}
page {
  --td-avatar-bg-color: #d4e3fc;
  --td-avatar-content-color: #0052d9;
}
.t-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}
.t-avatar__wrapper {
  float: left;
  position: relative;
  background-color: var(--td-avatar-bg-color, #d4e3fc);
  color: var(--td-avatar-content-color, #0052d9);
  border-radius: 999rpx;
}
.t-avatar__wrapper .t-badge-host {
  position: absolute;
}
.t-avatar__wrapper .t-badge__round {
  top: -10%;
  right: -10%;
}
.t-avatar__wrapper .t-badge__circle {
  top: -5%;
  right: -5%;
}
.t-avatar.t-size-l {
  width: 128rpx;
  height: 128rpx;
  font-size: 52rpx;
}
.t-avatar.t-size-m {
  width: 96rpx;
  height: 96rpx;
  font-size: 40rpx;
}
.t-avatar.t-size-s {
  width: 64rpx;
  height: 64rpx;
  font-size: 28rpx;
}
.t-avatar .t-image,
.t-avatar__image {
  width: 100%;
  height: 100%;
}
.t-avatar--circle {
  border-radius: 999rpx;
  overflow: hidden;
}
.t-avatar--round {
  border-radius: 10rpx;
  overflow: hidden;
}
.t-avatar__text,
.t-avatar__icon {
  font-size: inherit;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.t-avatar__text:empty,
.t-avatar__icon:empty {
  width: 0;
  height: 0;
}
