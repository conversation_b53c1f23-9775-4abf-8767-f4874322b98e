<view class="{{className}}">
  <slot />
  <!-- 自定义折叠元素 -->
  <view class="{{classPrefix}}__collapse--slot {{collapseAvatar ? prefix + '-is-hidden' : ''}}">
    <slot name="collapseAvatar" />
  </view>
  <!-- 默认折叠元素 -->
  <view class="{{classPrefix}}__collapse--default" wx:if="{{max && (max < length)}}">
    <t-avatar
      t-class-image="border--{{borderSize}} {{prefix}}-class-image"
      t-class-content="{{prefix}}-class-content"
      size="{{size}}"
      >{{collapseAvatar || '+N'}}</t-avatar
    >
  </view>
</view>
