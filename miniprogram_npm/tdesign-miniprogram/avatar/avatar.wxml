<wxs src="./avatar.wxs" module="this" />

<view class="{{classPrefix}}__wrapper {{prefix}}-class" style="{{this.getStyles(isShow, zIndex)}}">
  <view
    class="{{this.getAvatarOuterClass(classPrefix, size, shape)}} border--{{borderSize}} {{prefix}}-class-image "
    style="{{this.getAvatarSizePx(size)}}"
  >
    <t-image
      wx:if="{{image}}"
      class="{{prefix}}-image"
      t-class-load="{{prefix}}-class-alt"
      t-class="{{classPrefix}}__image"
      src="{{image}}"
      mode="aspectFill"
      binderror="onLoadError"
      loadFailed="{{alt}}"
    />
    <view
      wx:elif="{{icon}}"
      class="{{classPrefix}}__icon {{this.getIconClass(classPrefix, size)}} {{prefix}}-class-icon"
    >
      <t-icon name="{{icon}}" />
    </view>
    <view wx:else class="{{classPrefix}}__text {{prefix}}-class-content">
      <slot />
    </view>
  </view>
  <t-badge
    class="{{prefix}}-badge-host {{prefix}}-badge__{{shape === 'circle' ? 'circle' : 'round'}}"
    wx:if="{{badgeProps.dot || badgeProps.count}}"
    color="{{badgeProps.color}}"
    count="{{badgeProps.count}}"
    max-count="{{badgeProps.maxCount || 100}}"
    dot="{{badgeProps.dot}}"
    content="{{badgeProps.content}}"
    size="{{badgeProps.size}}"
    visible="{{badgeProps.visible}}"
    offset="{{badgeProps.offset}}"
  />
</view>
