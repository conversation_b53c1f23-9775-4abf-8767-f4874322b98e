.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
page {
  --td-avatar-group-border-color: #fff;
}
.border--small {
  border: 4rpx solid var(--td-avatar-group-border-color, #fff);
}
.border--medium {
  border: 6rpx solid var(--td-avatar-group-border-color, #fff);
}
.border--large {
  border: 8rpx solid var(--td-avatar-group-border-color, #fff);
}
.t-avatar-group {
  display: inline-flex;
  align-items: center;
}
.t-avatar-group-offset-left-small t-avatar:not(:first-child) {
  margin-left: -4px;
}
.t-avatar-group-offset-left-medium t-avatar:not(:first-child) {
  margin-left: -6px;
}
.t-avatar-group-offset-left-large t-avatar:not(:first-child) {
  margin-left: -8px;
}
.t-avatar-group-offset-right-small t-avatar:not(:last-child) {
  margin-right: -4px;
}
.t-avatar-group-offset-right-medium t-avatar:not(:last-child) {
  margin-right: -6px;
}
.t-avatar-group-offset-right-large t-avatar:not(:last-child) {
  margin-right: -8px;
}
.t-avatar-group__collapse--slot {
  float: left;
}
.t-avatar-group__collapse--slot:not(:empty) + .t-avatar-group__collapse--default {
  display: none;
  float: left;
}
.t-avatar-group__collapse--slot:empty + .t-avatar-group__collapse--default {
  display: block;
  float: left;
}
.t-avatar-group .t-is-hidden {
  display: none;
}
