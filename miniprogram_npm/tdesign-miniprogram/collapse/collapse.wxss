.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-collapse__title {
  font-size: 28rpx;
  padding: 12px;
  padding-top: 24px;
  color: #999;
}
.hairline--top-bottom:after {
  position: absolute;
  box-sizing: border-box;
  transform-origin: center;
  content: ' ';
  pointer-events: none;
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
  border: 0 solid #eee;
  transform: scale(0.5);
}
.hairline--top-bottom:after {
  border-width: 1px 0;
}
