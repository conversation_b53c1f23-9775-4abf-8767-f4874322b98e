.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-collapse-panel {
  overflow: hidden;
  transition: height 200ms ease-in-out;
  box-shadow: inset 0 -1px 0 0 #eeeeee;
  background-color: #fff;
}
.t-collapse-panel--active {
  height: auto;
}
.t-collapse-panel--disabled {
  pointer-events: none;
}
.t-collapse-panel--disabled .t-collapse-panel__content,
.t-collapse-panel--disabled .t-collapse-panel__header {
  opacity: 0.3;
}
.t-collapse-panel__header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 32rpx;
  height: 96rpx;
  box-shadow: inset 0 -1px 0 0 #eeeeee;
  color: #000000;
}
.t-collapse-panel__header-right {
  display: inline-flex;
  align-items: center;
  height: 100%;
}
.t-collapse-panel__header-icon {
  height: 100%;
  padding-left: 8px;
  width: 44px;
  padding-right: 8px;
  color: rgba(0, 0, 0, 0.4);
}
.t-collapse-panel__extra {
  font-size: 32rpx;
}
.t-collapse-panel__body {
  box-shadow: inset 0 -1px 0 0 #eeeeee;
}
.t-collapse-panel__wrapper {
  transition: height 200ms ease-in-out;
}
.t-collapse-panel__content {
  color: #000000;
  font-size: 28rpx;
  padding: 32rpx;
  line-height: 1.5;
}
.class-title {
  font-size: 32rpx;
}
.class-title--disabled {
  color: rgba(0, 0, 0, 0.26);
}
.class-note--disabled {
  color: rgba(0, 0, 0, 0.26) !important;
}
.class-right-icon--disabled {
  color: rgba(0, 0, 0, 0.26) !important;
}
