.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-cell-group {
  position: relative;
}
.t-cell-group__title {
  font-family: PingFangSC-Regular;
  font-size: 28rpx;
  color: #888888;
  text-align: left;
  line-height: 90rpx;
  background-color: #fbfbfb;
  padding-left: 32rpx;
}
.t-cell-group--bordered::before {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  left: 0;
  top: 0;
  border-top: 1px solid #e6e6e6;
  transform: scaleY(0.5);
  z-index: 1;
}
.t-cell-group--bordered::after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 0;
  left: 0;
  bottom: 0;
  border-bottom: 1px solid #e6e6e6;
  transform: scaleY(0.5);
  z-index: 1;
}
