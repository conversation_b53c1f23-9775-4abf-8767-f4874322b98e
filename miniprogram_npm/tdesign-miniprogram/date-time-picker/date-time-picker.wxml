<t-picker
  class="{{classPrefix}}"
  visible="{{visible}}"
  value="{{columnsValue}}"
  bind:pick="onColumnChange"
  bind:change="onConfirm"
  bind:cancel="onCancel"
  bind:visible-change="onVisibleChange"
  header="{{header}}"
  title="{{title}}"
  confirmBtn="{{confirmBtn || locale.confirm}}"
  cancelBtn="{{cancelBtn || locale.cancel}}"
>
  <slot slot="header" name="header" />

  <t-picker-item wx:for="{{columns}}" wx:key="index" class="{{classPrefix}}__item" options="{{item}}" index="index" />
  <slot slot="footer" name="footer" />
</t-picker>
