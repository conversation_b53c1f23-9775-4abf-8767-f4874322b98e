/* eslint-disable */
var utils = require('../wxs/utils.wxs');

function tabClass(active, ellipsis) {
  var classes = ['tab-class'];

  if (active) {
    classes.push('tab-active-class');
  }

  if (ellipsis) {
    classes.push('van-ellipsis');
  }

  return classes.join(' ');
}

function tabStyle(
  active,
  ellipsis,
  color,
  type,
  disabled,
  activeColor,
  inactiveColor,
  swipeThreshold,
  scrollable
) {
  var styles = [];
  var isCard = type === 'card';
  // card theme color
  if (color && isCard) {
    styles.push('border-color:' + color);

    if (!disabled) {
      if (active) {
        styles.push('background-color:' + color);
      } else {
        styles.push('color:' + color);
      }
    }
  }

  var titleColor = active ? activeColor : inactiveColor;
  if (titleColor) {
    styles.push('color:' + titleColor);
  }

  if (scrollable && ellipsis) {
    styles.push('flex-basis:' + 88 / swipeThreshold + '%');
  }

  return styles.join(';');
}

function tabCardTypeBorderStyle(color, type) {
  var isCard = type === 'card';
  var styles = [];
  if (isCard && color) {
    styles.push('border-color:' + color);
  }
  return styles.join(';');
}

function trackStyle(data) {
  if (!data.animated) {
    return '';
  }

  return [
    'transform: translate3d(' + -100 * data.currentIndex + '%, 0, 0)',
    '-webkit-transition-duration: ' + data.duration + 's',
    'transition-duration: ' + data.duration + 's',
  ].join(';');
}

function lineStyle(data) {
  var styles = [
    ['width', utils.addUnit(data.lineWidth)],
    ['transform', 'translateX(' + data.lineOffsetLeft + 'px)'],
    ['-webkit-transform', 'translateX(' + data.lineOffsetLeft + 'px)'],
  ];

  if (data.color) {
    styles.push(['background-color', data.color]);
  }

  if (data.lineHeight !== -1) {
    styles.push(['height', utils.addUnit(data.lineHeight)]);
    styles.push(['border-radius', utils.addUnit(data.lineHeight)]);
  }

  if (!data.skipTransition) {
    styles.push(['transition-duration', data.duration + 's']);
    styles.push(['-webkit-transition-duration', data.duration + 's']);
  }

  return styles
    .map(function (item) {
      return item.join(':');
    })
    .join(';');
}

module.exports = {
  tabClass: tabClass,
  tabStyle: tabStyle,
  trackStyle: trackStyle,
  lineStyle: lineStyle,
  tabCardTypeBorderStyle: tabCardTypeBorderStyle,
};
