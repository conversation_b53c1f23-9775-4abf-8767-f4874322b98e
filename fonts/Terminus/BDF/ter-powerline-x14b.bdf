STARTFONT 2.1
FONT -xos4-TerminessPowerline-Bold-R-Normal--14-140-72-72-C-80-ISO10646-1
SIZE 14 72 72
FONTBOUNDINGBOX 8 14 0 -2
STARTPROPERTIES 23
ADD_STYLE_NAME ""
AVERAGE_WIDTH 80
CHARSET_ENCODING "1"
CHARSET_REGISTRY "ISO10646"
COPYRIGHT "Copyright (C) 2011 Dimitar <PERSON>. Copyright (c) 2013, <PERSON>, <PERSON><PERSON> and <PERSON>"
FAMILY_NAME "Terminess Powerline"
FOUNDRY "xos4"
MIN_SPACE 8
NOTICE "Licensed under the SIL Open Font License, Version 1.1"
PIXEL_SIZE 14
POINT_SIZE 140
QUAD_WIDTH 8
RESOLUTION_X 72
RESOLUTION_Y 72
SETWIDTH_NAME "Normal"
SLANT "R"
SPACING "C"
WEIGHT 10
WEIGHT_NAME "Bold"
X_HEIGHT 12
DEFAULT_CHAR -3
FONT_DESCENT 2
FONT_ASCENT 12
ENDPROPERTIES

CHARS 894

STARTCHAR uni25AE
ENCODING 0
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
FE
FE
FE
FE
FE
FE
FE
FE
FE
00
00
ENDCHAR

STARTCHAR blackdiamond
ENCODING 1
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
18
3C
7E
FF
7E
3C
18
00
00
00
ENDCHAR

STARTCHAR shade
ENCODING 2
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
AA
55
AA
55
AA
55
AA
55
AA
55
AA
55
AA
55
ENDCHAR

STARTCHAR uni2409
ENCODING 3
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
CC
CC
FC
CC
CC
00
3F
0C
0C
0C
0C
00
00
ENDCHAR

STARTCHAR uni240C
ENCODING 4
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
FC
C0
F0
C0
C0
00
3F
30
3C
30
30
00
00
ENDCHAR

STARTCHAR uni240D
ENCODING 5
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
78
CC
C0
CC
78
00
3E
33
3E
36
33
00
00
ENDCHAR

STARTCHAR uni240A
ENCODING 6
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
C0
C0
C0
C0
F8
00
3F
30
3C
30
30
00
00
ENDCHAR

STARTCHAR degree
ENCODING 7
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
38
6C
6C
38
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR plusminus
ENCODING 8
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
18
18
7E
18
18
00
7E
00
00
ENDCHAR

STARTCHAR uni2424
ENCODING 9
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
CC
EC
FC
DC
CC
00
30
30
30
30
3F
00
00
ENDCHAR

STARTCHAR uni240B
ENCODING 10
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
CC
CC
CC
78
30
00
3F
0C
0C
0C
0C
00
00
ENDCHAR

STARTCHAR SF040000
ENCODING 11
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
F8
F8
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF030000
ENCODING 12
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR SF010000
ENCODING 13
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
1F
1F
18
18
18
18
18
18
ENDCHAR

STARTCHAR SF020000
ENCODING 14
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
1F
1F
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF050000
ENCODING 15
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni23BA
ENCODING 16
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni23BB
ENCODING 17
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
FF
FF
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF100000
ENCODING 18
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni23BC
ENCODING 19
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
FF
FF
00
00
00
ENDCHAR

STARTCHAR uni23BD
ENCODING 20
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
FF
FF
ENDCHAR

STARTCHAR SF080000
ENCODING 21
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
1F
1F
18
18
18
18
18
18
ENDCHAR

STARTCHAR SF090000
ENCODING 22
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR SF070000
ENCODING 23
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF060000
ENCODING 24
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR SF110000
ENCODING 25
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
18
18
18
18
18
18
18
18
ENDCHAR

STARTCHAR lessequal
ENCODING 26
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
0C
18
30
60
30
18
0C
00
7E
00
00
ENDCHAR

STARTCHAR greaterequal
ENCODING 27
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
30
18
0C
06
0C
18
30
00
7E
00
00
ENDCHAR

STARTCHAR pi
ENCODING 28
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR notequal
ENCODING 29
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
06
FE
18
30
FE
C0
00
00
00
00
ENDCHAR

STARTCHAR sterling
ENCODING 30
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
60
60
F8
60
60
60
66
FE
00
00
ENDCHAR

STARTCHAR periodcentered
ENCODING 31
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
18
18
00
00
00
00
00
00
ENDCHAR

STARTCHAR space
ENCODING 32
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR exclam
ENCODING 33
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
18
18
18
18
18
00
18
18
00
00
ENDCHAR

STARTCHAR quotedbl
ENCODING 34
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
66
66
66
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR numbersign
ENCODING 35
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
6C
FE
6C
6C
FE
6C
6C
6C
00
00
ENDCHAR

STARTCHAR dollar
ENCODING 36
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
10
10
7C
D6
D0
D0
7C
16
16
D6
7C
10
10
ENDCHAR

STARTCHAR percent
ENCODING 37
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
66
D6
6C
0C
18
18
30
36
6B
66
00
00
ENDCHAR

STARTCHAR ampersand
ENCODING 38
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
6C
38
76
DC
CC
CC
DC
76
00
00
ENDCHAR

STARTCHAR quotesingle
ENCODING 39
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
18
18
18
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR parenleft
ENCODING 40
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
0C
18
30
30
30
30
30
30
18
0C
00
00
ENDCHAR

STARTCHAR parenright
ENCODING 41
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
0C
0C
0C
0C
0C
0C
18
30
00
00
ENDCHAR

STARTCHAR asterisk
ENCODING 42
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
6C
38
FE
38
6C
00
00
00
00
ENDCHAR

STARTCHAR plus
ENCODING 43
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
18
18
7E
18
18
00
00
00
00
ENDCHAR

STARTCHAR comma
ENCODING 44
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
18
18
30
00
ENDCHAR

STARTCHAR hyphen
ENCODING 45
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
FE
00
00
00
00
00
00
ENDCHAR

STARTCHAR period
ENCODING 46
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
18
18
00
00
ENDCHAR

STARTCHAR slash
ENCODING 47
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
06
0C
0C
18
18
30
30
60
60
00
00
ENDCHAR

STARTCHAR zero
ENCODING 48
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
CE
DE
F6
E6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR one
ENCODING 49
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
38
78
18
18
18
18
18
18
7E
00
00
ENDCHAR

STARTCHAR two
ENCODING 50
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
06
0C
18
30
60
C0
FE
00
00
ENDCHAR

STARTCHAR three
ENCODING 51
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
06
3C
06
06
C6
C6
7C
00
00
ENDCHAR

STARTCHAR four
ENCODING 52
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
0E
1E
36
66
C6
FE
06
06
06
00
00
ENDCHAR

STARTCHAR five
ENCODING 53
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
FC
06
06
06
C6
7C
00
00
ENDCHAR

STARTCHAR six
ENCODING 54
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
60
C0
C0
FC
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR seven
ENCODING 55
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
06
06
0C
0C
18
18
30
30
30
00
00
ENDCHAR

STARTCHAR eight
ENCODING 56
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
7C
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR nine
ENCODING 57
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
7E
06
06
0C
78
00
00
ENDCHAR

STARTCHAR colon
ENCODING 58
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
18
18
00
00
00
18
18
00
00
ENDCHAR

STARTCHAR semicolon
ENCODING 59
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
18
18
00
00
00
18
18
30
00
ENDCHAR

STARTCHAR less
ENCODING 60
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
06
0C
18
30
60
30
18
0C
06
00
00
ENDCHAR

STARTCHAR equal
ENCODING 61
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
00
00
FE
00
00
00
00
00
ENDCHAR

STARTCHAR greater
ENCODING 62
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
60
30
18
0C
06
0C
18
30
60
00
00
ENDCHAR

STARTCHAR question
ENCODING 63
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
0C
18
18
00
18
18
00
00
ENDCHAR

STARTCHAR at
ENCODING 64
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
CE
D6
D6
D6
D6
CE
C0
7E
00
00
ENDCHAR

STARTCHAR A
ENCODING 65
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR B
ENCODING 66
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
FC
C6
C6
C6
C6
FC
00
00
ENDCHAR

STARTCHAR C
ENCODING 67
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C0
C0
C0
C0
C6
C6
7C
00
00
ENDCHAR

STARTCHAR D
ENCODING 68
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
F8
CC
C6
C6
C6
C6
C6
C6
CC
F8
00
00
ENDCHAR

STARTCHAR E
ENCODING 69
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
F8
C0
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR F
ENCODING 70
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
F8
C0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR G
ENCODING 71
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C0
C0
DE
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR H
ENCODING 72
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
FE
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR I
ENCODING 73
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
18
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR J
ENCODING 74
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
1E
0C
0C
0C
0C
0C
0C
CC
CC
78
00
00
ENDCHAR

STARTCHAR K
ENCODING 75
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
CC
D8
F0
F0
D8
CC
C6
C6
00
00
ENDCHAR

STARTCHAR L
ENCODING 76
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
C0
C0
C0
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR M
ENCODING 77
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
82
C6
EE
FE
D6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR N
ENCODING 78
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
E6
F6
DE
CE
C6
C6
C6
00
00
ENDCHAR

STARTCHAR O
ENCODING 79
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR P
ENCODING 80
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
C6
FC
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR Q
ENCODING 81
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
C6
C6
C6
DE
7C
06
00
ENDCHAR

STARTCHAR R
ENCODING 82
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
C6
FC
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR S
ENCODING 83
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C0
C0
7C
06
06
C6
C6
7C
00
00
ENDCHAR

STARTCHAR T
ENCODING 84
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
18
18
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR U
ENCODING 85
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR V
ENCODING 86
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
6C
6C
6C
38
38
00
00
ENDCHAR

STARTCHAR W
ENCODING 87
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
D6
FE
EE
C6
82
00
00
ENDCHAR

STARTCHAR X
ENCODING 88
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
6C
6C
38
38
6C
6C
C6
C6
00
00
ENDCHAR

STARTCHAR Y
ENCODING 89
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C3
C3
66
66
3C
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR Z
ENCODING 90
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
06
06
0C
18
30
60
C0
C0
FE
00
00
ENDCHAR

STARTCHAR bracketleft
ENCODING 91
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
30
30
30
30
30
30
30
30
3C
00
00
ENDCHAR

STARTCHAR backslash
ENCODING 92
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
60
60
30
30
18
18
0C
0C
06
06
00
00
ENDCHAR

STARTCHAR bracketright
ENCODING 93
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
0C
0C
0C
0C
0C
0C
0C
0C
3C
00
00
ENDCHAR

STARTCHAR asciicircum
ENCODING 94
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
18
3C
66
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR underscore
ENCODING 95
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
FE
00
ENDCHAR

STARTCHAR grave
ENCODING 96
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR a
ENCODING 97
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR b
ENCODING 98
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
FC
C6
C6
C6
C6
C6
FC
00
00
ENDCHAR

STARTCHAR c
ENCODING 99
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
C0
C0
C6
7C
00
00
ENDCHAR

STARTCHAR d
ENCODING 100
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
06
06
7E
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR e
ENCODING 101
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR f
ENCODING 102
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
1E
30
30
FC
30
30
30
30
30
30
00
00
ENDCHAR

STARTCHAR g
ENCODING 103
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR h
ENCODING 104
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR i
ENCODING 105
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR j
ENCODING 106
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
06
00
0E
06
06
06
06
06
66
66
3C
ENDCHAR

STARTCHAR k
ENCODING 107
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
C6
CC
D8
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR l
ENCODING 108
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
18
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR m
ENCODING 109
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
D6
D6
D6
D6
D6
D6
00
00
ENDCHAR

STARTCHAR n
ENCODING 110
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR o
ENCODING 111
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR p
ENCODING 112
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
C6
C6
C6
C6
C6
FC
C0
C0
ENDCHAR

STARTCHAR q
ENCODING 113
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C6
C6
C6
C6
C6
7E
06
06
ENDCHAR

STARTCHAR r
ENCODING 114
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
DE
F0
E0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR s
ENCODING 115
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C0
C0
7C
06
06
FC
00
00
ENDCHAR

STARTCHAR t
ENCODING 116
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
30
30
FC
30
30
30
30
30
1E
00
00
ENDCHAR

STARTCHAR u
ENCODING 117
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR v
ENCODING 118
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
6C
6C
38
38
00
00
ENDCHAR

STARTCHAR w
ENCODING 119
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
D6
D6
D6
D6
7C
00
00
ENDCHAR

STARTCHAR x
ENCODING 120
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
6C
38
6C
C6
C6
00
00
ENDCHAR

STARTCHAR y
ENCODING 121
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR z
ENCODING 122
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
0C
18
30
60
C0
FE
00
00
ENDCHAR

STARTCHAR braceleft
ENCODING 123
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
1C
30
30
30
60
30
30
30
30
1C
00
00
ENDCHAR

STARTCHAR bar
ENCODING 124
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
18
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR braceright
ENCODING 125
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
70
18
18
18
0C
18
18
18
18
70
00
00
ENDCHAR

STARTCHAR asciitilde
ENCODING 126
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
73
DB
CE
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR nbspace
ENCODING 160
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR exclamdown
ENCODING 161
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR cent
ENCODING 162
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
10
10
7C
D6
D0
D0
D0
D6
7C
10
10
ENDCHAR

STARTCHAR sterling
ENCODING 163
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
60
60
F8
60
60
60
66
FE
00
00
ENDCHAR

STARTCHAR currency
ENCODING 164
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
66
3C
66
66
66
3C
66
00
00
00
ENDCHAR

STARTCHAR yen
ENCODING 165
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C3
C3
66
3C
18
7E
18
7E
18
18
00
00
ENDCHAR

STARTCHAR brokenbar
ENCODING 166
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
18
18
00
00
18
18
18
18
00
00
ENDCHAR

STARTCHAR section
ENCODING 167
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
3C
66
60
38
6C
66
66
36
1C
06
66
3C
00
ENDCHAR

STARTCHAR dieresis
ENCODING 168
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR copyright
ENCODING 169
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7E
81
99
A5
A1
A5
99
81
7E
00
00
ENDCHAR

STARTCHAR ordfeminine
ENCODING 170
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
3C
06
3E
66
3E
00
7E
00
00
00
00
00
00
ENDCHAR

STARTCHAR guillemotleft
ENCODING 171
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
1B
36
6C
D8
6C
36
1B
00
00
ENDCHAR

STARTCHAR logicalnot
ENCODING 172
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
06
06
06
00
00
00
00
00
ENDCHAR

STARTCHAR softhyphen
ENCODING 173
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
7C
00
00
00
00
00
00
ENDCHAR

STARTCHAR registered
ENCODING 174
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7E
81
B9
A5
B9
A9
A5
81
7E
00
00
ENDCHAR

STARTCHAR macron
ENCODING 175
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7C
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR degree
ENCODING 176
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
38
6C
6C
38
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR plusminus
ENCODING 177
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
18
18
7E
18
18
00
7E
00
00
ENDCHAR

STARTCHAR twosuperior
ENCODING 178
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
38
6C
18
30
7C
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR threesuperior
ENCODING 179
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
78
0C
38
0C
78
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR acute
ENCODING 180
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR mu
ENCODING 181
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
CE
F6
C0
C0
ENDCHAR

STARTCHAR paragraph
ENCODING 182
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
D6
D6
D6
D6
76
16
16
16
16
00
00
ENDCHAR

STARTCHAR periodcentered
ENCODING 183
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
18
18
00
00
00
00
00
00
ENDCHAR

STARTCHAR cedilla
ENCODING 184
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
30
30
60
ENDCHAR

STARTCHAR onesuperior
ENCODING 185
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
18
38
18
18
18
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR ordmasculine
ENCODING 186
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
3C
66
66
66
3C
00
7E
00
00
00
00
00
00
ENDCHAR

STARTCHAR guillemotright
ENCODING 187
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
D8
6C
36
1B
36
6C
D8
00
00
ENDCHAR

STARTCHAR onequarter
ENCODING 188
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
E0
62
66
6C
18
30
66
CE
9A
3E
06
06
00
ENDCHAR

STARTCHAR onehalf
ENCODING 189
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
E0
62
66
6C
18
30
60
DC
B6
0C
18
3E
00
ENDCHAR

STARTCHAR threequarters
ENCODING 190
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
E0
30
62
36
EC
18
30
66
CE
9A
3E
06
06
00
ENDCHAR

STARTCHAR questiondown
ENCODING 191
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
30
00
30
30
60
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Agrave
ENCODING 192
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Aacute
ENCODING 193
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Acircumflex
ENCODING 194
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Atilde
ENCODING 195
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Adieresis
ENCODING 196
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Aring
ENCODING 197
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
38
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR AE
ENCODING 198
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
D8
D8
D8
FE
D8
D8
D8
D8
DE
00
00
ENDCHAR

STARTCHAR Ccedilla
ENCODING 199
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C0
C0
C0
C0
C6
C6
7C
30
60
ENDCHAR

STARTCHAR Egrave
ENCODING 200
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR Eacute
ENCODING 201
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR Ecircumflex
ENCODING 202
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR Edieresis
ENCODING 203
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR Igrave
ENCODING 204
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Iacute
ENCODING 205
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
0C
18
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Icircumflex
ENCODING 206
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Idieresis
ENCODING 207
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
66
66
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Eth
ENCODING 208
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
78
6C
66
66
F6
66
66
66
6C
78
00
00
ENDCHAR

STARTCHAR Ntilde
ENCODING 209
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
C6
C6
E6
F6
DE
CE
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Ograve
ENCODING 210
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Oacute
ENCODING 211
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Ocircumflex
ENCODING 212
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Otilde
ENCODING 213
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Odieresis
ENCODING 214
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR multiply
ENCODING 215
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
6C
38
38
6C
C6
00
00
00
ENDCHAR

STARTCHAR Oslash
ENCODING 216
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C7
C6
CE
DE
F6
E6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Ugrave
ENCODING 217
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
C6
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Uacute
ENCODING 218
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
C6
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Ucircumflex
ENCODING 219
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Udieresis
ENCODING 220
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Yacute
ENCODING 221
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
0C
18
C3
C3
66
66
3C
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR Thorn
ENCODING 222
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
FC
C6
C6
C6
C6
FC
C0
C0
00
00
ENDCHAR

STARTCHAR germandbls
ENCODING 223
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
78
CC
CC
C8
FC
C6
C6
C6
E6
DC
00
00
ENDCHAR

STARTCHAR agrave
ENCODING 224
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR aacute
ENCODING 225
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR acircumflex
ENCODING 226
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR atilde
ENCODING 227
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
76
DC
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR adieresis
ENCODING 228
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR aring
ENCODING 229
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
38
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR ae
ENCODING 230
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
6C
16
16
7E
D0
D0
6C
00
00
ENDCHAR

STARTCHAR ccedilla
ENCODING 231
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
C0
C0
C6
7C
30
60
ENDCHAR

STARTCHAR egrave
ENCODING 232
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR eacute
ENCODING 233
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR ecircumflex
ENCODING 234
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR edieresis
ENCODING 235
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR igrave
ENCODING 236
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR iacute
ENCODING 237
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
0C
18
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR icircumflex
ENCODING 238
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR idieresis
ENCODING 239
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR eth
ENCODING 240
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
68
30
58
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR ntilde
ENCODING 241
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
76
DC
00
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR ograve
ENCODING 242
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR oacute
ENCODING 243
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR ocircumflex
ENCODING 244
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR otilde
ENCODING 245
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
76
DC
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR odieresis
ENCODING 246
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR divide
ENCODING 247
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
18
18
00
7E
00
18
18
00
00
00
ENDCHAR

STARTCHAR oslash
ENCODING 248
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
3D
67
6E
7E
76
E6
BC
00
00
ENDCHAR

STARTCHAR ugrave
ENCODING 249
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR uacute
ENCODING 250
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR ucircumflex
ENCODING 251
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR udieresis
ENCODING 252
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR yacute
ENCODING 253
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR thorn
ENCODING 254
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
FC
C6
C6
C6
C6
C6
FC
C0
C0
ENDCHAR

STARTCHAR ydieresis
ENCODING 255
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR Amacron
ENCODING 256
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7C
00
7C
C6
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR amacron
ENCODING 257
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Abreve
ENCODING 258
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR abreve
ENCODING 259
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Aogonek
ENCODING 260
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
FE
C6
C6
C6
C6
0C
07
ENDCHAR

STARTCHAR aogonek
ENCODING 261
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
06
7E
C6
C6
C6
7E
0C
07
ENDCHAR

STARTCHAR Cacute
ENCODING 262
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
7C
C6
C6
C0
C0
C0
C6
C6
7C
00
00
ENDCHAR

STARTCHAR cacute
ENCODING 263
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7C
C6
C0
C0
C0
C6
7C
00
00
ENDCHAR

STARTCHAR Ccircumflex
ENCODING 264
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
7C
C6
C6
C0
C0
C0
C6
C6
7C
00
00
ENDCHAR

STARTCHAR ccircumflex
ENCODING 265
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
7C
C6
C0
C0
C0
C6
7C
00
00
ENDCHAR

STARTCHAR Cdotaccent
ENCODING 266
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
00
7C
C6
C6
C0
C0
C0
C6
C6
7C
00
00
ENDCHAR

STARTCHAR cdotaccent
ENCODING 267
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
7C
C6
C0
C0
C0
C6
7C
00
00
ENDCHAR

STARTCHAR Ccaron
ENCODING 268
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
7C
C6
C6
C0
C0
C0
C6
C6
7C
00
00
ENDCHAR

STARTCHAR ccaron
ENCODING 269
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7C
C6
C0
C0
C0
C6
7C
00
00
ENDCHAR

STARTCHAR Dcaron
ENCODING 270
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
F8
CC
C6
C6
C6
C6
C6
CC
F8
00
00
ENDCHAR

STARTCHAR dcaron
ENCODING 271
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
06
06
06
7E
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Dcroat
ENCODING 272
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
78
6C
66
66
F6
66
66
66
6C
78
00
00
ENDCHAR

STARTCHAR dcroat
ENCODING 273
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
1F
06
7E
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Emacron
ENCODING 274
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7C
00
FE
C0
C0
C0
F8
C0
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR emacron
ENCODING 275
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR Ebreve
ENCODING 276
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR ebreve
ENCODING 277
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR Edotaccent
ENCODING 278
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR edotaccent
ENCODING 279
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR Eogonek
ENCODING 280
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
F8
C0
C0
C0
C0
FE
0C
07
ENDCHAR

STARTCHAR eogonek
ENCODING 281
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
FE
C0
C0
7C
18
0E
ENDCHAR

STARTCHAR Ecaron
ENCODING 282
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR ecaron
ENCODING 283
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR Gcircumflex
ENCODING 284
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
7C
C6
C6
C0
DE
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR gcircumflex
ENCODING 285
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
7E
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR Gbreve
ENCODING 286
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
7C
C6
C6
C0
DE
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR gbreve
ENCODING 287
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7E
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR Gdotaccent
ENCODING 288
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
00
7C
C6
C6
C0
DE
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR gdotaccent
ENCODING 289
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
7E
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR Gcommaaccent
ENCODING 290
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C0
C0
DE
C6
C6
C6
7C
30
60
ENDCHAR

STARTCHAR gcommaaccent
ENCODING 291
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
0C
18
18
00
7E
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR Hcircumflex
ENCODING 292
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
C6
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR hcircumflex
ENCODING 293
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
1C
36
C0
C0
C0
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Hbar
ENCODING 294
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
66
FF
66
66
7E
66
66
66
66
66
00
00
ENDCHAR

STARTCHAR hbar
ENCODING 295
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
60
F8
60
7C
66
66
66
66
66
66
00
00
ENDCHAR

STARTCHAR Itilde
ENCODING 296
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR itilde
ENCODING 297
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
76
DC
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Imacron
ENCODING 298
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7E
00
3C
18
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR imacron
ENCODING 299
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Ibreve
ENCODING 300
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR ibreve
ENCODING 301
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Iogonek
ENCODING 302
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
18
18
18
18
18
18
18
18
3C
18
0E
ENDCHAR

STARTCHAR iogonek
ENCODING 303
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
38
18
18
18
18
18
3C
18
0E
ENDCHAR

STARTCHAR Idotaccent
ENCODING 304
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR dotlessi
ENCODING 305
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR IJ
ENCODING 306
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
CF
C6
C6
C6
C6
C6
C6
F6
F6
DC
00
00
ENDCHAR

STARTCHAR ij
ENCODING 307
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
00
CE
C6
C6
C6
C6
C6
F6
36
1C
ENDCHAR

STARTCHAR Jcircumflex
ENCODING 308
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
1C
36
00
1E
0C
0C
0C
0C
0C
CC
CC
78
00
00
ENDCHAR

STARTCHAR jcircumflex
ENCODING 309
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
0E
1B
00
0E
06
06
06
06
06
66
66
3C
ENDCHAR

STARTCHAR Kcommaaccent
ENCODING 310
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
CC
D8
F0
F0
D8
CC
C6
F6
30
60
ENDCHAR

STARTCHAR kcommaaccent
ENCODING 311
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
C6
CC
D8
F0
D8
CC
F6
30
60
ENDCHAR

STARTCHAR kgreenlandic
ENCODING 312
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
CC
D8
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR Lacute
ENCODING 313
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
00
C0
C0
C0
C0
C0
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR lacute
ENCODING 314
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
0C
18
00
38
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Lcommaaccent
ENCODING 315
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
C0
C0
C0
C0
C0
C0
FE
30
60
ENDCHAR

STARTCHAR lcommaaccent
ENCODING 316
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
18
18
18
18
18
18
18
18
3C
18
30
ENDCHAR

STARTCHAR Lcaron
ENCODING 317
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
C0
C0
C0
C0
C0
C0
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR lcaron
ENCODING 318
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
38
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Ldot
ENCODING 319
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
C0
CC
CC
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR ldot
ENCODING 320
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
18
18
18
1B
1B
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Lslash
ENCODING 321
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
60
60
60
60
70
E0
60
60
60
7F
00
00
ENDCHAR

STARTCHAR lslash
ENCODING 322
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
18
18
18
1C
38
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Nacute
ENCODING 323
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
C6
C6
C6
E6
F6
DE
CE
C6
C6
C6
00
00
ENDCHAR

STARTCHAR nacute
ENCODING 324
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Ncommaaccent
ENCODING 325
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
E6
F6
DE
CE
C6
C6
F6
30
60
ENDCHAR

STARTCHAR ncommaaccent
ENCODING 326
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
C6
C6
C6
C6
C6
F6
30
60
ENDCHAR

STARTCHAR Ncaron
ENCODING 327
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
C6
C6
C6
E6
F6
DE
CE
C6
C6
C6
00
00
ENDCHAR

STARTCHAR ncaron
ENCODING 328
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR napostrophe
ENCODING 329
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
60
60
C0
00
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Eng
ENCODING 330
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
E6
F6
DE
CE
C6
C6
C6
06
1C
ENDCHAR

STARTCHAR eng
ENCODING 331
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
C6
C6
C6
C6
C6
C6
06
1C
ENDCHAR

STARTCHAR Omacron
ENCODING 332
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7C
00
7C
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR omacron
ENCODING 333
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Obreve
ENCODING 334
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR obreve
ENCODING 335
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Ohungarumlaut
ENCODING 336
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
36
6C
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR ohungarumlaut
ENCODING 337
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
36
6C
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR OE
ENCODING 338
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
D8
D8
D8
DE
D8
D8
D8
D8
7E
00
00
ENDCHAR

STARTCHAR oe
ENCODING 339
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
D6
D6
DE
D0
D0
7C
00
00
ENDCHAR

STARTCHAR Racute
ENCODING 340
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
FC
C6
C6
C6
FC
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR racute
ENCODING 341
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
DE
F0
E0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR Rcommaaccent
ENCODING 342
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
C6
FC
F0
D8
CC
F6
30
60
ENDCHAR

STARTCHAR rcommaaccent
ENCODING 343
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
DE
F0
E0
C0
C0
C0
E0
60
C0
ENDCHAR

STARTCHAR Rcaron
ENCODING 344
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
FC
C6
C6
C6
FC
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR rcaron
ENCODING 345
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
DE
F0
E0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR Sacute
ENCODING 346
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
7C
C6
C0
C0
7C
06
06
C6
7C
00
00
ENDCHAR

STARTCHAR sacute
ENCODING 347
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
0C
18
00
7E
C0
C0
7C
06
06
FC
00
00
ENDCHAR

STARTCHAR Scircumflex
ENCODING 348
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
7C
C6
C0
C0
7C
06
06
C6
7C
00
00
ENDCHAR

STARTCHAR scircumflex
ENCODING 349
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
7E
C0
C0
7C
06
06
FC
00
00
ENDCHAR

STARTCHAR Scedilla
ENCODING 350
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C0
C0
7C
06
06
C6
C6
7C
30
60
ENDCHAR

STARTCHAR scedilla
ENCODING 351
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C0
C0
7C
06
06
FC
30
60
ENDCHAR

STARTCHAR Scaron
ENCODING 352
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
7C
C6
C0
C0
7C
06
06
C6
7C
00
00
ENDCHAR

STARTCHAR scaron
ENCODING 353
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7E
C0
C0
7C
06
06
FC
00
00
ENDCHAR

STARTCHAR Tcedilla
ENCODING 354
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
18
18
18
18
18
18
18
18
1C
0C
18
ENDCHAR

STARTCHAR tcedilla
ENCODING 355
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
30
30
FC
30
30
30
30
30
1E
0C
18
ENDCHAR

STARTCHAR Tcaron
ENCODING 356
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
FF
18
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR tcaron
ENCODING 357
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
30
30
FC
30
30
30
30
30
1E
00
00
ENDCHAR

STARTCHAR Tbar
ENCODING 358
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
18
18
18
7E
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR tbar
ENCODING 359
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
30
30
FC
30
78
30
30
30
1E
00
00
ENDCHAR

STARTCHAR Utilde
ENCODING 360
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR utilde
ENCODING 361
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
76
DC
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Umacron
ENCODING 362
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7C
00
C6
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR umacron
ENCODING 363
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Ubreve
ENCODING 364
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
C6
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR ubreve
ENCODING 365
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Uring
ENCODING 366
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
38
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uring
ENCODING 367
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
38
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Uhungarumlaut
ENCODING 368
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
36
6C
00
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uhungarumlaut
ENCODING 369
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
36
6C
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR Uogonek
ENCODING 370
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
C6
C6
C6
C6
7C
18
0E
ENDCHAR

STARTCHAR uogonek
ENCODING 371
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7E
0C
07
ENDCHAR

STARTCHAR Wcircumflex
ENCODING 372
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
C6
C6
C6
C6
D6
FE
EE
C6
82
00
00
ENDCHAR

STARTCHAR wcircumflex
ENCODING 373
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
38
6C
00
C6
C6
D6
D6
D6
D6
7C
00
00
ENDCHAR

STARTCHAR Ydieresis
ENCODING 376
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
66
66
00
C3
C3
66
66
3C
18
18
18
18
00
00
ENDCHAR

STARTCHAR Zacute
ENCODING 377
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
FE
06
0C
18
30
60
C0
C0
FE
00
00
ENDCHAR

STARTCHAR zacute
ENCODING 378
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
FE
0C
18
30
60
C0
FE
00
00
ENDCHAR

STARTCHAR Zdotaccent
ENCODING 379
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
00
FE
06
0C
18
30
60
C0
C0
FE
00
00
ENDCHAR

STARTCHAR zdotaccent
ENCODING 380
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
FE
0C
18
30
60
C0
FE
00
00
ENDCHAR

STARTCHAR Zcaron
ENCODING 381
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
FE
06
0C
18
30
60
C0
C0
FE
00
00
ENDCHAR

STARTCHAR zcaron
ENCODING 382
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
FE
0C
18
30
60
C0
FE
00
00
ENDCHAR

STARTCHAR longs
ENCODING 383
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
1E
30
30
30
30
30
30
30
30
30
00
00
ENDCHAR

STARTCHAR uni0186
ENCODING 390
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
06
06
06
06
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni018E
ENCODING 398
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
06
06
06
3E
06
06
06
06
FE
00
00
ENDCHAR

STARTCHAR Schwa
ENCODING 399
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
06
06
FE
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni0190
ENCODING 400
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C0
78
C0
C0
C6
C6
7C
00
00
ENDCHAR

STARTCHAR florin
ENCODING 402
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
0E
1B
18
18
7E
18
18
18
18
18
D8
70
ENDCHAR

STARTCHAR uni019D
ENCODING 413
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
66
66
66
76
7E
6E
66
66
66
66
60
C0
ENDCHAR

STARTCHAR uni019E
ENCODING 414
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
C6
C6
C6
C6
C6
C6
06
06
ENDCHAR

STARTCHAR Ezh
ENCODING 439
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
06
0C
18
3C
06
06
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Scommaaccent
ENCODING 536
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C0
C0
7C
06
06
C6
C6
7C
30
60
ENDCHAR

STARTCHAR scommaaccent
ENCODING 537
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C0
C0
7C
06
06
FC
30
60
ENDCHAR

STARTCHAR Tcommaaccent
ENCODING 538
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
18
18
18
18
18
18
18
18
1C
0C
18
ENDCHAR

STARTCHAR tcommaaccent
ENCODING 539
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
30
30
FC
30
30
30
30
30
1E
06
0C
ENDCHAR

STARTCHAR uni0232
ENCODING 562
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7E
00
C3
C3
66
66
3C
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR uni0233
ENCODING 563
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR dotlessj
ENCODING 567
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
0E
06
06
06
06
06
66
66
3C
ENDCHAR

STARTCHAR uni0254
ENCODING 596
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
06
06
06
C6
7C
00
00
ENDCHAR

STARTCHAR uni0258
ENCODING 600
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
FE
06
06
7C
00
00
ENDCHAR

STARTCHAR schwa
ENCODING 601
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
06
06
FE
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni025B
ENCODING 603
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
78
C0
C6
7C
00
00
ENDCHAR

STARTCHAR uni0272
ENCODING 626
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
66
66
66
66
66
66
60
C0
ENDCHAR

STARTCHAR ezh
ENCODING 658
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
06
0C
18
3C
06
06
C6
7C
ENDCHAR

STARTCHAR commaturnedmod
ENCODING 699
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
30
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR afii57929
ENCODING 700
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
30
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR afii64937
ENCODING 701
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
30
18
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR circumflex
ENCODING 710
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR caron
ENCODING 711
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR breve
ENCODING 728
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR dotaccent
ENCODING 729
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR ogonek
ENCODING 731
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
06
0C
07
ENDCHAR

STARTCHAR tilde
ENCODING 732
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR hungarumlaut
ENCODING 733
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
36
6C
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR gravecomb
ENCODING 768
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR acutecomb
ENCODING 769
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni0302
ENCODING 770
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
6C
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR tildecomb
ENCODING 771
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni0306
ENCODING 774
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni030C
ENCODING 780
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni0329
ENCODING 809
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
18
18
ENDCHAR

STARTCHAR tonos
ENCODING 900
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR dieresistonos
ENCODING 901
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
6C
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR Alphatonos
ENCODING 902
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
7C
C6
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR anoteleia
ENCODING 903
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
18
18
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR Epsilontonos
ENCODING 904
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR Etatonos
ENCODING 905
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
00
C6
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Iotatonos
ENCODING 906
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
3C
18
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Omicrontonos
ENCODING 908
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
7C
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Upsilontonos
ENCODING 910
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
00
C3
C3
66
66
3C
18
18
18
18
00
00
ENDCHAR

STARTCHAR Omegatonos
ENCODING 911
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
C0
7C
C6
C6
C6
C6
C6
C6
6C
6C
EE
00
00
ENDCHAR

STARTCHAR iotadieresistonos
ENCODING 912
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
D8
D8
00
70
30
30
30
30
30
1C
00
00
ENDCHAR

STARTCHAR Alpha
ENCODING 913
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Beta
ENCODING 914
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
FC
C6
C6
C6
C6
FC
00
00
ENDCHAR

STARTCHAR Gamma
ENCODING 915
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
C0
C0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR Delta
ENCODING 916
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
10
10
38
38
6C
6C
6C
C6
C6
FE
00
00
ENDCHAR

STARTCHAR Epsilon
ENCODING 917
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
F8
C0
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR Zeta
ENCODING 918
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
06
06
0C
18
30
60
C0
C0
FE
00
00
ENDCHAR

STARTCHAR Eta
ENCODING 919
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
FE
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Theta
ENCODING 920
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
D6
D6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Iota
ENCODING 921
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
18
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Kappa
ENCODING 922
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
CC
D8
F0
F0
D8
CC
C6
C6
00
00
ENDCHAR

STARTCHAR Lambda
ENCODING 923
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
10
10
38
38
6C
6C
6C
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Mu
ENCODING 924
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
82
C6
EE
FE
D6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Nu
ENCODING 925
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
E6
F6
DE
CE
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Xi
ENCODING 926
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
00
00
00
7C
00
00
00
00
FE
00
00
ENDCHAR

STARTCHAR Omicron
ENCODING 927
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR Pi
ENCODING 928
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C6
C6
C6
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR Rho
ENCODING 929
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
C6
FC
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR Sigma
ENCODING 931
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
60
30
18
18
30
60
C0
FE
00
00
ENDCHAR

STARTCHAR Tau
ENCODING 932
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
18
18
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR Upsilon
ENCODING 933
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C3
C3
66
66
3C
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR Phi
ENCODING 934
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
10
7C
D6
D6
D6
D6
D6
D6
7C
10
00
00
ENDCHAR

STARTCHAR Chi
ENCODING 935
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
6C
6C
38
38
6C
6C
C6
C6
00
00
ENDCHAR

STARTCHAR Psi
ENCODING 936
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
D6
D6
D6
D6
D6
D6
D6
7C
10
10
00
00
ENDCHAR

STARTCHAR Omega
ENCODING 937
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
C6
C6
6C
6C
EE
00
00
ENDCHAR

STARTCHAR Iotadieresis
ENCODING 938
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
66
66
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR Upsilondieresis
ENCODING 939
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
66
66
00
C3
C3
66
66
3C
18
18
18
18
00
00
ENDCHAR

STARTCHAR alphatonos
ENCODING 940
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7A
CE
CC
CC
CC
CE
7A
00
00
ENDCHAR

STARTCHAR epsilontonos
ENCODING 941
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7C
C6
C0
78
C0
C6
7C
00
00
ENDCHAR

STARTCHAR etatonos
ENCODING 942
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
FC
C6
C6
C6
C6
C6
C6
06
06
ENDCHAR

STARTCHAR iotatonos
ENCODING 943
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
70
30
30
30
30
30
1C
00
00
ENDCHAR

STARTCHAR upsilondieresistonos
ENCODING 944
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
6C
00
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR alpha
ENCODING 945
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7A
CE
CC
CC
CC
CE
7A
00
00
ENDCHAR

STARTCHAR beta
ENCODING 946
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
78
CC
CC
C8
FC
C6
C6
C6
C6
FC
C0
C0
ENDCHAR

STARTCHAR gamma
ENCODING 947
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C3
C3
66
66
3C
3C
18
18
18
ENDCHAR

STARTCHAR delta
ENCODING 948
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
30
18
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR epsilon
ENCODING 949
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
78
C0
C6
7C
00
00
ENDCHAR

STARTCHAR zeta
ENCODING 950
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
0C
18
30
60
C0
C0
C0
C0
7C
06
0C
ENDCHAR

STARTCHAR eta
ENCODING 951
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
C6
C6
C6
C6
C6
C6
06
06
ENDCHAR

STARTCHAR theta
ENCODING 952
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
66
66
66
7E
66
66
66
66
3C
00
00
ENDCHAR

STARTCHAR iota
ENCODING 953
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
70
30
30
30
30
30
1C
00
00
ENDCHAR

STARTCHAR kappa
ENCODING 954
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
CC
D8
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR lambda
ENCODING 955
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
30
18
18
3C
3C
66
66
C3
C3
00
00
ENDCHAR

STARTCHAR mugreek
ENCODING 956
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
CE
F6
C0
C0
ENDCHAR

STARTCHAR nu
ENCODING 957
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
6C
6C
38
38
00
00
ENDCHAR

STARTCHAR xi
ENCODING 958
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
C0
C0
C0
7C
C0
C0
C0
C0
7C
06
0C
ENDCHAR

STARTCHAR omicron
ENCODING 959
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR pi
ENCODING 960
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR rho
ENCODING 961
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
C6
C6
C6
FC
C0
C0
ENDCHAR

STARTCHAR sigma1
ENCODING 962
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
C0
C0
C0
7C
06
0C
ENDCHAR

STARTCHAR sigma
ENCODING 963
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
3F
66
66
66
66
66
3C
00
00
ENDCHAR

STARTCHAR tau
ENCODING 964
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FF
18
18
18
18
18
0E
00
00
ENDCHAR

STARTCHAR upsilon
ENCODING 965
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR phi
ENCODING 966
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
4C
D6
D6
D6
D6
D6
7C
10
10
ENDCHAR

STARTCHAR chi
ENCODING 967
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
6C
6C
38
6C
6C
C6
C6
ENDCHAR

STARTCHAR psi
ENCODING 968
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
D6
D6
D6
D6
D6
D6
7C
10
10
ENDCHAR

STARTCHAR omega
ENCODING 969
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
44
C6
D6
D6
D6
FE
6C
00
00
ENDCHAR

STARTCHAR iotadieresis
ENCODING 970
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
D8
D8
00
70
30
30
30
30
30
1C
00
00
ENDCHAR

STARTCHAR upsilondieresis
ENCODING 971
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR omicrontonos
ENCODING 972
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR upsilontonos
ENCODING 973
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR omegatonos
ENCODING 974
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
44
C6
D6
D6
D6
FE
6C
00
00
ENDCHAR

STARTCHAR uni03F3
ENCODING 1011
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
06
00
0E
06
06
06
06
06
66
66
3C
ENDCHAR

STARTCHAR uni03F4
ENCODING 1012
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni0400
ENCODING 1024
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR afii10023
ENCODING 1025
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR afii10051
ENCODING 1026
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
F0
60
60
7C
66
66
66
66
66
6C
00
00
ENDCHAR

STARTCHAR afii10052
ENCODING 1027
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
00
FE
C0
C0
C0
C0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR afii10053
ENCODING 1028
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C0
C0
F8
C0
C0
C0
C6
7C
00
00
ENDCHAR

STARTCHAR afii10054
ENCODING 1029
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C0
C0
7C
06
06
C6
C6
7C
00
00
ENDCHAR

STARTCHAR afii10055
ENCODING 1030
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
18
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR afii10056
ENCODING 1031
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
66
66
00
3C
18
18
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR afii10057
ENCODING 1032
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
1E
0C
0C
0C
0C
0C
0C
CC
CC
78
00
00
ENDCHAR

STARTCHAR afii10058
ENCODING 1033
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
70
D0
DC
D6
D6
D6
D6
D6
9C
00
00
ENDCHAR

STARTCHAR afii10059
ENCODING 1034
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
D0
D0
D0
DC
F6
D6
D6
D6
D6
DC
00
00
ENDCHAR

STARTCHAR afii10060
ENCODING 1035
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
F0
60
60
7C
66
66
66
66
66
66
00
00
ENDCHAR

STARTCHAR afii10061
ENCODING 1036
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
30
C6
C6
CC
D8
F0
F0
D8
CC
C6
C6
00
00
ENDCHAR

STARTCHAR uni040D
ENCODING 1037
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
30
18
C6
C6
C6
CE
DE
F6
E6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10062
ENCODING 1038
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
C6
C6
C6
C6
C6
7E
06
06
06
7C
00
00
ENDCHAR

STARTCHAR afii10145
ENCODING 1039
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
C6
C6
C6
C6
FE
38
38
ENDCHAR

STARTCHAR afii10017
ENCODING 1040
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10018
ENCODING 1041
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C0
C0
FC
C6
C6
C6
C6
C6
FC
00
00
ENDCHAR

STARTCHAR afii10019
ENCODING 1042
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
FC
C6
C6
C6
C6
FC
00
00
ENDCHAR

STARTCHAR afii10020
ENCODING 1043
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
C0
C0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR afii10021
ENCODING 1044
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3E
66
66
66
66
66
66
66
66
FF
C3
00
ENDCHAR

STARTCHAR afii10022
ENCODING 1045
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
F8
C0
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR afii10024
ENCODING 1046
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
D6
D6
D6
7C
38
7C
D6
D6
D6
D6
00
00
ENDCHAR

STARTCHAR afii10025
ENCODING 1047
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
06
3C
06
06
C6
C6
7C
00
00
ENDCHAR

STARTCHAR afii10026
ENCODING 1048
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
CE
DE
F6
E6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10027
ENCODING 1049
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
C6
C6
C6
CE
DE
F6
E6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10028
ENCODING 1050
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
CC
D8
F0
F0
D8
CC
C6
C6
00
00
ENDCHAR

STARTCHAR afii10029
ENCODING 1051
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
1E
36
66
66
66
66
66
66
66
C6
00
00
ENDCHAR

STARTCHAR afii10030
ENCODING 1052
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
82
C6
EE
FE
D6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10031
ENCODING 1053
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
FE
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10032
ENCODING 1054
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR afii10033
ENCODING 1055
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C6
C6
C6
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10034
ENCODING 1056
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FC
C6
C6
C6
C6
FC
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR afii10035
ENCODING 1057
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C0
C0
C0
C0
C6
C6
7C
00
00
ENDCHAR

STARTCHAR afii10036
ENCODING 1058
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
18
18
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR afii10037
ENCODING 1059
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
7E
06
06
06
7C
00
00
ENDCHAR

STARTCHAR afii10038
ENCODING 1060
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
10
7C
D6
D6
D6
D6
D6
D6
D6
D6
7C
10
00
ENDCHAR

STARTCHAR afii10039
ENCODING 1061
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
6C
6C
38
38
6C
6C
C6
C6
00
00
ENDCHAR

STARTCHAR afii10040
ENCODING 1062
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
C6
C6
C6
C6
7F
03
03
ENDCHAR

STARTCHAR afii10041
ENCODING 1063
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
7E
06
06
06
06
00
00
ENDCHAR

STARTCHAR afii10042
ENCODING 1064
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
D6
D6
D6
D6
D6
D6
D6
D6
D6
7E
00
00
ENDCHAR

STARTCHAR afii10043
ENCODING 1065
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
D6
D6
D6
D6
D6
D6
D6
D6
D6
7F
03
03
ENDCHAR

STARTCHAR afii10044
ENCODING 1066
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
E0
60
60
7C
66
66
66
66
66
7C
00
00
ENDCHAR

STARTCHAR afii10045
ENCODING 1067
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
E6
D6
D6
D6
D6
D6
E6
00
00
ENDCHAR

STARTCHAR afii10046
ENCODING 1068
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
60
60
60
7C
66
66
66
66
66
7C
00
00
ENDCHAR

STARTCHAR afii10047
ENCODING 1069
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
06
06
3E
06
06
06
C6
7C
00
00
ENDCHAR

STARTCHAR afii10048
ENCODING 1070
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
CC
D6
D6
D6
D6
F6
D6
D6
D6
CC
00
00
ENDCHAR

STARTCHAR afii10049
ENCODING 1071
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
C6
C6
C6
C6
7E
1E
36
66
C6
00
00
ENDCHAR

STARTCHAR afii10065
ENCODING 1072
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR afii10066
ENCODING 1073
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C0
C0
FC
C6
C6
C6
C6
C6
FC
00
00
ENDCHAR

STARTCHAR afii10067
ENCODING 1074
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
78
CC
CC
C8
FC
C6
C6
C6
C6
FC
00
00
ENDCHAR

STARTCHAR afii10068
ENCODING 1075
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
06
06
7C
C0
C0
7C
00
00
ENDCHAR

STARTCHAR afii10069
ENCODING 1076
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR afii10070
ENCODING 1077
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR afii10072
ENCODING 1078
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
D6
D6
7C
38
7C
D6
D6
00
00
ENDCHAR

STARTCHAR afii10073
ENCODING 1079
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
06
3C
06
C6
7C
00
00
ENDCHAR

STARTCHAR afii10074
ENCODING 1080
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR afii10075
ENCODING 1081
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR afii10076
ENCODING 1082
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
CC
D8
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR afii10077
ENCODING 1083
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
3E
66
66
66
66
66
C6
00
00
ENDCHAR

STARTCHAR afii10078
ENCODING 1084
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
EE
FE
D6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10079
ENCODING 1085
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
FE
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10080
ENCODING 1086
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR afii10081
ENCODING 1087
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR afii10082
ENCODING 1088
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FC
C6
C6
C6
C6
C6
FC
C0
C0
ENDCHAR

STARTCHAR afii10083
ENCODING 1089
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
C0
C0
C6
7C
00
00
ENDCHAR

STARTCHAR afii10084
ENCODING 1090
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FF
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR afii10085
ENCODING 1091
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR afii10086
ENCODING 1092
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
10
7C
D6
D6
D6
D6
D6
7C
10
00
ENDCHAR

STARTCHAR afii10087
ENCODING 1093
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
6C
38
6C
C6
C6
00
00
ENDCHAR

STARTCHAR afii10088
ENCODING 1094
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7F
03
03
ENDCHAR

STARTCHAR afii10089
ENCODING 1095
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
7E
06
06
06
00
00
ENDCHAR

STARTCHAR afii10090
ENCODING 1096
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
D6
D6
D6
D6
D6
D6
7E
00
00
ENDCHAR

STARTCHAR afii10091
ENCODING 1097
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
D6
D6
D6
D6
D6
D6
7F
03
03
ENDCHAR

STARTCHAR afii10092
ENCODING 1098
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
E0
60
7C
66
66
66
7C
00
00
ENDCHAR

STARTCHAR afii10093
ENCODING 1099
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
E6
D6
D6
D6
E6
00
00
ENDCHAR

STARTCHAR afii10094
ENCODING 1100
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
60
60
7C
66
66
66
7C
00
00
ENDCHAR

STARTCHAR afii10095
ENCODING 1101
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
06
3E
06
C6
7C
00
00
ENDCHAR

STARTCHAR afii10096
ENCODING 1102
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
CC
D6
D6
F6
D6
D6
CC
00
00
ENDCHAR

STARTCHAR afii10097
ENCODING 1103
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C6
C6
7E
36
66
C6
00
00
ENDCHAR

STARTCHAR uni0450
ENCODING 1104
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR afii10071
ENCODING 1105
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR afii10099
ENCODING 1106
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
60
F8
60
7C
66
66
66
66
66
66
06
1C
ENDCHAR

STARTCHAR afii10100
ENCODING 1107
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
7C
06
06
7C
C0
C0
7C
00
00
ENDCHAR

STARTCHAR afii10101
ENCODING 1108
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
F8
C0
C6
7C
00
00
ENDCHAR

STARTCHAR afii10102
ENCODING 1109
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7E
C0
C0
7C
06
06
FC
00
00
ENDCHAR

STARTCHAR afii10103
ENCODING 1110
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR afii10104
ENCODING 1111
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
38
18
18
18
18
18
3C
00
00
ENDCHAR

STARTCHAR afii10105
ENCODING 1112
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
06
00
0E
06
06
06
06
06
66
66
3C
ENDCHAR

STARTCHAR afii10106
ENCODING 1113
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
70
D0
DC
D6
D6
D6
9C
00
00
ENDCHAR

STARTCHAR afii10107
ENCODING 1114
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
D0
D0
DC
F6
D6
D6
DC
00
00
ENDCHAR

STARTCHAR afii10108
ENCODING 1115
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
60
F8
60
7C
66
66
66
66
66
66
00
00
ENDCHAR

STARTCHAR afii10109
ENCODING 1116
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
30
00
C6
CC
D8
F0
D8
CC
C6
00
00
ENDCHAR

STARTCHAR uni045D
ENCODING 1117
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
30
18
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR afii10110
ENCODING 1118
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR afii10193
ENCODING 1119
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
FE
38
38
ENDCHAR

STARTCHAR afii10050
ENCODING 1168
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
06
06
FE
C0
C0
C0
C0
C0
C0
C0
C0
C0
00
00
ENDCHAR

STARTCHAR afii10098
ENCODING 1169
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
60
60
7C
06
06
7C
C0
C0
7C
00
00
ENDCHAR

STARTCHAR uni0492
ENCODING 1170
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7F
60
60
60
FC
60
60
60
60
60
00
00
ENDCHAR

STARTCHAR uni0493
ENCODING 1171
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7F
60
60
FC
60
60
60
00
00
ENDCHAR

STARTCHAR uni0494
ENCODING 1172
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
C0
FC
C6
C6
C6
C6
06
0C
ENDCHAR

STARTCHAR uni0495
ENCODING 1173
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
C0
C0
F8
CC
CC
CC
0C
18
ENDCHAR

STARTCHAR uni0496
ENCODING 1174
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
D6
D6
D6
7C
38
7C
D6
D6
D6
D7
03
03
ENDCHAR

STARTCHAR uni0497
ENCODING 1175
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
D6
D6
7C
38
7C
D6
D7
03
03
ENDCHAR

STARTCHAR uni0498
ENCODING 1176
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
06
3C
06
06
C6
C6
7C
30
30
ENDCHAR

STARTCHAR uni0499
ENCODING 1177
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
06
3C
06
C6
7C
30
30
ENDCHAR

STARTCHAR uni049A
ENCODING 1178
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
CC
D8
F0
F0
D8
CC
C6
C7
03
03
ENDCHAR

STARTCHAR uni049B
ENCODING 1179
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
CC
D8
F0
D8
CC
C7
03
03
ENDCHAR

STARTCHAR uni049C
ENCODING 1180
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
D6
DC
F8
F8
DC
D6
C6
C6
00
00
ENDCHAR

STARTCHAR uni049D
ENCODING 1181
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
D6
DC
F8
DC
D6
C6
00
00
ENDCHAR

STARTCHAR uni04A0
ENCODING 1184
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
E3
E3
66
6C
78
78
6C
66
63
63
00
00
ENDCHAR

STARTCHAR uni04A1
ENCODING 1185
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
E3
66
6C
78
6C
66
63
00
00
ENDCHAR

STARTCHAR uni04A2
ENCODING 1186
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
FE
C6
C6
C6
C6
C7
03
03
ENDCHAR

STARTCHAR uni04A3
ENCODING 1187
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
FE
C6
C6
C7
03
03
ENDCHAR

STARTCHAR uni04A4
ENCODING 1188
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
CF
CC
CC
CC
FC
CC
CC
CC
CC
CC
00
00
ENDCHAR

STARTCHAR uni04A5
ENCODING 1189
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
CF
CC
CC
FC
CC
CC
CC
00
00
ENDCHAR

STARTCHAR uni04AA
ENCODING 1194
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C0
C0
C0
C0
C6
C6
7C
30
30
ENDCHAR

STARTCHAR uni04AB
ENCODING 1195
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C0
C0
C0
C6
7C
30
30
ENDCHAR

STARTCHAR uni04AE
ENCODING 1198
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C3
C3
66
66
3C
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR uni04AF
ENCODING 1199
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C3
C3
66
66
3C
3C
18
18
18
ENDCHAR

STARTCHAR uni04B0
ENCODING 1200
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C3
C3
66
66
3C
18
7E
18
18
18
00
00
ENDCHAR

STARTCHAR uni04B1
ENCODING 1201
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C3
C3
66
66
3C
3C
18
7E
18
ENDCHAR

STARTCHAR uni04B2
ENCODING 1202
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
6C
6C
38
38
6C
6C
C6
C7
03
03
ENDCHAR

STARTCHAR uni04B3
ENCODING 1203
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
6C
38
6C
C6
C7
03
03
ENDCHAR

STARTCHAR uni04B6
ENCODING 1206
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
7E
06
06
06
07
03
03
ENDCHAR

STARTCHAR uni04B7
ENCODING 1207
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
7E
06
06
07
03
03
ENDCHAR

STARTCHAR uni04B8
ENCODING 1208
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
D6
D6
7E
16
16
06
06
00
00
ENDCHAR

STARTCHAR uni04B9
ENCODING 1209
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
D6
D6
7E
16
06
06
00
00
ENDCHAR

STARTCHAR uni04BA
ENCODING 1210
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
C0
FC
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR uni04BB
ENCODING 1211
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C0
C0
FC
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR uni04D0
ENCODING 1232
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR uni04D1
ENCODING 1233
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR uni04D2
ENCODING 1234
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR uni04D3
ENCODING 1235
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
06
7E
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR uni04D4
ENCODING 1236
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
D8
D8
D8
FE
D8
D8
D8
D8
DE
00
00
ENDCHAR

STARTCHAR uni04D5
ENCODING 1237
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
6C
16
16
7E
D0
D0
6C
00
00
ENDCHAR

STARTCHAR uni04D6
ENCODING 1238
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
38
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR uni04D7
ENCODING 1239
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
38
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR uni04D8
ENCODING 1240
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
06
06
FE
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR afii10846
ENCODING 1241
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
06
06
FE
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04DA
ENCODING 1242
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
06
06
FE
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04DB
ENCODING 1243
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
06
06
FE
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04DC
ENCODING 1244
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
D6
D6
D6
7C
38
7C
D6
D6
D6
00
00
ENDCHAR

STARTCHAR uni04DD
ENCODING 1245
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
D6
D6
7C
38
7C
D6
D6
00
00
ENDCHAR

STARTCHAR uni04DE
ENCODING 1246
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
C6
06
3C
06
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04DF
ENCODING 1247
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
C6
06
3C
06
C6
7C
00
00
ENDCHAR

STARTCHAR uni04E2
ENCODING 1250
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7C
00
C6
C6
C6
CE
DE
F6
E6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR uni04E3
ENCODING 1251
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR uni04E4
ENCODING 1252
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
C6
C6
C6
CE
DE
F6
E6
C6
C6
00
00
ENDCHAR

STARTCHAR uni04E5
ENCODING 1253
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
C6
C6
C6
C6
C6
C6
7E
00
00
ENDCHAR

STARTCHAR uni04E6
ENCODING 1254
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04E7
ENCODING 1255
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04E8
ENCODING 1256
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
FE
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04E9
ENCODING 1257
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
FE
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04EA
ENCODING 1258
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
C6
C6
FE
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04EB
ENCODING 1259
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
C6
C6
FE
C6
C6
7C
00
00
ENDCHAR

STARTCHAR uni04EC
ENCODING 1260
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
7C
C6
06
06
3E
06
06
C6
7C
00
00
ENDCHAR

STARTCHAR uni04ED
ENCODING 1261
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
7C
C6
06
3E
06
C6
7C
00
00
ENDCHAR

STARTCHAR uni04EE
ENCODING 1262
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
7C
00
C6
C6
C6
C6
C6
7E
06
06
06
7C
00
00
ENDCHAR

STARTCHAR uni04EF
ENCODING 1263
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
7C
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR uni04F0
ENCODING 1264
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
C6
C6
C6
C6
7E
06
06
06
7C
00
00
ENDCHAR

STARTCHAR uni04F1
ENCODING 1265
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR uni04F2
ENCODING 1266
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
36
6C
00
C6
C6
C6
C6
7E
06
06
06
7C
00
00
ENDCHAR

STARTCHAR uni04F3
ENCODING 1267
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
36
6C
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR uni04F4
ENCODING 1268
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
C6
C6
C6
C6
7E
06
06
06
06
00
00
ENDCHAR

STARTCHAR uni04F5
ENCODING 1269
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
C6
C6
C6
7E
06
06
06
00
00
ENDCHAR

STARTCHAR uni04F8
ENCODING 1272
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
00
C6
C6
C6
E6
D6
D6
D6
D6
E6
00
00
ENDCHAR

STARTCHAR uni04F9
ENCODING 1273
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
00
C6
C6
E6
D6
D6
D6
E6
00
00
ENDCHAR

STARTCHAR Klinebelow
ENCODING 7732
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
CC
D8
F0
F0
D8
CC
C6
C6
00
7C
ENDCHAR

STARTCHAR klinebelow
ENCODING 7733
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
C6
CC
D8
F0
D8
CC
C6
00
7C
ENDCHAR

STARTCHAR Edotbelow
ENCODING 7864
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C0
C0
C0
F8
C0
C0
C0
C0
FE
18
18
ENDCHAR

STARTCHAR edotbelow
ENCODING 7865
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
FE
C0
C0
7C
18
18
ENDCHAR

STARTCHAR Etilde
ENCODING 7868
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
FE
C0
C0
C0
F8
C0
C0
C0
FE
00
00
ENDCHAR

STARTCHAR etilde
ENCODING 7869
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
76
DC
00
7C
C6
C6
FE
C0
C0
7C
00
00
ENDCHAR

STARTCHAR uni1ECA
ENCODING 7882
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
18
18
18
18
18
18
18
18
3C
18
18
ENDCHAR

STARTCHAR uni1ECB
ENCODING 7883
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
00
38
18
18
18
18
18
3C
18
18
ENDCHAR

STARTCHAR Odotbelow
ENCODING 7884
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
C6
C6
C6
C6
7C
18
18
ENDCHAR

STARTCHAR odotbelow
ENCODING 7885
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
C6
C6
C6
C6
C6
7C
18
18
ENDCHAR

STARTCHAR uni1EE4
ENCODING 7908
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C6
C6
C6
C6
C6
C6
C6
C6
C6
7C
18
18
ENDCHAR

STARTCHAR uni1EE5
ENCODING 7909
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
C6
C6
C6
C6
C6
C6
7E
18
18
ENDCHAR

STARTCHAR Ytilde
ENCODING 7928
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
76
DC
00
C3
C3
66
66
3C
18
18
18
18
00
00
ENDCHAR

STARTCHAR ytilde
ENCODING 7929
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
76
DC
00
C6
C6
C6
C6
C6
C6
7E
06
7C
ENDCHAR

STARTCHAR uni2000
ENCODING 8192
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2001
ENCODING 8193
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR enspace
ENCODING 8194
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2003
ENCODING 8195
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2004
ENCODING 8196
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2005
ENCODING 8197
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2006
ENCODING 8198
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2007
ENCODING 8199
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2008
ENCODING 8200
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2009
ENCODING 8201
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni200A
ENCODING 8202
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR hyphentwo
ENCODING 8208
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
7C
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2011
ENCODING 8209
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
7C
00
00
00
00
00
00
ENDCHAR

STARTCHAR figuredash
ENCODING 8210
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
FE
00
00
00
00
00
00
ENDCHAR

STARTCHAR endash
ENCODING 8211
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
FE
00
00
00
00
00
00
ENDCHAR

STARTCHAR emdash
ENCODING 8212
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
FE
00
00
00
00
00
00
ENDCHAR

STARTCHAR afii00208
ENCODING 8213
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
FE
00
00
00
00
00
00
ENDCHAR

STARTCHAR dblverticalbar
ENCODING 8214
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
6C
6C
6C
6C
6C
6C
6C
6C
00
00
ENDCHAR

STARTCHAR underscoredbl
ENCODING 8215
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
FE
00
FE
ENDCHAR

STARTCHAR quoteleft
ENCODING 8216
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
18
30
30
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR quoteright
ENCODING 8217
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
18
18
30
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR quotesinglbase
ENCODING 8218
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
18
18
30
00
ENDCHAR

STARTCHAR quotereversed
ENCODING 8219
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
30
30
18
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR quotedblleft
ENCODING 8220
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
66
CC
CC
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR quotedblright
ENCODING 8221
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
33
33
66
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR quotedblbase
ENCODING 8222
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
66
66
CC
00
ENDCHAR

STARTCHAR uni201F
ENCODING 8223
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
CC
CC
66
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR dagger
ENCODING 8224
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
7E
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR daggerdbl
ENCODING 8225
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
7E
18
18
18
18
7E
18
18
00
00
ENDCHAR

STARTCHAR bullet
ENCODING 8226
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
18
3C
3C
18
00
00
00
00
00
ENDCHAR

STARTCHAR ellipsis
ENCODING 8230
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
DB
DB
00
00
ENDCHAR

STARTCHAR perthousand
ENCODING 8240
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
EC
AC
F8
18
30
30
60
7F
D5
DF
00
00
ENDCHAR

STARTCHAR minute
ENCODING 8242
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
18
18
18
18
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR second
ENCODING 8243
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
66
66
66
66
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR guilsinglleft
ENCODING 8249
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
0C
18
30
60
30
18
0C
00
00
ENDCHAR

STARTCHAR guilsinglright
ENCODING 8250
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
60
30
18
0C
18
30
60
00
00
ENDCHAR

STARTCHAR exclamdbl
ENCODING 8252
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
66
66
66
66
66
66
66
00
66
66
00
00
ENDCHAR

STARTCHAR overline
ENCODING 8254
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FE
00
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR nsuperior
ENCODING 8319
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
78
6C
6C
6C
6C
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR peseta
ENCODING 8359
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
F8
CC
CC
CC
FA
C6
CF
C6
C6
C3
00
00
ENDCHAR

STARTCHAR Euro
ENCODING 8364
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
1E
33
60
FC
60
FC
60
33
1E
00
00
ENDCHAR

STARTCHAR uni20AE
ENCODING 8366
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
18
18
1E
78
1E
78
18
18
18
00
00
ENDCHAR

STARTCHAR uni210E
ENCODING 8462
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
C0
C0
C0
FC
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR uni210F
ENCODING 8463
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
60
F8
60
7C
66
66
66
66
66
66
00
00
ENDCHAR

STARTCHAR afii61352
ENCODING 8470
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
96
96
96
D0
F0
F0
B0
96
90
96
00
00
ENDCHAR

STARTCHAR trademark
ENCODING 8482
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FB
55
55
51
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR Ohm
ENCODING 8486
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
C6
C6
C6
C6
C6
C6
6C
6C
EE
00
00
ENDCHAR

STARTCHAR arrowleft
ENCODING 8592
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
20
60
FE
FE
60
20
00
00
00
00
ENDCHAR

STARTCHAR arrowup
ENCODING 8593
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
3C
7E
18
18
18
18
18
18
18
00
00
ENDCHAR

STARTCHAR arrowright
ENCODING 8594
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
08
0C
FE
FE
0C
08
00
00
00
00
ENDCHAR

STARTCHAR arrowdown
ENCODING 8595
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
18
18
18
18
18
7E
3C
18
00
00
ENDCHAR

STARTCHAR arrowboth
ENCODING 8596
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
24
66
FF
FF
66
24
00
00
00
00
ENDCHAR

STARTCHAR arrowupdn
ENCODING 8597
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
3C
7E
18
18
18
18
7E
3C
18
00
00
ENDCHAR

STARTCHAR arrowupdnbse
ENCODING 8616
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
3C
7E
18
18
18
7E
3C
18
7E
00
00
ENDCHAR

STARTCHAR carriagereturn
ENCODING 8629
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
06
06
06
26
66
FE
FE
60
20
00
00
ENDCHAR

STARTCHAR arrowdblleft
ENCODING 8656
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
10
3E
7E
E0
7E
3E
10
00
00
00
00
ENDCHAR

STARTCHAR arrowdblup
ENCODING 8657
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
10
38
7C
EE
6C
6C
6C
6C
6C
6C
00
00
ENDCHAR

STARTCHAR arrowdblright
ENCODING 8658
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
10
F8
FC
0E
FC
F8
10
00
00
00
00
ENDCHAR

STARTCHAR arrowdbldown
ENCODING 8659
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
6C
6C
6C
6C
6C
6C
EE
7C
38
10
00
00
ENDCHAR

STARTCHAR arrowdblboth
ENCODING 8660
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
24
7E
FF
C3
FF
7E
24
00
00
00
00
ENDCHAR

STARTCHAR uni21D5
ENCODING 8661
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
10
38
7C
EE
6C
6C
EE
7C
38
10
00
00
ENDCHAR

STARTCHAR existential
ENCODING 8707
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
FE
06
06
06
FE
06
06
06
FE
00
00
ENDCHAR

STARTCHAR emptyset
ENCODING 8709
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
06
0C
7C
CE
DE
F6
E6
7C
60
C0
00
00
ENDCHAR

STARTCHAR increment
ENCODING 8710
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
10
10
38
38
6C
6C
6C
C6
C6
FE
00
00
ENDCHAR

STARTCHAR element
ENCODING 8712
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
3E
60
C0
C0
FE
C0
C0
60
3E
00
00
ENDCHAR

STARTCHAR uni220A
ENCODING 8714
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
3E
60
C0
FE
C0
60
3E
00
00
00
ENDCHAR

STARTCHAR minus
ENCODING 8722
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
FE
00
00
00
00
00
00
ENDCHAR

STARTCHAR bulletoperator
ENCODING 8729
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
38
38
38
00
00
00
00
00
ENDCHAR

STARTCHAR radical
ENCODING 8730
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
0E
0C
0C
0C
0C
CC
CC
CC
6C
3C
1C
00
00
ENDCHAR

STARTCHAR infinity
ENCODING 8734
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
7C
D6
D6
D6
7C
00
00
00
00
ENDCHAR

STARTCHAR orthogonal
ENCODING 8735
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
C0
C0
C0
C0
C0
FE
00
00
00
00
ENDCHAR

STARTCHAR logicaland
ENCODING 8743
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
10
10
38
38
6C
6C
C6
C6
00
00
ENDCHAR

STARTCHAR logicalor
ENCODING 8744
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
C6
C6
6C
6C
38
38
10
10
00
00
ENDCHAR

STARTCHAR intersection
ENCODING 8745
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
7C
C6
C6
C6
C6
C6
C6
C6
00
00
ENDCHAR

STARTCHAR union
ENCODING 8746
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
C6
C6
C6
C6
C6
C6
C6
7C
00
00
ENDCHAR

STARTCHAR approxequal
ENCODING 8776
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
76
DC
00
76
DC
00
00
00
00
ENDCHAR

STARTCHAR notequal
ENCODING 8800
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
06
FE
18
30
FE
C0
00
00
00
00
ENDCHAR

STARTCHAR equivalence
ENCODING 8801
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
FE
00
00
FE
00
00
FE
00
00
00
ENDCHAR

STARTCHAR lessequal
ENCODING 8804
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
0C
18
30
60
30
18
0C
00
7E
00
00
ENDCHAR

STARTCHAR greaterequal
ENCODING 8805
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
30
18
0C
06
0C
18
30
00
7E
00
00
ENDCHAR

STARTCHAR house
ENCODING 8962
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
10
38
6C
C6
C6
C6
C6
FE
00
00
ENDCHAR

STARTCHAR revlogicalnot
ENCODING 8976
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FE
C0
C0
C0
00
00
00
00
00
ENDCHAR

STARTCHAR integraltp
ENCODING 8992
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
0E
1B
1B
18
18
18
18
18
18
18
18
18
ENDCHAR

STARTCHAR integralbt
ENCODING 8993
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
18
18
18
D8
D8
70
00
00
ENDCHAR

STARTCHAR uni23BA
ENCODING 9146
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
00
00
00
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni23BB
ENCODING 9147
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
FF
FF
00
00
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni23BC
ENCODING 9148
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
FF
FF
00
00
00
ENDCHAR

STARTCHAR uni23BD
ENCODING 9149
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
FF
FF
ENDCHAR

STARTCHAR uni2409
ENCODING 9225
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
CC
CC
FC
CC
CC
00
3F
0C
0C
0C
0C
00
00
ENDCHAR

STARTCHAR uni240A
ENCODING 9226
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
C0
C0
C0
C0
F8
00
3F
30
3C
30
30
00
00
ENDCHAR

STARTCHAR uni240B
ENCODING 9227
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
CC
CC
CC
78
30
00
3F
0C
0C
0C
0C
00
00
ENDCHAR

STARTCHAR uni240C
ENCODING 9228
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
FC
C0
F0
C0
C0
00
3F
30
3C
30
30
00
00
ENDCHAR

STARTCHAR uni240D
ENCODING 9229
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
78
CC
C0
CC
78
00
3E
33
3E
36
33
00
00
ENDCHAR

STARTCHAR uni2424
ENCODING 9252
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
CC
EC
FC
DC
CC
00
30
30
30
30
3F
00
00
ENDCHAR

STARTCHAR SF100000
ENCODING 9472
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2501
ENCODING 9473
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FF
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF110000
ENCODING 9474
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
18
18
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2503
ENCODING 9475
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
38
38
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2508
ENCODING 9480
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
AA
AA
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2509
ENCODING 9481
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
AA
AA
AA
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni250A
ENCODING 9482
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
00
18
18
00
18
18
18
00
18
18
00
ENDCHAR

STARTCHAR uni250B
ENCODING 9483
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
00
38
38
00
38
38
38
00
38
38
00
ENDCHAR

STARTCHAR SF010000
ENCODING 9484
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
1F
1F
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni250D
ENCODING 9485
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
1F
1F
1F
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni250E
ENCODING 9486
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
3F
3F
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni250F
ENCODING 9487
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
3F
3F
3F
38
38
38
38
38
38
ENDCHAR

STARTCHAR SF030000
ENCODING 9488
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2511
ENCODING 9489
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
F8
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2512
ENCODING 9490
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
F8
F8
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2513
ENCODING 9491
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
F8
F8
F8
38
38
38
38
38
38
ENDCHAR

STARTCHAR SF020000
ENCODING 9492
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
1F
1F
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2515
ENCODING 9493
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
1F
1F
1F
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2516
ENCODING 9494
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
3F
3F
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2517
ENCODING 9495
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
3F
3F
3F
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF040000
ENCODING 9496
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
F8
F8
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2519
ENCODING 9497
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
F8
F8
F8
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni251A
ENCODING 9498
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
F8
F8
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni251B
ENCODING 9499
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
F8
F8
F8
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF080000
ENCODING 9500
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
1F
1F
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni251D
ENCODING 9501
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
1F
1F
1F
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni251E
ENCODING 9502
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
3F
3F
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni251F
ENCODING 9503
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
3F
3F
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2520
ENCODING 9504
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
3F
3F
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2521
ENCODING 9505
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
3F
3F
3F
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2522
ENCODING 9506
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
3F
3F
3F
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2523
ENCODING 9507
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
3F
3F
3F
38
38
38
38
38
38
ENDCHAR

STARTCHAR SF090000
ENCODING 9508
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2525
ENCODING 9509
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
F8
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2526
ENCODING 9510
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2527
ENCODING 9511
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
F8
F8
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2528
ENCODING 9512
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
F8
F8
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2529
ENCODING 9513
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
F8
F8
F8
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni252A
ENCODING 9514
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
F8
F8
F8
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni252B
ENCODING 9515
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
F8
F8
F8
38
38
38
38
38
38
ENDCHAR

STARTCHAR SF060000
ENCODING 9516
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni252D
ENCODING 9517
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
F8
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni252E
ENCODING 9518
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
1F
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni252F
ENCODING 9519
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FF
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2530
ENCODING 9520
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2531
ENCODING 9521
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
F8
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2532
ENCODING 9522
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
3F
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2533
ENCODING 9523
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FF
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR SF070000
ENCODING 9524
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2535
ENCODING 9525
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
F8
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2536
ENCODING 9526
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
1F
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2537
ENCODING 9527
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
FF
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2538
ENCODING 9528
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2539
ENCODING 9529
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
F8
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni253A
ENCODING 9530
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
3F
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni253B
ENCODING 9531
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
FF
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF050000
ENCODING 9532
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni253D
ENCODING 9533
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
F8
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni253E
ENCODING 9534
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
1F
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni253F
ENCODING 9535
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
FF
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2540
ENCODING 9536
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2541
ENCODING 9537
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
18
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2542
ENCODING 9538
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
38
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2543
ENCODING 9539
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
F8
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2544
ENCODING 9540
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
3F
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2545
ENCODING 9541
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
F8
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2546
ENCODING 9542
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
1F
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2547
ENCODING 9543
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
FF
FF
FF
18
18
18
18
18
18
ENDCHAR

STARTCHAR uni2548
ENCODING 9544
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
18
FF
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni2549
ENCODING 9545
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
F8
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni254A
ENCODING 9546
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
3F
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR uni254B
ENCODING 9547
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
38
38
38
38
FF
FF
FF
38
38
38
38
38
38
ENDCHAR

STARTCHAR SF430000
ENCODING 9552
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
FF
FF
00
FF
FF
00
00
00
00
00
ENDCHAR

STARTCHAR SF240000
ENCODING 9553
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6C
6C
6C
6C
6C
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF510000
ENCODING 9554
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
1F
1F
18
1F
1F
18
18
18
18
18
ENDCHAR

STARTCHAR SF520000
ENCODING 9555
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
7F
7F
6C
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF390000
ENCODING 9556
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
7F
7F
60
6F
6F
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF220000
ENCODING 9557
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
F8
F8
18
F8
F8
18
18
18
18
18
ENDCHAR

STARTCHAR SF210000
ENCODING 9558
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
FC
FC
6C
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF250000
ENCODING 9559
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
FC
FC
0C
EC
EC
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF500000
ENCODING 9560
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
1F
1F
18
1F
1F
00
00
00
00
00
ENDCHAR

STARTCHAR SF490000
ENCODING 9561
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6C
6C
7F
7F
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF380000
ENCODING 9562
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6F
6F
60
7F
7F
00
00
00
00
00
ENDCHAR

STARTCHAR SF280000
ENCODING 9563
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
F8
F8
18
F8
F8
00
00
00
00
00
ENDCHAR

STARTCHAR SF270000
ENCODING 9564
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6C
6C
FC
FC
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF260000
ENCODING 9565
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
EC
EC
0C
FC
FC
00
00
00
00
00
ENDCHAR

STARTCHAR SF360000
ENCODING 9566
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
1F
1F
18
1F
1F
18
18
18
18
18
ENDCHAR

STARTCHAR SF370000
ENCODING 9567
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6C
6C
6F
6F
6C
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF420000
ENCODING 9568
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6F
6F
60
6F
6F
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF190000
ENCODING 9569
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
F8
F8
18
F8
F8
18
18
18
18
18
ENDCHAR

STARTCHAR SF200000
ENCODING 9570
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6C
6C
EC
EC
6C
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF230000
ENCODING 9571
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
EC
EC
0C
EC
EC
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF470000
ENCODING 9572
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
FF
FF
00
FF
FF
18
18
18
18
18
ENDCHAR

STARTCHAR SF480000
ENCODING 9573
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
FF
FF
6C
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF410000
ENCODING 9574
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
FF
FF
00
EF
EF
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF450000
ENCODING 9575
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
FF
FF
00
FF
FF
00
00
00
00
00
ENDCHAR

STARTCHAR SF460000
ENCODING 9576
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6C
6C
FF
FF
00
00
00
00
00
00
ENDCHAR

STARTCHAR SF400000
ENCODING 9577
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
EF
EF
00
FF
FF
00
00
00
00
00
ENDCHAR

STARTCHAR SF540000
ENCODING 9578
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
18
18
18
18
FF
FF
18
FF
FF
18
18
18
18
18
ENDCHAR

STARTCHAR SF530000
ENCODING 9579
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
6C
6C
FF
FF
6C
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR SF440000
ENCODING 9580
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
6C
6C
6C
6C
EF
EF
00
EF
EF
6C
6C
6C
6C
6C
ENDCHAR

STARTCHAR upblock
ENCODING 9600
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
FF
FF
FF
FF
FF
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2581
ENCODING 9601
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
00
00
FF
FF
ENDCHAR

STARTCHAR uni2582
ENCODING 9602
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
00
FF
FF
FF
FF
ENDCHAR

STARTCHAR uni2583
ENCODING 9603
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
00
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR dnblock
ENCODING 9604
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
FF
FF
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR uni2585
ENCODING 9605
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
FF
FF
FF
FF
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR uni2586
ENCODING 9606
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR uni2587
ENCODING 9607
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR block
ENCODING 9608
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR lfblock
ENCODING 9612
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
F0
F0
F0
F0
F0
F0
F0
F0
F0
F0
F0
F0
F0
F0
ENDCHAR

STARTCHAR rtblock
ENCODING 9616
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
0F
0F
0F
0F
0F
0F
0F
0F
0F
0F
0F
0F
0F
0F
ENDCHAR

STARTCHAR ltshade
ENCODING 9617
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
88
22
88
22
88
22
88
22
88
22
88
22
88
22
ENDCHAR

STARTCHAR shade
ENCODING 9618
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
AA
55
AA
55
AA
55
AA
55
AA
55
AA
55
AA
55
ENDCHAR

STARTCHAR dkshade
ENCODING 9619
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
EE
BB
EE
BB
EE
BB
EE
BB
EE
BB
EE
BB
EE
BB
ENDCHAR

STARTCHAR uni2596
ENCODING 9622
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
F0
F0
F0
F0
F0
F0
F0
ENDCHAR

STARTCHAR uni2597
ENCODING 9623
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
0F
0F
0F
0F
0F
0F
0F
ENDCHAR

STARTCHAR uni2598
ENCODING 9624
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
F0
F0
F0
F0
F0
F0
F0
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni2599
ENCODING 9625
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
F0
F0
F0
F0
F0
F0
F0
FF
FF
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR uni259A
ENCODING 9626
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
F0
F0
F0
F0
F0
F0
F0
0F
0F
0F
0F
0F
0F
0F
ENDCHAR

STARTCHAR uni259B
ENCODING 9627
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
FF
FF
FF
FF
FF
F0
F0
F0
F0
F0
F0
F0
ENDCHAR

STARTCHAR uni259C
ENCODING 9628
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
FF
FF
FF
FF
FF
0F
0F
0F
0F
0F
0F
0F
ENDCHAR

STARTCHAR uni259D
ENCODING 9629
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
0F
0F
0F
0F
0F
0F
0F
00
00
00
00
00
00
00
ENDCHAR

STARTCHAR uni259E
ENCODING 9630
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
0F
0F
0F
0F
0F
0F
0F
F0
F0
F0
F0
F0
F0
F0
ENDCHAR

STARTCHAR uni259F
ENCODING 9631
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
0F
0F
0F
0F
0F
0F
0F
FF
FF
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR filledbox
ENCODING 9632
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
7C
7C
7C
7C
7C
7C
00
00
00
00
ENDCHAR

STARTCHAR filledrect
ENCODING 9644
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
00
00
00
FE
FE
FE
FE
00
00
ENDCHAR

STARTCHAR uni25AE
ENCODING 9646
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
FE
FE
FE
FE
FE
FE
FE
FE
FE
00
00
ENDCHAR

STARTCHAR triagup
ENCODING 9650
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
18
18
3C
3C
7E
7E
FF
FF
00
00
00
ENDCHAR

STARTCHAR uni25B6
ENCODING 9654
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
C0
F0
FC
FF
FF
FC
F0
C0
00
00
00
ENDCHAR

STARTCHAR triagdn
ENCODING 9660
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
FF
FF
7E
7E
3C
3C
18
18
00
00
00
ENDCHAR

STARTCHAR uni25C0
ENCODING 9664
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
03
0F
3F
FF
FF
3F
0F
03
00
00
00
ENDCHAR

STARTCHAR blackdiamond
ENCODING 9670
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
18
3C
7E
FF
7E
3C
18
00
00
00
ENDCHAR

STARTCHAR lozenge
ENCODING 9674
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
18
3C
66
C3
66
3C
18
00
00
00
ENDCHAR

STARTCHAR circle
ENCODING 9675
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
3C
66
42
42
66
3C
00
00
00
00
ENDCHAR

STARTCHAR invbullet
ENCODING 9688
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
FF
FF
FF
E7
C3
C3
E7
FF
FF
FF
FF
FF
ENDCHAR

STARTCHAR invcircle
ENCODING 9689
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
FF
FF
FF
FF
C3
99
BD
BD
99
C3
FF
FF
FF
FF
ENDCHAR

STARTCHAR smileface
ENCODING 9786
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
82
AA
82
82
BA
92
82
82
7C
00
00
ENDCHAR

STARTCHAR invsmileface
ENCODING 9787
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7C
FE
D6
FE
FE
C6
EE
FE
FE
7C
00
00
ENDCHAR

STARTCHAR sun
ENCODING 9788
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
18
DB
7E
3C
E7
3C
7E
DB
18
00
00
ENDCHAR

STARTCHAR female
ENCODING 9792
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3C
66
66
66
66
3C
18
7E
18
18
00
00
ENDCHAR

STARTCHAR male
ENCODING 9794
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
3E
0E
1A
32
78
CC
CC
CC
CC
78
00
00
ENDCHAR

STARTCHAR spade
ENCODING 9824
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
18
3C
7E
FF
FF
7E
18
18
3C
00
00
ENDCHAR

STARTCHAR club
ENCODING 9827
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
18
3C
3C
18
5A
FF
FF
5A
18
3C
00
00
ENDCHAR

STARTCHAR heart
ENCODING 9829
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
6C
FE
FE
FE
FE
7C
38
10
00
00
00
ENDCHAR

STARTCHAR diamond
ENCODING 9830
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
18
3C
7E
FF
7E
3C
18
00
00
00
ENDCHAR

STARTCHAR musicalnote
ENCODING 9834
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
66
7E
60
60
60
60
60
E0
C0
00
00
ENDCHAR

STARTCHAR musicalnotedbl
ENCODING 9835
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
7E
66
7E
66
66
66
66
66
6E
EC
C0
00
ENDCHAR

STARTCHAR uniE0A0
ENCODING 57504
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
60
60
66
6F
66
66
66
6C
18
30
60
60
60
60
ENDCHAR

STARTCHAR uniE0A1
ENCODING 57505
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
40
40
40
40
40
78
00
12
12
1A
16
12
12
00
ENDCHAR

STARTCHAR uniE0A2
ENCODING 57506
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
38
28
24
24
7C
7C
6C
6C
6C
7C
7C
00
00
00
ENDCHAR

STARTCHAR uniE0B0
ENCODING 57520
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
C0
E0
F0
F8
FC
FE
FF
FF
FE
FC
F8
F0
E0
C0
ENDCHAR

STARTCHAR uniE0B1
ENCODING 57521
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
40
20
10
08
04
02
01
01
02
04
08
10
20
40
ENDCHAR

STARTCHAR uniE0B2
ENCODING 57522
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
03
07
0F
1F
3F
7F
FF
FF
7F
3F
1F
0F
07
03
ENDCHAR

STARTCHAR uniE0B3
ENCODING 57523
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
02
04
08
10
20
40
80
80
40
20
10
08
04
02
ENDCHAR

STARTCHAR uniF6BE
ENCODING 63166
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
00
00
00
0E
06
06
06
06
06
66
66
3C
ENDCHAR

STARTCHAR uniFFFD
ENCODING 65533
SWIDTH 571 0
DWIDTH 8 0
BBX 8 14 0 -2
BITMAP
00
00
FE
C6
C6
C6
C6
C6
C6
C6
C6
FE
00
00
ENDCHAR

ENDFONT
