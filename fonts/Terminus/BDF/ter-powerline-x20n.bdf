STARTFONT 2.1
FONT -xos4-TerminessPowerline-Medium-R-Normal--20-200-72-72-C-100-ISO10646-1
SIZE 20 72 72
FONTBOUNDINGBOX 10 20 0 -4
STARTPROPERTIES 23
ADD_STYLE_NAME ""
AVERAGE_WIDTH 100
CHARSET_ENCODING "1"
CHARSET_REGISTRY "ISO10646"
COPYRIGHT "Copyright (C) 2011 Dimitar <PERSON>. Copyright (c) 2013, <PERSON>, <PERSON><PERSON> and <PERSON>"
FAMILY_NAME "Terminess Powerline"
FOUNDRY "xos4"
MIN_SPACE 10
NOTICE "Licensed under the SIL Open Font License, Version 1.1"
PIXEL_SIZE 20
POINT_SIZE 200
QUAD_WIDTH 10
RESOLUTION_X 72
RESOLUTION_Y 72
SETWIDTH_NAME "Normal"
SLANT "R"
SPACING "C"
WEIGHT 10
WEIGHT_NAME "Medium"
X_HEIGHT 16
DEFAULT_CHAR -3
FONT_DESCENT 4
FONT_ASCENT 16
ENDPROPERTIES

CHARS 894

STARTCHAR uni25AE
ENCODING 0
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR blackdiamond
ENCODING 1
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
1C00
3E00
7F00
FF80
7F00
3E00
1C00
0800
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR shade
ENCODING 2
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
ENDCHAR

STARTCHAR uni2409
ENCODING 3
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4400
4400
4400
7C00
4400
4400
4400
0000
0F80
0200
0200
0200
0200
0200
0200
0000
0000
0000
ENDCHAR

STARTCHAR uni240C
ENCODING 4
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
7C00
4000
4000
7000
4000
4000
4000
0000
0F80
0800
0800
0E00
0800
0800
0800
0000
0000
0000
ENDCHAR

STARTCHAR uni240D
ENCODING 5
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
3800
4400
4000
4000
4000
4400
3800
0000
0F00
0880
0880
0F00
0A00
0900
0880
0000
0000
0000
ENDCHAR

STARTCHAR uni240A
ENCODING 6
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4000
4000
4000
4000
4000
4000
7C00
0000
0F80
0800
0800
0E00
0800
0800
0800
0000
0000
0000
ENDCHAR

STARTCHAR degree
ENCODING 7
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1C00
2200
2200
2200
1C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR plusminus
ENCODING 8
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0800
0800
0800
7F00
0800
0800
0800
0000
0000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2424
ENCODING 9
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4400
4400
6400
5400
4C00
4400
4400
0000
0800
0800
0800
0800
0800
0800
0F80
0000
0000
0000
ENDCHAR

STARTCHAR uni240B
ENCODING 10
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4400
4400
4400
2800
2800
1000
1000
0000
0F80
0200
0200
0200
0200
0200
0200
0000
0000
0000
ENDCHAR

STARTCHAR SF040000
ENCODING 11
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
F800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF030000
ENCODING 12
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF010000
ENCODING 13
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF020000
ENCODING 14
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF050000
ENCODING 15
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni23BA
ENCODING 16
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni23BB
ENCODING 17
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF100000
ENCODING 18
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni23BC
ENCODING 19
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni23BD
ENCODING 20
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
ENDCHAR

STARTCHAR SF080000
ENCODING 21
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF090000
ENCODING 22
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF070000
ENCODING 23
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF060000
ENCODING 24
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF110000
ENCODING 25
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR lessequal
ENCODING 26
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0200
0400
0800
1000
2000
4000
2000
1000
0800
0400
0200
0000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR greaterequal
ENCODING 27
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
2000
1000
0800
0400
0200
0100
0200
0400
0800
1000
2000
0000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR pi
ENCODING 28
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR notequal
ENCODING 29
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0100
7F00
0400
0800
1000
7F00
4000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR sterling
ENCODING 30
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
2200
2000
2000
2000
2000
7C00
2000
2000
2000
2000
2100
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR periodcentered
ENCODING 31
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR space
ENCODING 32
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR exclam
ENCODING 33
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR quotedbl
ENCODING 34
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
2200
2200
2200
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR numbersign
ENCODING 35
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
2200
2200
2200
2200
7F00
2200
2200
2200
7F00
2200
2200
2200
2200
0000
0000
0000
0000
ENDCHAR

STARTCHAR dollar
ENCODING 36
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0800
0800
3E00
4900
4800
4800
4800
3E00
0900
0900
0900
4900
3E00
0800
0800
0000
0000
0000
ENDCHAR

STARTCHAR percent
ENCODING 37
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
7100
5100
7200
0200
0400
0400
0800
0800
1000
1380
2280
2380
0000
0000
0000
0000
ENDCHAR

STARTCHAR ampersand
ENCODING 38
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
2200
2200
2200
1400
0800
1480
2280
4100
4100
4100
2280
1C80
0000
0000
0000
0000
ENDCHAR

STARTCHAR quotesingle
ENCODING 39
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0800
0800
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR parenleft
ENCODING 40
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0400
0800
0800
1000
1000
1000
1000
1000
1000
1000
0800
0800
0400
0000
0000
0000
0000
ENDCHAR

STARTCHAR parenright
ENCODING 41
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1000
0800
0800
0400
0400
0400
0400
0400
0400
0400
0800
0800
1000
0000
0000
0000
0000
ENDCHAR

STARTCHAR asterisk
ENCODING 42
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
2200
1400
0800
7F00
0800
1400
2200
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR plus
ENCODING 43
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0800
0800
0800
7F00
0800
0800
0800
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR comma
ENCODING 44
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0800
0800
0800
1000
0000
0000
0000
ENDCHAR

STARTCHAR hyphen
ENCODING 45
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR period
ENCODING 46
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR slash
ENCODING 47
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0100
0100
0200
0200
0400
0400
0800
0800
1000
1000
2000
2000
0000
0000
0000
0000
ENDCHAR

STARTCHAR zero
ENCODING 48
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4300
4500
4900
5100
6100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR one
ENCODING 49
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
1800
2800
0800
0800
0800
0800
0800
0800
0800
0800
0800
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR two
ENCODING 50
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
0100
0100
0200
0400
0800
1000
2000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR three
ENCODING 51
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
0100
0100
0100
1E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR four
ENCODING 52
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0100
0300
0500
0900
1100
2100
4100
4100
4100
7F00
0100
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR five
ENCODING 53
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
7E00
0100
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR six
ENCODING 54
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1E00
2000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR seven
ENCODING 55
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4100
4100
0100
0200
0200
0400
0400
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR eight
ENCODING 56
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
3E00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR nine
ENCODING 57
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0200
3C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR colon
ENCODING 58
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0800
0800
0000
0000
0000
0000
0800
0800
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR semicolon
ENCODING 59
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0800
0800
0000
0000
0000
0000
0800
0800
0800
1000
0000
0000
0000
ENDCHAR

STARTCHAR less
ENCODING 60
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0100
0200
0400
0800
1000
2000
4000
2000
1000
0800
0400
0200
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR equal
ENCODING 61
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
0000
0000
0000
7F00
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR greater
ENCODING 62
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
2000
1000
0800
0400
0200
0100
0200
0400
0800
1000
2000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR question
ENCODING 63
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
2200
4100
4100
0100
0200
0400
0800
0800
0000
0000
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR at
ENCODING 64
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3F00
4080
4080
4780
4880
4880
4880
4880
4980
4680
4000
4000
3F80
0000
0000
0000
0000
ENDCHAR

STARTCHAR A
ENCODING 65
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR B
ENCODING 66
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
7E00
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR C
ENCODING 67
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR D
ENCODING 68
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7C00
4200
4100
4100
4100
4100
4100
4100
4100
4100
4100
4200
7C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR E
ENCODING 69
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR F
ENCODING 70
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR G
ENCODING 71
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
4F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR H
ENCODING 72
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR I
ENCODING 73
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR J
ENCODING 74
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0380
0100
0100
0100
0100
0100
0100
0100
0100
2100
2100
2100
1E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR K
ENCODING 75
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR L
ENCODING 76
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR M
ENCODING 77
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4080
6180
5280
5280
4C80
4C80
4080
4080
4080
4080
4080
4080
4080
0000
0000
0000
0000
ENDCHAR

STARTCHAR N
ENCODING 78
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR O
ENCODING 79
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR P
ENCODING 80
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
4100
7E00
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Q
ENCODING 81
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4900
3E00
0200
0100
0000
0000
ENDCHAR

STARTCHAR R
ENCODING 82
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
4100
7E00
6000
5000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR S
ENCODING 83
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
3E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR T
ENCODING 84
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR U
ENCODING 85
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR V
ENCODING 86
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
2200
2200
2200
2200
1400
1400
1400
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR W
ENCODING 87
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4080
4080
4080
4080
4080
4080
4080
4C80
4C80
5280
5280
6180
4080
0000
0000
0000
0000
ENDCHAR

STARTCHAR X
ENCODING 88
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
1400
1400
2200
2200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Y
ENCODING 89
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR Z
ENCODING 90
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0100
0100
0100
0200
0400
0800
1000
2000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR bracketleft
ENCODING 91
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
1000
1000
1000
1000
1000
1000
1000
1000
1000
1000
1000
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR backslash
ENCODING 92
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2000
2000
1000
1000
0800
0800
0400
0400
0200
0200
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR bracketright
ENCODING 93
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
0400
0400
0400
0400
0400
0400
0400
0400
0400
0400
0400
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR asciicircum
ENCODING 94
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0800
1400
2200
4100
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR underscore
ENCODING 95
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F00
0000
0000
ENDCHAR

STARTCHAR grave
ENCODING 96
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR a
ENCODING 97
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR b
ENCODING 98
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR c
ENCODING 99
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR d
ENCODING 100
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0100
0100
0100
0100
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR e
ENCODING 101
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR f
ENCODING 102
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0700
0800
0800
0800
3E00
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR g
ENCODING 103
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR h
ENCODING 104
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR i
ENCODING 105
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR j
ENCODING 106
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0200
0200
0000
0000
0600
0200
0200
0200
0200
0200
0200
0200
0200
2200
2200
1C00
0000
ENDCHAR

STARTCHAR k
ENCODING 107
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
4100
4200
4400
4800
7000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR l
ENCODING 108
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR m
ENCODING 109
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4900
4900
4900
4900
4900
4900
4900
4900
0000
0000
0000
0000
ENDCHAR

STARTCHAR n
ENCODING 110
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR o
ENCODING 111
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR p
ENCODING 112
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
7E00
4000
4000
4000
0000
ENDCHAR

STARTCHAR q
ENCODING 113
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0000
ENDCHAR

STARTCHAR r
ENCODING 114
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4F00
5000
6000
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR s
ENCODING 115
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
3E00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR t
ENCODING 116
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
3E00
0800
0800
0800
0800
0800
0800
0800
0700
0000
0000
0000
0000
ENDCHAR

STARTCHAR u
ENCODING 117
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR v
ENCODING 118
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
2200
2200
1400
1400
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR w
ENCODING 119
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4900
4900
4900
4900
4900
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR x
ENCODING 120
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
2200
1400
0800
1400
2200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR y
ENCODING 121
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR z
ENCODING 122
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
0100
0200
0400
0800
1000
2000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR braceleft
ENCODING 123
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0600
0800
0800
0800
0800
0800
3000
0800
0800
0800
0800
0800
0600
0000
0000
0000
0000
ENDCHAR

STARTCHAR bar
ENCODING 124
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR braceright
ENCODING 125
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3000
0800
0800
0800
0800
0800
0600
0800
0800
0800
0800
0800
3000
0000
0000
0000
0000
ENDCHAR

STARTCHAR asciitilde
ENCODING 126
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3100
4900
4900
4600
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR nbspace
ENCODING 160
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR exclamdown
ENCODING 161
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0000
0000
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR cent
ENCODING 162
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
0800
3E00
4900
4800
4800
4800
4800
4800
4900
3E00
0800
0800
0000
0000
ENDCHAR

STARTCHAR sterling
ENCODING 163
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
2200
2000
2000
2000
2000
7C00
2000
2000
2000
2000
2100
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR currency
ENCODING 164
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
4080
2100
1E00
2100
2100
2100
2100
1E00
2100
4080
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR yen
ENCODING 165
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
0800
3E00
0800
3E00
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR brokenbar
ENCODING 166
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
0800
0000
0000
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR section
ENCODING 167
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
1C00
2200
2000
2000
1800
2400
2200
2200
2200
1200
0C00
0200
0200
2200
1C00
0000
0000
0000
ENDCHAR

STARTCHAR dieresis
ENCODING 168
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR copyright
ENCODING 169
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
4100
9C80
A280
A080
A080
A280
9C80
4100
3E00
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR ordfeminine
ENCODING 170
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1E00
0100
1F00
2100
2100
2100
1F00
0000
3F00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR guillemotleft
ENCODING 171
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0880
1100
2200
4400
8800
4400
2200
1100
0880
0000
0000
0000
0000
ENDCHAR

STARTCHAR logicalnot
ENCODING 172
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
0100
0100
0100
0100
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR softhyphen
ENCODING 173
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
3E00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR registered
ENCODING 174
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
4100
BC80
A280
A280
BC80
A480
A280
4100
3E00
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR macron
ENCODING 175
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR degree
ENCODING 176
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1C00
2200
2200
2200
1C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR plusminus
ENCODING 177
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0800
0800
0800
7F00
0800
0800
0800
0000
0000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR twosuperior
ENCODING 178
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0C00
1200
0200
0400
0800
1E00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR threesuperior
ENCODING 179
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1C00
0200
0C00
0200
0200
1C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR acute
ENCODING 180
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR mu
ENCODING 181
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4300
7D00
4000
4000
4000
0000
ENDCHAR

STARTCHAR paragraph
ENCODING 182
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3F00
4900
4900
4900
4900
4900
3900
0900
0900
0900
0900
0900
0900
0000
0000
0000
0000
ENDCHAR

STARTCHAR periodcentered
ENCODING 183
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR cedilla
ENCODING 184
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0800
0800
1000
0000
ENDCHAR

STARTCHAR onesuperior
ENCODING 185
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0800
1800
0800
0800
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR ordmasculine
ENCODING 186
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1E00
2100
2100
2100
2100
2100
1E00
0000
3F00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR guillemotright
ENCODING 187
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
8800
4400
2200
1100
0880
1100
2200
4400
8800
0000
0000
0000
0000
ENDCHAR

STARTCHAR onequarter
ENCODING 188
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
1000
3000
1000
1080
1100
1200
0400
0800
1100
2300
4500
8900
0F00
0100
0100
0000
0000
0000
ENDCHAR

STARTCHAR onehalf
ENCODING 189
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
1000
3000
1000
1080
1100
1200
0400
0800
1000
2600
4900
8100
0200
0400
0F00
0000
0000
0000
ENDCHAR

STARTCHAR threequarters
ENCODING 190
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
7000
0800
3000
0880
0900
7200
0400
0800
1100
2300
4500
8900
0F00
0100
0100
0000
0000
0000
ENDCHAR

STARTCHAR questiondown
ENCODING 191
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0000
0000
0800
0800
1000
2000
4000
4100
4100
2200
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Agrave
ENCODING 192
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Aacute
ENCODING 193
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Acircumflex
ENCODING 194
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Atilde
ENCODING 195
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Adieresis
ENCODING 196
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Aring
ENCODING 197
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
1C00
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR AE
ENCODING 198
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F80
8800
8800
8800
8800
8800
FF00
8800
8800
8800
8800
8800
8F80
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ccedilla
ENCODING 199
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0800
0800
1000
0000
ENDCHAR

STARTCHAR Egrave
ENCODING 200
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Eacute
ENCODING 201
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ecircumflex
ENCODING 202
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Edieresis
ENCODING 203
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Igrave
ENCODING 204
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Iacute
ENCODING 205
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Icircumflex
ENCODING 206
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Idieresis
ENCODING 207
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Eth
ENCODING 208
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7C00
4200
4100
4100
4100
4100
F900
4100
4100
4100
4100
4200
7C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ntilde
ENCODING 209
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ograve
ENCODING 210
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Oacute
ENCODING 211
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ocircumflex
ENCODING 212
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Otilde
ENCODING 213
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Odieresis
ENCODING 214
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR multiply
ENCODING 215
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
4100
2200
1400
0800
1400
2200
4100
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Oslash
ENCODING 216
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4180
4100
4300
4500
4900
5100
6100
4100
C100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ugrave
ENCODING 217
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Uacute
ENCODING 218
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ucircumflex
ENCODING 219
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Udieresis
ENCODING 220
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Yacute
ENCODING 221
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR Thorn
ENCODING 222
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
7E00
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR germandbls
ENCODING 223
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3C00
4200
4200
4200
4400
7E00
4100
4100
4100
4100
4100
6100
5E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR agrave
ENCODING 224
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1000
0800
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR aacute
ENCODING 225
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR acircumflex
ENCODING 226
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR atilde
ENCODING 227
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
3300
4C00
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR adieresis
ENCODING 228
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR aring
ENCODING 229
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
1C00
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ae
ENCODING 230
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7700
0880
0880
7880
8F80
8800
8800
8880
7700
0000
0000
0000
0000
ENDCHAR

STARTCHAR ccedilla
ENCODING 231
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0800
0800
1000
0000
ENDCHAR

STARTCHAR egrave
ENCODING 232
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1000
0800
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR eacute
ENCODING 233
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ecircumflex
ENCODING 234
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR edieresis
ENCODING 235
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR igrave
ENCODING 236
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1000
0800
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR iacute
ENCODING 237
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR icircumflex
ENCODING 238
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR idieresis
ENCODING 239
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR eth
ENCODING 240
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
2C00
1000
6800
0400
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ntilde
ENCODING 241
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
3300
4C00
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR ograve
ENCODING 242
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1000
0800
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR oacute
ENCODING 243
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ocircumflex
ENCODING 244
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR otilde
ENCODING 245
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
3300
4C00
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR odieresis
ENCODING 246
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR divide
ENCODING 247
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
0800
0000
0000
7F00
0000
0000
0800
0800
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR oslash
ENCODING 248
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E80
4100
4300
4500
4900
5100
6100
4100
BE00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ugrave
ENCODING 249
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1000
0800
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uacute
ENCODING 250
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ucircumflex
ENCODING 251
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR udieresis
ENCODING 252
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR yacute
ENCODING 253
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR thorn
ENCODING 254
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
7E00
4000
4000
4000
0000
ENDCHAR

STARTCHAR ydieresis
ENCODING 255
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR Amacron
ENCODING 256
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR amacron
ENCODING 257
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Abreve
ENCODING 258
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR abreve
ENCODING 259
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Aogonek
ENCODING 260
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0100
0200
0180
0000
ENDCHAR

STARTCHAR aogonek
ENCODING 261
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0100
0200
0180
0000
ENDCHAR

STARTCHAR Cacute
ENCODING 262
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR cacute
ENCODING 263
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ccircumflex
ENCODING 264
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ccircumflex
ENCODING 265
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Cdotaccent
ENCODING 266
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR cdotaccent
ENCODING 267
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0800
0800
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ccaron
ENCODING 268
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ccaron
ENCODING 269
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Dcaron
ENCODING 270
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
7C00
4200
4100
4100
4100
4100
4100
4100
4100
4100
4100
4200
7C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR dcaron
ENCODING 271
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
0100
0100
0100
0100
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Dcroat
ENCODING 272
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7C00
4200
4100
4100
4100
4100
F900
4100
4100
4100
4100
4200
7C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR dcroat
ENCODING 273
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0100
0F80
0100
0100
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Emacron
ENCODING 274
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR emacron
ENCODING 275
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ebreve
ENCODING 276
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ebreve
ENCODING 277
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Edotaccent
ENCODING 278
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR edotaccent
ENCODING 279
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0800
0800
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Eogonek
ENCODING 280
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0100
0200
0180
0000
ENDCHAR

STARTCHAR eogonek
ENCODING 281
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0800
1000
0C00
0000
ENDCHAR

STARTCHAR Ecaron
ENCODING 282
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ecaron
ENCODING 283
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Gcircumflex
ENCODING 284
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
3E00
4100
4100
4000
4000
4000
4F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR gcircumflex
ENCODING 285
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR Gbreve
ENCODING 286
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
3E00
4100
4100
4000
4000
4000
4F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR gbreve
ENCODING 287
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR Gdotaccent
ENCODING 288
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0000
3E00
4100
4100
4000
4000
4000
4F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR gdotaccent
ENCODING 289
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0800
0800
0000
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR Gcommaaccent
ENCODING 290
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
4F00
4100
4100
4100
4100
4100
3E00
0000
0800
0800
1000
ENDCHAR

STARTCHAR gcommaaccent
ENCODING 291
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0400
0800
0800
0000
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR Hcircumflex
ENCODING 292
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
4100
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR hcircumflex
ENCODING 293
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Hbar
ENCODING 294
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
FF80
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR hbar
ENCODING 295
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
F800
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Itilde
ENCODING 296
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR itilde
ENCODING 297
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
3300
4C00
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Imacron
ENCODING 298
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR imacron
ENCODING 299
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ibreve
ENCODING 300
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ibreve
ENCODING 301
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Iogonek
ENCODING 302
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0800
1000
0C00
0000
ENDCHAR

STARTCHAR iogonek
ENCODING 303
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0800
1000
0C00
0000
ENDCHAR

STARTCHAR Idotaccent
ENCODING 304
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR dotlessi
ENCODING 305
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR IJ
ENCODING 306
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
E380
4100
4100
4100
4100
4100
4100
4100
4100
4900
4900
4900
E600
0000
0000
0000
0000
ENDCHAR

STARTCHAR ij
ENCODING 307
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
2080
2080
0000
0000
6180
2080
2080
2080
2080
2080
2080
2080
7080
0480
0480
0300
0000
ENDCHAR

STARTCHAR Jcircumflex
ENCODING 308
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0700
0880
0000
0700
0200
0200
0200
0200
0200
0200
0200
0200
4200
4200
4200
3C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR jcircumflex
ENCODING 309
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0700
0880
0000
0600
0200
0200
0200
0200
0200
0200
0200
0200
2200
2200
1C00
0000
ENDCHAR

STARTCHAR Kcommaaccent
ENCODING 310
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4100
0000
0800
0800
1000
ENDCHAR

STARTCHAR kcommaaccent
ENCODING 311
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
4100
4200
4400
4800
7000
4800
4400
4200
4100
0000
0800
0800
1000
ENDCHAR

STARTCHAR kgreenlandic
ENCODING 312
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4200
4400
4800
7000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Lacute
ENCODING 313
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2000
4000
0000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR lacute
ENCODING 314
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
1800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Lcommaaccent
ENCODING 315
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
7F00
0000
0800
0800
1000
ENDCHAR

STARTCHAR lcommaaccent
ENCODING 316
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0800
0800
1000
ENDCHAR

STARTCHAR Lcaron
ENCODING 317
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR lcaron
ENCODING 318
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
1800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ldot
ENCODING 319
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
4000
4200
4200
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ldot
ENCODING 320
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1800
0800
0800
0800
0800
0840
0840
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Lslash
ENCODING 321
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
2000
2000
2000
2000
2800
3000
2000
6000
A000
2000
2000
2000
3F80
0000
0000
0000
0000
ENDCHAR

STARTCHAR lslash
ENCODING 322
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1800
0800
0800
0800
0A00
0C00
0800
1800
2800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Nacute
ENCODING 323
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR nacute
ENCODING 324
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ncommaaccent
ENCODING 325
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
0000
0800
0800
1000
ENDCHAR

STARTCHAR ncommaaccent
ENCODING 326
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0800
0800
1000
ENDCHAR

STARTCHAR Ncaron
ENCODING 327
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR ncaron
ENCODING 328
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR napostrophe
ENCODING 329
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
4000
4000
8000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Eng
ENCODING 330
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
0100
0100
0600
0000
ENDCHAR

STARTCHAR eng
ENCODING 331
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0100
0100
0600
0000
ENDCHAR

STARTCHAR Omacron
ENCODING 332
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR omacron
ENCODING 333
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Obreve
ENCODING 334
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR obreve
ENCODING 335
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ohungarumlaut
ENCODING 336
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1100
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ohungarumlaut
ENCODING 337
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1100
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR OE
ENCODING 338
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F80
8800
8800
8800
8800
8800
8F00
8800
8800
8800
8800
8800
7F80
0000
0000
0000
0000
ENDCHAR

STARTCHAR oe
ENCODING 339
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
8880
8880
8880
8F80
8800
8800
8880
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Racute
ENCODING 340
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
7E00
4100
4100
4100
4100
4100
7E00
6000
5000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR racute
ENCODING 341
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
4F00
5000
6000
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Rcommaaccent
ENCODING 342
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
4100
7E00
6000
5000
4800
4400
4200
4100
0000
0800
0800
1000
ENDCHAR

STARTCHAR rcommaaccent
ENCODING 343
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4F00
5000
6000
4000
4000
4000
4000
4000
4000
0000
4000
4000
8000
ENDCHAR

STARTCHAR Rcaron
ENCODING 344
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
7E00
4100
4100
4100
4100
4100
7E00
6000
5000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR rcaron
ENCODING 345
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
4F00
5000
6000
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Sacute
ENCODING 346
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
3E00
4100
4100
4000
4000
4000
3E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR sacute
ENCODING 347
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
4100
4000
4000
3E00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Scircumflex
ENCODING 348
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
3E00
4100
4100
4000
4000
4000
3E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR scircumflex
ENCODING 349
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
3E00
4100
4000
4000
3E00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Scedilla
ENCODING 350
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
3E00
0100
0100
0100
4100
4100
3E00
0800
0800
1000
0000
ENDCHAR

STARTCHAR scedilla
ENCODING 351
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
3E00
0100
0100
4100
3E00
0800
0800
1000
0000
ENDCHAR

STARTCHAR Scaron
ENCODING 352
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
3E00
4100
4100
4000
4000
4000
3E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR scaron
ENCODING 353
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
4100
4000
4000
3E00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Tcedilla
ENCODING 354
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0400
0400
0800
0000
ENDCHAR

STARTCHAR tcedilla
ENCODING 355
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
3E00
0800
0800
0800
0800
0800
0800
0800
0700
0200
0200
0400
0000
ENDCHAR

STARTCHAR Tcaron
ENCODING 356
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR tcaron
ENCODING 357
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
0800
0800
0800
0800
3E00
0800
0800
0800
0800
0800
0800
0800
0700
0000
0000
0000
0000
ENDCHAR

STARTCHAR Tbar
ENCODING 358
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0800
0800
0800
0800
0800
3E00
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR tbar
ENCODING 359
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
3E00
0800
1C00
0800
0800
0800
0800
0800
0700
0000
0000
0000
0000
ENDCHAR

STARTCHAR Utilde
ENCODING 360
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR utilde
ENCODING 361
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
3300
4C00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Umacron
ENCODING 362
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR umacron
ENCODING 363
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ubreve
ENCODING 364
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR ubreve
ENCODING 365
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Uring
ENCODING 366
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
1C00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uring
ENCODING 367
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
1C00
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Uhungarumlaut
ENCODING 368
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1100
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uhungarumlaut
ENCODING 369
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1100
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Uogonek
ENCODING 370
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0800
1000
0C00
0000
ENDCHAR

STARTCHAR uogonek
ENCODING 371
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0200
0180
0000
ENDCHAR

STARTCHAR Wcircumflex
ENCODING 372
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
4080
4080
4080
4080
4080
4080
4080
4C80
4C80
5280
5280
6180
4080
0000
0000
0000
0000
ENDCHAR

STARTCHAR wcircumflex
ENCODING 373
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1C00
2200
0000
4100
4100
4100
4900
4900
4900
4900
4900
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ydieresis
ENCODING 376
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR Zacute
ENCODING 377
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
7F00
0100
0100
0100
0200
0400
0800
1000
2000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR zacute
ENCODING 378
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
7F00
0100
0200
0400
0800
1000
2000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Zdotaccent
ENCODING 379
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0000
7F00
0100
0100
0100
0200
0400
0800
1000
2000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR zdotaccent
ENCODING 380
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0800
0800
0000
7F00
0100
0200
0400
0800
1000
2000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Zcaron
ENCODING 381
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
7F00
0100
0100
0100
0200
0400
0800
1000
2000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR zcaron
ENCODING 382
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
7F00
0100
0200
0400
0800
1000
2000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR longs
ENCODING 383
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0700
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0186
ENCODING 390
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
0100
0100
0100
0100
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni018E
ENCODING 398
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0100
0100
0100
0100
0100
1F00
0100
0100
0100
0100
0100
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Schwa
ENCODING 399
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
0100
0100
0100
7F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0190
ENCODING 400
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
3C00
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR florin
ENCODING 402
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0600
0900
0900
0800
0800
3E00
0800
0800
0800
0800
0800
0800
0800
4800
4800
3000
0000
ENDCHAR

STARTCHAR uni019D
ENCODING 413
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
4000
4000
8000
0000
ENDCHAR

STARTCHAR uni019E
ENCODING 414
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0100
0100
0100
0000
ENDCHAR

STARTCHAR Ezh
ENCODING 439
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0100
0200
0400
0800
1E00
0100
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Scommaaccent
ENCODING 536
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
3E00
0100
0100
0100
4100
4100
3E00
0000
0800
0800
1000
ENDCHAR

STARTCHAR scommaaccent
ENCODING 537
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
3E00
0100
0100
4100
3E00
0000
0800
0800
1000
ENDCHAR

STARTCHAR Tcommaaccent
ENCODING 538
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0800
0800
1000
ENDCHAR

STARTCHAR tcommaaccent
ENCODING 539
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
3E00
0800
0800
0800
0800
0800
0800
0800
0700
0000
0200
0200
0400
ENDCHAR

STARTCHAR uni0232
ENCODING 562
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0233
ENCODING 563
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR dotlessj
ENCODING 567
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0600
0200
0200
0200
0200
0200
0200
0200
0200
2200
2200
1C00
0000
ENDCHAR

STARTCHAR uni0254
ENCODING 596
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
0100
0100
0100
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0258
ENCODING 600
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
7F00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR schwa
ENCODING 601
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
0100
0100
7F00
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni025B
ENCODING 603
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
3C00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0272
ENCODING 626
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
4000
4000
8000
0000
ENDCHAR

STARTCHAR ezh
ENCODING 658
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
0100
0200
0400
0800
1E00
0100
0100
0100
4100
4100
3E00
0000
ENDCHAR

STARTCHAR commaturnedmod
ENCODING 699
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii57929
ENCODING 700
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
1000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii64937
ENCODING 701
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0400
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR circumflex
ENCODING 710
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR caron
ENCODING 711
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR breve
ENCODING 728
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR dotaccent
ENCODING 729
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR ogonek
ENCODING 731
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0100
0200
0180
0000
ENDCHAR

STARTCHAR tilde
ENCODING 732
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR hungarumlaut
ENCODING 733
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1100
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR gravecomb
ENCODING 768
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR acutecomb
ENCODING 769
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0302
ENCODING 770
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1C00
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR tildecomb
ENCODING 771
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0306
ENCODING 774
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni030C
ENCODING 780
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0329
ENCODING 809
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0800
0800
0000
ENDCHAR

STARTCHAR tonos
ENCODING 900
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR dieresistonos
ENCODING 901
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0400
0800
0000
2200
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Alphatonos
ENCODING 902
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR anoteleia
ENCODING 903
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Epsilontonos
ENCODING 904
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Etatonos
ENCODING 905
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
4100
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Iotatonos
ENCODING 906
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Omicrontonos
ENCODING 908
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Upsilontonos
ENCODING 910
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
2080
2080
1100
1100
0A00
0A00
0400
0400
0400
0400
0400
0400
0400
0000
0000
0000
0000
ENDCHAR

STARTCHAR Omegatonos
ENCODING 911
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4000
8000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
2200
1400
1400
7700
0000
0000
0000
0000
ENDCHAR

STARTCHAR iotadieresistonos
ENCODING 912
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0400
0800
0000
2200
2200
0000
1800
0800
0800
0800
0800
0800
0800
0800
0600
0000
0000
0000
0000
ENDCHAR

STARTCHAR Alpha
ENCODING 913
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Beta
ENCODING 914
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
7E00
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Gamma
ENCODING 915
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Delta
ENCODING 916
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
1400
1400
1400
2200
2200
2200
4100
4100
4100
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Epsilon
ENCODING 917
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Zeta
ENCODING 918
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0100
0100
0100
0200
0400
0800
1000
2000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Eta
ENCODING 919
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Theta
ENCODING 920
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
5D00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Iota
ENCODING 921
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Kappa
ENCODING 922
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Lambda
ENCODING 923
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
1400
1400
1400
2200
2200
2200
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Mu
ENCODING 924
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4080
6180
5280
5280
4C80
4C80
4080
4080
4080
4080
4080
4080
4080
0000
0000
0000
0000
ENDCHAR

STARTCHAR Nu
ENCODING 925
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
6100
5100
4900
4500
4300
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Xi
ENCODING 926
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0000
0000
0000
0000
0000
3E00
0000
0000
0000
0000
0000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Omicron
ENCODING 927
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Pi
ENCODING 928
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Rho
ENCODING 929
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
4100
7E00
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Sigma
ENCODING 931
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
2000
1000
0800
0400
0200
0400
0800
1000
2000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Tau
ENCODING 932
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR Upsilon
ENCODING 933
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR Phi
ENCODING 934
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
3E00
4900
4900
4900
4900
4900
4900
4900
4900
4900
3E00
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR Chi
ENCODING 935
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
1400
1400
2200
2200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Psi
ENCODING 936
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4900
4900
4900
4900
4900
4900
4900
4900
4900
3E00
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR Omega
ENCODING 937
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
2200
1400
1400
7700
0000
0000
0000
0000
ENDCHAR

STARTCHAR Iotadieresis
ENCODING 938
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR Upsilondieresis
ENCODING 939
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR alphatonos
ENCODING 940
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3D80
4300
4200
4200
4200
4200
4200
4300
3D80
0000
0000
0000
0000
ENDCHAR

STARTCHAR epsilontonos
ENCODING 941
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
4100
4000
4000
3C00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR etatonos
ENCODING 942
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0100
0100
0100
0000
ENDCHAR

STARTCHAR iotatonos
ENCODING 943
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
1800
0800
0800
0800
0800
0800
0800
0800
0600
0000
0000
0000
0000
ENDCHAR

STARTCHAR upsilondieresistonos
ENCODING 944
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0400
0800
0000
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR alpha
ENCODING 945
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3D80
4300
4200
4200
4200
4200
4200
4300
3D80
0000
0000
0000
0000
ENDCHAR

STARTCHAR beta
ENCODING 946
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3C00
4200
4200
4200
4400
7E00
4100
4100
4100
4100
4100
4100
7E00
4000
4000
4000
0000
ENDCHAR

STARTCHAR gamma
ENCODING 947
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
2200
2200
2200
1400
1400
0800
0800
0800
0800
0000
ENDCHAR

STARTCHAR delta
ENCODING 948
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
1000
0800
0400
1E00
2300
4100
4100
4100
4100
4100
2200
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR epsilon
ENCODING 949
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
3C00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR zeta
ENCODING 950
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0200
0400
0800
1000
2000
2000
4000
4000
4000
4000
4000
3E00
0100
0100
0200
0000
ENDCHAR

STARTCHAR eta
ENCODING 951
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0100
0100
0100
0000
ENDCHAR

STARTCHAR theta
ENCODING 952
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1E00
2100
2100
2100
2100
2100
3F00
2100
2100
2100
2100
2100
1E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR iota
ENCODING 953
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
0600
0000
0000
0000
0000
ENDCHAR

STARTCHAR kappa
ENCODING 954
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4200
4400
4800
7000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR lambda
ENCODING 955
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1000
1000
0800
0800
0800
1400
1400
2200
2200
2200
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR mugreek
ENCODING 956
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4300
7D00
4000
4000
4000
0000
ENDCHAR

STARTCHAR nu
ENCODING 957
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
2200
2200
1400
1400
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR xi
ENCODING 958
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3F00
4000
4000
4000
4000
3E00
4000
4000
4000
4000
4000
4000
3E00
0100
0100
0200
0000
ENDCHAR

STARTCHAR omicron
ENCODING 959
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR pi
ENCODING 960
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR rho
ENCODING 961
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
7E00
4000
4000
4000
0000
ENDCHAR

STARTCHAR sigma1
ENCODING 962
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
4000
4000
4000
4000
3E00
0100
0100
0200
0000
ENDCHAR

STARTCHAR sigma
ENCODING 963
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
1F80
2200
4100
4100
4100
4100
4100
2200
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR tau
ENCODING 964
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0600
0000
0000
0000
0000
ENDCHAR

STARTCHAR upsilon
ENCODING 965
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR phi
ENCODING 966
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
2600
4900
4900
4900
4900
4900
4900
4900
3E00
0800
0800
0800
0000
ENDCHAR

STARTCHAR chi
ENCODING 967
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
2200
2200
1400
0800
0800
1400
2200
2200
4100
4100
0000
ENDCHAR

STARTCHAR psi
ENCODING 968
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4900
4900
4900
4900
4900
4900
4900
4900
3E00
0800
0800
0800
0000
ENDCHAR

STARTCHAR omega
ENCODING 969
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
2200
4100
4100
4900
4900
4900
4900
4900
3600
0000
0000
0000
0000
ENDCHAR

STARTCHAR iotadieresis
ENCODING 970
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
1800
0800
0800
0800
0800
0800
0800
0800
0600
0000
0000
0000
0000
ENDCHAR

STARTCHAR upsilondieresis
ENCODING 971
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR omicrontonos
ENCODING 972
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR upsilontonos
ENCODING 973
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR omegatonos
ENCODING 974
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
2200
4100
4100
4900
4900
4900
4900
4900
3600
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni03F3
ENCODING 1011
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0200
0200
0000
0000
0600
0200
0200
0200
0200
0200
0200
0200
0200
2200
2200
1C00
0000
ENDCHAR

STARTCHAR uni03F4
ENCODING 1012
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0400
ENCODING 1024
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10023
ENCODING 1025
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10051
ENCODING 1026
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
F800
2000
2000
2000
3E00
2100
2100
2100
2100
2100
2100
2100
2600
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10052
ENCODING 1027
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
7F00
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10053
ENCODING 1028
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
7C00
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10054
ENCODING 1029
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
3E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10055
ENCODING 1030
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10056
ENCODING 1031
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10057
ENCODING 1032
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0380
0100
0100
0100
0100
0100
0100
0100
0100
2100
2100
2100
1E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10058
ENCODING 1033
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3800
4800
8800
8800
8800
8F00
8880
8880
8880
8880
8880
8880
8F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10059
ENCODING 1034
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
8800
8800
8800
8800
8800
8F00
F880
8880
8880
8880
8880
8880
8F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10060
ENCODING 1035
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
F800
2000
2000
2000
3E00
2100
2100
2100
2100
2100
2100
2100
2100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10061
ENCODING 1036
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0400
0800
0000
4100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni040D
ENCODING 1037
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1000
0800
0000
4100
4100
4100
4100
4300
4500
4900
5100
6100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10062
ENCODING 1038
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10145
ENCODING 1039
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
7F00
0800
0800
0000
0000
ENDCHAR

STARTCHAR afii10017
ENCODING 1040
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10018
ENCODING 1041
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10019
ENCODING 1042
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
7E00
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10020
ENCODING 1043
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10021
ENCODING 1044
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0F00
1100
2100
2100
2100
2100
2100
2100
2100
2100
2100
2100
7F80
4080
4080
0000
0000
ENDCHAR

STARTCHAR afii10022
ENCODING 1045
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10024
ENCODING 1046
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4900
4900
4900
4900
4900
2A00
1C00
2A00
4900
4900
4900
4900
4900
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10025
ENCODING 1047
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
0100
0100
0100
1E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10026
ENCODING 1048
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4300
4500
4900
5100
6100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10027
ENCODING 1049
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
4100
4100
4100
4100
4300
4500
4900
5100
6100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10028
ENCODING 1050
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10029
ENCODING 1051
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0F00
1100
2100
2100
2100
2100
2100
2100
2100
2100
2100
2100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10030
ENCODING 1052
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4080
6180
5280
5280
4C80
4C80
4080
4080
4080
4080
4080
4080
4080
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10031
ENCODING 1053
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10032
ENCODING 1054
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10033
ENCODING 1055
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10034
ENCODING 1056
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7E00
4100
4100
4100
4100
4100
7E00
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10035
ENCODING 1057
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10036
ENCODING 1058
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10037
ENCODING 1059
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10038
ENCODING 1060
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0800
3E00
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
3E00
0800
0000
0000
0000
ENDCHAR

STARTCHAR afii10039
ENCODING 1061
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
1400
1400
2200
2200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10040
ENCODING 1062
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3F80
0080
0080
0000
0000
ENDCHAR

STARTCHAR afii10041
ENCODING 1063
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10042
ENCODING 1064
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10043
ENCODING 1065
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
4900
3F80
0080
0080
0000
0000
ENDCHAR

STARTCHAR afii10044
ENCODING 1066
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
C000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10045
ENCODING 1067
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4080
4080
4080
4080
7880
4480
4480
4480
4480
4480
4480
4480
7880
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10046
ENCODING 1068
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10047
ENCODING 1069
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
0100
0100
0100
1F00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10048
ENCODING 1070
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4700
4880
4880
4880
4880
4880
7880
4880
4880
4880
4880
4880
4700
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10049
ENCODING 1071
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3F00
4100
4100
4100
4100
4100
3F00
0300
0500
0900
1100
2100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10065
ENCODING 1072
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10066
ENCODING 1073
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10067
ENCODING 1074
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3C00
4200
4200
4200
4400
7E00
4100
4100
4100
4100
4100
4100
7E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10068
ENCODING 1075
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
0100
0100
0100
3E00
4000
4000
4000
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10069
ENCODING 1076
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3F00
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR afii10070
ENCODING 1077
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10072
ENCODING 1078
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4900
4900
4900
2A00
1C00
2A00
4900
4900
4900
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10073
ENCODING 1079
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
0100
0100
1E00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10074
ENCODING 1080
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10075
ENCODING 1081
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10076
ENCODING 1082
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4200
4400
4800
7000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10077
ENCODING 1083
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0F00
1100
2100
2100
2100
2100
2100
2100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10078
ENCODING 1084
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
6300
5500
4900
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10079
ENCODING 1085
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
7F00
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10080
ENCODING 1086
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10081
ENCODING 1087
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10082
ENCODING 1088
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7E00
4100
4100
4100
4100
4100
4100
4100
7E00
4000
4000
4000
0000
ENDCHAR

STARTCHAR afii10083
ENCODING 1089
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10084
ENCODING 1090
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10085
ENCODING 1091
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR afii10086
ENCODING 1092
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
0800
3E00
4900
4900
4900
4900
4900
4900
4900
3E00
0800
0800
0000
0000
ENDCHAR

STARTCHAR afii10087
ENCODING 1093
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
2200
1400
0800
1400
2200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10088
ENCODING 1094
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F80
0080
0080
0000
0000
ENDCHAR

STARTCHAR afii10089
ENCODING 1095
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
3F00
0100
0100
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10090
ENCODING 1096
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4900
4900
4900
4900
4900
4900
4900
4900
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10091
ENCODING 1097
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4900
4900
4900
4900
4900
4900
4900
4900
3F80
0080
0080
0000
0000
ENDCHAR

STARTCHAR afii10092
ENCODING 1098
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
6000
2000
2000
3E00
2100
2100
2100
2100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10093
ENCODING 1099
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4080
4080
4080
7880
4480
4480
4480
4480
7880
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10094
ENCODING 1100
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
2000
2000
2000
3E00
2100
2100
2100
2100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10095
ENCODING 1101
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
0100
0100
1F00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10096
ENCODING 1102
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4700
4880
4880
4880
7880
4880
4880
4880
4700
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10097
ENCODING 1103
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3F00
4100
4100
4100
3F00
0500
0900
1100
2100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0450
ENCODING 1104
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1000
0800
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10071
ENCODING 1105
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10099
ENCODING 1106
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
F800
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0100
0100
0600
0000
ENDCHAR

STARTCHAR afii10100
ENCODING 1107
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
3E00
0100
0100
0100
3E00
4000
4000
4000
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10101
ENCODING 1108
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
7C00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10102
ENCODING 1109
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
3E00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10103
ENCODING 1110
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10104
ENCODING 1111
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10105
ENCODING 1112
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0200
0200
0000
0000
0600
0200
0200
0200
0200
0200
0200
0200
0200
2200
2200
1C00
0000
ENDCHAR

STARTCHAR afii10106
ENCODING 1113
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3800
4800
8800
8F00
8880
8880
8880
8880
8F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10107
ENCODING 1114
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
8800
8800
8800
8F00
F880
8880
8880
8880
8F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10108
ENCODING 1115
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
F800
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10109
ENCODING 1116
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0400
0800
0000
4100
4200
4400
4800
7000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni045D
ENCODING 1117
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1000
0800
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10110
ENCODING 1118
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR afii10193
ENCODING 1119
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
7F00
0800
0800
0000
0000
ENDCHAR

STARTCHAR afii10050
ENCODING 1168
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0100
0100
7F00
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10098
ENCODING 1169
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
2000
2000
3E00
0100
0100
0100
3E00
4000
4000
4000
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0492
ENCODING 1170
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
F800
4000
4000
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0493
ENCODING 1171
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
4000
4000
4000
F800
4000
4000
4000
4000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni0494
ENCODING 1172
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
0100
0200
0000
0000
ENDCHAR

STARTCHAR uni0495
ENCODING 1173
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
4000
4000
4000
7C00
4200
4200
4200
4200
0200
0400
0000
0000
ENDCHAR

STARTCHAR uni0496
ENCODING 1174
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4900
4900
4900
4900
4900
2A00
1C00
2A00
4900
4900
4900
4900
4980
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni0497
ENCODING 1175
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4900
4900
4900
2A00
1C00
2A00
4900
4900
4980
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni0498
ENCODING 1176
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
0100
0100
0100
1E00
0100
0100
0100
4100
4100
3E00
0800
0800
0800
0000
ENDCHAR

STARTCHAR uni0499
ENCODING 1177
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
0100
0100
1E00
0100
0100
4100
3E00
0800
0800
0800
0000
ENDCHAR

STARTCHAR uni049A
ENCODING 1178
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni049B
ENCODING 1179
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4200
4400
4800
7000
4800
4400
4200
4180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni049C
ENCODING 1180
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
5200
5400
5800
7000
5800
5400
5200
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni049D
ENCODING 1181
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
5200
5400
5800
7000
5800
5400
5200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04A0
ENCODING 1184
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
C100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04A1
ENCODING 1185
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
C100
4200
4400
4800
7000
4800
4400
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04A2
ENCODING 1186
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni04A3
ENCODING 1187
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
7F00
4100
4100
4100
4180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni04A4
ENCODING 1188
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
43C0
4200
4200
4200
4200
4200
7E00
4200
4200
4200
4200
4200
4200
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04A5
ENCODING 1189
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
43C0
4200
4200
4200
7E00
4200
4200
4200
4200
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04AA
ENCODING 1194
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4000
4000
4000
4000
4000
4000
4000
4100
4100
3E00
0800
0800
0800
0000
ENDCHAR

STARTCHAR uni04AB
ENCODING 1195
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4000
4000
4000
4000
4000
4100
3E00
0800
0800
0800
0000
ENDCHAR

STARTCHAR uni04AE
ENCODING 1198
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04AF
ENCODING 1199
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
2200
2200
2200
1400
1400
0800
0800
0800
0800
0000
ENDCHAR

STARTCHAR uni04B0
ENCODING 1200
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
0800
3E00
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04B1
ENCODING 1201
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
2200
2200
2200
1400
1400
0800
3E00
0800
0800
0000
ENDCHAR

STARTCHAR uni04B2
ENCODING 1202
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
2200
2200
1400
1400
0800
1400
1400
2200
2200
4100
4180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni04B3
ENCODING 1203
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
2200
1400
0800
1400
2200
4100
4180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni04B6
ENCODING 1206
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
0100
0180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni04B7
ENCODING 1207
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
3F00
0100
0100
0100
0180
0080
0080
0000
0000
ENDCHAR

STARTCHAR uni04B8
ENCODING 1208
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4900
4900
4900
3F00
0900
0900
0900
0100
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04B9
ENCODING 1209
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4900
4900
3F00
0900
0900
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04BA
ENCODING 1210
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04BB
ENCODING 1211
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D0
ENCODING 1232
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D1
ENCODING 1233
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D2
ENCODING 1234
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D3
ENCODING 1235
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
0100
0100
3F00
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D4
ENCODING 1236
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F80
8800
8800
8800
8800
8800
FF00
8800
8800
8800
8800
8800
8F80
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D5
ENCODING 1237
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7700
0880
0880
7880
8F80
8800
8800
8880
7700
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D6
ENCODING 1238
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
1C00
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D7
ENCODING 1239
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
1C00
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04D8
ENCODING 1240
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
0100
0100
0100
7F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii10846
ENCODING 1241
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
0100
0100
7F00
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04DA
ENCODING 1242
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
0100
0100
0100
7F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04DB
ENCODING 1243
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
0100
0100
7F00
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04DC
ENCODING 1244
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4900
4900
4900
4900
4900
2A00
1C00
2A00
4900
4900
4900
4900
4900
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04DD
ENCODING 1245
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4900
4900
4900
2A00
1C00
2A00
4900
4900
4900
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04DE
ENCODING 1246
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
0100
0100
0100
1E00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04DF
ENCODING 1247
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
0100
0100
1E00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E2
ENCODING 1250
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
4100
4100
4100
4100
4300
4500
4900
5100
6100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E3
ENCODING 1251
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E4
ENCODING 1252
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4100
4100
4100
4100
4300
4500
4900
5100
6100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E5
ENCODING 1253
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E6
ENCODING 1254
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E7
ENCODING 1255
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E8
ENCODING 1256
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04E9
ENCODING 1257
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
7F00
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04EA
ENCODING 1258
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
4100
4100
4100
7F00
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04EB
ENCODING 1259
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
4100
4100
7F00
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04EC
ENCODING 1260
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
3E00
4100
4100
0100
0100
0100
1F00
0100
0100
0100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04ED
ENCODING 1261
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
3E00
4100
0100
0100
1F00
0100
0100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04EE
ENCODING 1262
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3E00
0000
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04EF
ENCODING 1263
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR uni04F0
ENCODING 1264
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04F1
ENCODING 1265
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR uni04F2
ENCODING 1266
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1100
2200
0000
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04F3
ENCODING 1267
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1100
2200
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR uni04F4
ENCODING 1268
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4100
4100
4100
4100
4100
4100
3F00
0100
0100
0100
0100
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04F5
ENCODING 1269
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4100
4100
4100
4100
3F00
0100
0100
0100
0100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04F8
ENCODING 1272
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
2200
2200
0000
4080
4080
4080
4080
7880
4480
4480
4480
4480
4480
4480
4480
7880
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni04F9
ENCODING 1273
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
2200
2200
0000
4080
4080
4080
7880
4480
4480
4480
4480
7880
0000
0000
0000
0000
ENDCHAR

STARTCHAR Klinebelow
ENCODING 7732
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4200
4400
4800
5000
6000
5000
4800
4400
4200
4100
4100
0000
3E00
0000
0000
ENDCHAR

STARTCHAR klinebelow
ENCODING 7733
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
4100
4200
4400
4800
7000
4800
4400
4200
4100
0000
3E00
0000
0000
ENDCHAR

STARTCHAR Edotbelow
ENCODING 7864
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0800
0800
0000
ENDCHAR

STARTCHAR edotbelow
ENCODING 7865
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0800
0800
0000
ENDCHAR

STARTCHAR Etilde
ENCODING 7868
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
7F00
4000
4000
4000
4000
4000
7C00
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR etilde
ENCODING 7869
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
3300
4C00
0000
3E00
4100
4100
4100
7F00
4000
4000
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni1ECA
ENCODING 7882
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0800
0800
0000
ENDCHAR

STARTCHAR uni1ECB
ENCODING 7883
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0000
0000
1800
0800
0800
0800
0800
0800
0800
0800
1C00
0000
0800
0800
0000
ENDCHAR

STARTCHAR Odotbelow
ENCODING 7884
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0800
0800
0000
ENDCHAR

STARTCHAR odotbelow
ENCODING 7885
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0800
0800
0000
ENDCHAR

STARTCHAR uni1EE4
ENCODING 7908
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0800
0800
0000
ENDCHAR

STARTCHAR uni1EE5
ENCODING 7909
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0000
0800
0800
0000
ENDCHAR

STARTCHAR Ytilde
ENCODING 7928
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
3300
4C00
0000
4100
4100
2200
2200
1400
1400
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR ytilde
ENCODING 7929
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
3300
4C00
0000
4100
4100
4100
4100
4100
4100
4100
4100
3F00
0100
0100
3E00
0000
ENDCHAR

STARTCHAR uni2000
ENCODING 8192
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2001
ENCODING 8193
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR enspace
ENCODING 8194
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2003
ENCODING 8195
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2004
ENCODING 8196
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2005
ENCODING 8197
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2006
ENCODING 8198
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2007
ENCODING 8199
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2008
ENCODING 8200
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2009
ENCODING 8201
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni200A
ENCODING 8202
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR hyphentwo
ENCODING 8208
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
3E00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2011
ENCODING 8209
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
3E00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR figuredash
ENCODING 8210
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR endash
ENCODING 8211
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR emdash
ENCODING 8212
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F80
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii00208
ENCODING 8213
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F80
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR dblverticalbar
ENCODING 8214
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
0000
0000
0000
0000
ENDCHAR

STARTCHAR underscoredbl
ENCODING 8215
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F00
0000
7F00
ENDCHAR

STARTCHAR quoteleft
ENCODING 8216
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0400
0800
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR quoteright
ENCODING 8217
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0800
0800
0800
1000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR quotesinglbase
ENCODING 8218
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0800
0800
0800
1000
0000
0000
0000
ENDCHAR

STARTCHAR quotereversed
ENCODING 8219
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1000
1000
1000
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR quotedblleft
ENCODING 8220
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1100
2200
2200
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR quotedblright
ENCODING 8221
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
1100
1100
1100
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR quotedblbase
ENCODING 8222
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
2200
2200
2200
4400
0000
0000
0000
ENDCHAR

STARTCHAR uni201F
ENCODING 8223
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
4400
4400
4400
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR dagger
ENCODING 8224
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
7F00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR daggerdbl
ENCODING 8225
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
7F00
0800
0800
0800
0800
0800
0800
0800
7F00
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR bullet
ENCODING 8226
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0C00
1E00
1E00
0C00
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR ellipsis
ENCODING 8230
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
4900
4900
0000
0000
0000
0000
ENDCHAR

STARTCHAR perthousand
ENCODING 8240
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
E400
A400
E800
0800
1000
1000
2000
2000
4000
5DC0
9540
9DC0
0000
0000
0000
0000
ENDCHAR

STARTCHAR minute
ENCODING 8242
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0800
0800
0800
0800
0800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR second
ENCODING 8243
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
2200
2200
2200
2200
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR guilsinglleft
ENCODING 8249
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0200
0400
0800
1000
2000
1000
0800
0400
0200
0000
0000
0000
0000
ENDCHAR

STARTCHAR guilsinglright
ENCODING 8250
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
2000
1000
0800
0400
0200
0400
0800
1000
2000
0000
0000
0000
0000
ENDCHAR

STARTCHAR exclamdbl
ENCODING 8252
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
2200
2200
2200
2200
2200
2200
2200
2200
2200
0000
0000
2200
2200
0000
0000
0000
0000
ENDCHAR

STARTCHAR overline
ENCODING 8254
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
7F00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR nsuperior
ENCODING 8319
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
3C00
2200
2200
2200
2200
2200
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR peseta
ENCODING 8359
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7800
4400
4400
4400
4400
4400
7A00
4200
4700
4200
4200
4200
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR Euro
ENCODING 8364
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
1E00
2100
4080
4000
FC00
4000
4000
FC00
4000
4080
2100
1E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni20AE
ENCODING 8366
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0800
0800
0800
0800
0E00
3800
0E00
3800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni210E
ENCODING 8462
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
4000
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni210F
ENCODING 8463
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
4000
F800
4000
4000
7E00
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR afii61352
ENCODING 8470
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
8400
8480
C540
C540
A480
A400
9400
9400
8DC0
8C00
85C0
8400
8400
0000
0000
0000
0000
ENDCHAR

STARTCHAR trademark
ENCODING 8482
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
E880
4D80
4A80
4880
4880
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR Ohm
ENCODING 8486
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
2200
1400
1400
7700
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowleft
ENCODING 8592
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
1000
2000
4000
FF80
4000
2000
1000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowup
ENCODING 8593
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
1C00
2A00
4900
0800
0800
0800
0800
0800
0800
0800
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowright
ENCODING 8594
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0400
0200
0100
FF80
0100
0200
0400
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowdown
ENCODING 8595
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
0800
0800
0800
0800
0800
0800
4900
2A00
1C00
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowboth
ENCODING 8596
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
1200
2100
4080
FFC0
4080
2100
1200
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowupdn
ENCODING 8597
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
1C00
2A00
4900
0800
0800
0800
0800
0800
4900
2A00
1C00
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowupdnbse
ENCODING 8616
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
1C00
2A00
4900
0800
0800
0800
0800
4900
2A00
1C00
0800
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR carriagereturn
ENCODING 8629
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0080
0080
0080
0080
0080
0080
1080
2080
4080
FF80
4000
2000
1000
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowdblleft
ENCODING 8656
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
1000
2000
7F80
C000
7F80
2000
1000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowdblup
ENCODING 8657
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
1C00
3600
5500
1400
1400
1400
1400
1400
1400
1400
1400
1400
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowdblright
ENCODING 8658
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0400
0200
FF00
0180
FF00
0200
0400
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowdbldown
ENCODING 8659
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1400
1400
1400
1400
1400
1400
1400
1400
1400
5500
3600
1C00
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR arrowdblboth
ENCODING 8660
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
1200
2100
7F80
C0C0
7F80
2100
1200
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni21D5
ENCODING 8661
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
1C00
3600
5500
1400
1400
1400
1400
1400
5500
3600
1C00
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR existential
ENCODING 8707
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
0100
0100
0100
0100
0100
7F00
0100
0100
0100
0100
0100
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR emptyset
ENCODING 8709
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0100
0200
3E00
4500
4500
4900
5100
5100
3E00
2000
4000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR increment
ENCODING 8710
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
0800
1400
1400
1400
2200
2200
2200
4100
4100
4100
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR element
ENCODING 8712
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0F00
1000
2000
4000
4000
4000
7F00
4000
4000
4000
2000
1000
0F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni220A
ENCODING 8714
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
1F00
2000
4000
4000
7F00
4000
4000
2000
1F00
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR minus
ENCODING 8722
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR bulletoperator
ENCODING 8729
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0C00
0C00
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR radical
ENCODING 8730
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0380
0200
0200
0200
0200
0200
0200
0200
4200
4200
4200
2200
1200
0A00
0600
0000
0000
0000
0000
ENDCHAR

STARTCHAR infinity
ENCODING 8734
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
3600
4900
4900
4900
4900
3600
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR orthogonal
ENCODING 8735
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
4000
4000
4000
4000
4000
4000
7F00
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR logicaland
ENCODING 8743
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0800
0800
1400
1400
2200
2200
2200
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR logicalor
ENCODING 8744
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
4100
4100
2200
2200
2200
1400
1400
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR intersection
ENCODING 8745
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
0000
0000
0000
0000
ENDCHAR

STARTCHAR union
ENCODING 8746
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR approxequal
ENCODING 8776
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
3100
4900
4600
0000
3100
4900
4600
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR notequal
ENCODING 8800
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0100
7F00
0400
0800
1000
7F00
4000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR equivalence
ENCODING 8801
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
7F00
0000
0000
0000
7F00
0000
0000
0000
7F00
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR lessequal
ENCODING 8804
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0200
0400
0800
1000
2000
4000
2000
1000
0800
0400
0200
0000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR greaterequal
ENCODING 8805
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
2000
1000
0800
0400
0200
0100
0200
0400
0800
1000
2000
0000
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR house
ENCODING 8962
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0800
1400
2200
4100
8080
8080
8080
8080
8080
FF80
0000
0000
0000
0000
ENDCHAR

STARTCHAR revlogicalnot
ENCODING 8976
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
7F00
4000
4000
4000
4000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR integraltp
ENCODING 8992
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0600
0900
0900
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR integralbt
ENCODING 8993
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
4800
4800
3000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni23BA
ENCODING 9146
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni23BB
ENCODING 9147
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni23BC
ENCODING 9148
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni23BD
ENCODING 9149
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
ENDCHAR

STARTCHAR uni2409
ENCODING 9225
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4400
4400
4400
7C00
4400
4400
4400
0000
0F80
0200
0200
0200
0200
0200
0200
0000
0000
0000
ENDCHAR

STARTCHAR uni240A
ENCODING 9226
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4000
4000
4000
4000
4000
4000
7C00
0000
0F80
0800
0800
0E00
0800
0800
0800
0000
0000
0000
ENDCHAR

STARTCHAR uni240B
ENCODING 9227
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4400
4400
4400
2800
2800
1000
1000
0000
0F80
0200
0200
0200
0200
0200
0200
0000
0000
0000
ENDCHAR

STARTCHAR uni240C
ENCODING 9228
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
7C00
4000
4000
7000
4000
4000
4000
0000
0F80
0800
0800
0E00
0800
0800
0800
0000
0000
0000
ENDCHAR

STARTCHAR uni240D
ENCODING 9229
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
3800
4400
4000
4000
4000
4400
3800
0000
0F00
0880
0880
0F00
0A00
0900
0880
0000
0000
0000
ENDCHAR

STARTCHAR uni2424
ENCODING 9252
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
4400
4400
6400
5400
4C00
4400
4400
0000
0800
0800
0800
0800
0800
0800
0F80
0000
0000
0000
ENDCHAR

STARTCHAR SF100000
ENCODING 9472
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2501
ENCODING 9473
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF110000
ENCODING 9474
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2503
ENCODING 9475
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2508
ENCODING 9480
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
D680
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2509
ENCODING 9481
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
D680
D680
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni250A
ENCODING 9482
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0000
0800
0800
0800
0800
0000
0800
0800
0800
0800
0000
0800
0800
0800
0800
0000
ENDCHAR

STARTCHAR uni250B
ENCODING 9483
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0000
0C00
0C00
0C00
0C00
0000
0C00
0C00
0C00
0C00
0000
0C00
0C00
0C00
0C00
0000
ENDCHAR

STARTCHAR SF010000
ENCODING 9484
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni250D
ENCODING 9485
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0FC0
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni250E
ENCODING 9486
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni250F
ENCODING 9487
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0FC0
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR SF030000
ENCODING 9488
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2511
ENCODING 9489
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
F800
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2512
ENCODING 9490
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2513
ENCODING 9491
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FC00
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR SF020000
ENCODING 9492
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2515
ENCODING 9493
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2516
ENCODING 9494
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2517
ENCODING 9495
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0FC0
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF040000
ENCODING 9496
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
F800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2519
ENCODING 9497
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
F800
F800
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni251A
ENCODING 9498
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FC00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni251B
ENCODING 9499
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FC00
FC00
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF080000
ENCODING 9500
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni251D
ENCODING 9501
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni251E
ENCODING 9502
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni251F
ENCODING 9503
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2520
ENCODING 9504
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2521
ENCODING 9505
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0FC0
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2522
ENCODING 9506
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2523
ENCODING 9507
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0FC0
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR SF090000
ENCODING 9508
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2525
ENCODING 9509
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
F800
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2526
ENCODING 9510
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FC00
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2527
ENCODING 9511
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2528
ENCODING 9512
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2529
ENCODING 9513
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FC00
FC00
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni252A
ENCODING 9514
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FC00
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni252B
ENCODING 9515
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FC00
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR SF060000
ENCODING 9516
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni252D
ENCODING 9517
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni252E
ENCODING 9518
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni252F
ENCODING 9519
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2530
ENCODING 9520
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2531
ENCODING 9521
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2532
ENCODING 9522
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2533
ENCODING 9523
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR SF070000
ENCODING 9524
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2535
ENCODING 9525
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
F800
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2536
ENCODING 9526
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2537
ENCODING 9527
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2538
ENCODING 9528
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2539
ENCODING 9529
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
FC00
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni253A
ENCODING 9530
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni253B
ENCODING 9531
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF050000
ENCODING 9532
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni253D
ENCODING 9533
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni253E
ENCODING 9534
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni253F
ENCODING 9535
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2540
ENCODING 9536
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2541
ENCODING 9537
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2542
ENCODING 9538
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2543
ENCODING 9539
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2544
ENCODING 9540
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2545
ENCODING 9541
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2546
ENCODING 9542
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2547
ENCODING 9543
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR uni2548
ENCODING 9544
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
FFC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni2549
ENCODING 9545
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
FC00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni254A
ENCODING 9546
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
0FC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR uni254B
ENCODING 9547
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
FFC0
FFC0
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
0C00
ENDCHAR

STARTCHAR SF430000
ENCODING 9552
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0000
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF240000
ENCODING 9553
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF510000
ENCODING 9554
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0FC0
0800
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF520000
ENCODING 9555
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
1FC0
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF390000
ENCODING 9556
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
1FC0
1000
17C0
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF220000
ENCODING 9557
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
F800
0800
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF210000
ENCODING 9558
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FC00
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF250000
ENCODING 9559
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
FC00
0400
F400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF500000
ENCODING 9560
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0800
0FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF490000
ENCODING 9561
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
1400
1FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF380000
ENCODING 9562
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
17C0
1000
1FC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF280000
ENCODING 9563
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
F800
0800
F800
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF270000
ENCODING 9564
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
1400
FC00
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF260000
ENCODING 9565
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
F400
0400
FC00
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF360000
ENCODING 9566
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
0FC0
0800
0FC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF370000
ENCODING 9567
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
1400
17C0
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF420000
ENCODING 9568
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
17C0
1000
17C0
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF190000
ENCODING 9569
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
F800
0800
F800
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF200000
ENCODING 9570
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
1400
F400
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF230000
ENCODING 9571
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
F400
0400
F400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF470000
ENCODING 9572
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0000
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF480000
ENCODING 9573
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF410000
ENCODING 9574
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
0000
F7C0
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF450000
ENCODING 9575
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0000
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF460000
ENCODING 9576
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
1400
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF400000
ENCODING 9577
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
F7C0
0000
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR SF540000
ENCODING 9578
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0800
0800
0800
0800
0800
0800
0800
0800
FFC0
0800
FFC0
0800
0800
0800
0800
0800
0800
0800
0800
0800
ENDCHAR

STARTCHAR SF530000
ENCODING 9579
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
1400
FFC0
1400
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR SF440000
ENCODING 9580
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
1400
1400
1400
1400
1400
1400
1400
1400
F7C0
0000
F7C0
1400
1400
1400
1400
1400
1400
1400
1400
1400
ENDCHAR

STARTCHAR upblock
ENCODING 9600
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2581
ENCODING 9601
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
ENDCHAR

STARTCHAR uni2582
ENCODING 9602
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR uni2583
ENCODING 9603
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR dnblock
ENCODING 9604
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR uni2585
ENCODING 9605
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR uni2586
ENCODING 9606
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR uni2587
ENCODING 9607
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR block
ENCODING 9608
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR lfblock
ENCODING 9612
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
ENDCHAR

STARTCHAR rtblock
ENCODING 9616
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
ENDCHAR

STARTCHAR ltshade
ENCODING 9617
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
AA80
0000
AA80
0000
AA80
0000
AA80
0000
AA80
0000
AA80
0000
AA80
0000
AA80
0000
AA80
0000
AA80
0000
ENDCHAR

STARTCHAR shade
ENCODING 9618
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
AA80
5540
ENDCHAR

STARTCHAR dkshade
ENCODING 9619
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
AA80
FFC0
AA80
FFC0
AA80
FFC0
AA80
FFC0
AA80
FFC0
AA80
FFC0
AA80
FFC0
AA80
FFC0
AA80
FFC0
AA80
ENDCHAR

STARTCHAR uni2596
ENCODING 9622
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
ENDCHAR

STARTCHAR uni2597
ENCODING 9623
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
ENDCHAR

STARTCHAR uni2598
ENCODING 9624
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni2599
ENCODING 9625
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR uni259A
ENCODING 9626
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
ENDCHAR

STARTCHAR uni259B
ENCODING 9627
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
ENDCHAR

STARTCHAR uni259C
ENCODING 9628
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
ENDCHAR

STARTCHAR uni259D
ENCODING 9629
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni259E
ENCODING 9630
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
F800
F800
F800
F800
F800
F800
F800
F800
F800
F800
ENDCHAR

STARTCHAR uni259F
ENCODING 9631
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
07C0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR filledbox
ENCODING 9632
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
3E00
3E00
3E00
3E00
3E00
3E00
3E00
3E00
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR filledrect
ENCODING 9644
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
0000
7F80
7F80
7F80
7F80
7F80
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni25AE
ENCODING 9646
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR triagup
ENCODING 9650
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
0800
1C00
1C00
3E00
3E00
7F00
7F00
FF80
FF80
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni25B6
ENCODING 9654
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
C000
F000
FC00
FF00
FFC0
FF00
FC00
F000
C000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR triagdn
ENCODING 9660
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
FF80
FF80
7F00
7F00
3E00
3E00
1C00
1C00
0800
0800
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uni25C0
ENCODING 9664
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
00C0
03C0
0FC0
3FC0
FFC0
3FC0
0FC0
03C0
00C0
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR blackdiamond
ENCODING 9670
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
1C00
3E00
7F00
FF80
7F00
3E00
1C00
0800
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR lozenge
ENCODING 9674
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
1400
2200
4100
8080
4100
2200
1400
0800
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR circle
ENCODING 9675
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
1E00
2100
2100
2100
2100
1E00
0000
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR invbullet
ENCODING 9688
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
F3C0
E1C0
E1C0
F3C0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR invcircle
ENCODING 9689
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
E1C0
DEC0
DEC0
DEC0
DEC0
E1C0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
FFC0
ENDCHAR

STARTCHAR smileface
ENCODING 9786
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
8080
8080
B680
B680
8080
8080
BE80
9C80
8080
8080
8080
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR invsmileface
ENCODING 9787
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
FF80
FF80
C980
C980
FF80
FF80
C180
E380
FF80
FF80
FF80
7F00
0000
0000
0000
0000
ENDCHAR

STARTCHAR sun
ENCODING 9788
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
8880
4900
2A00
1C00
F780
1C00
2A00
4900
8880
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR female
ENCODING 9792
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
3E00
4100
4100
4100
4100
4100
4100
3E00
0800
0800
7F00
0800
0800
0000
0000
0000
0000
ENDCHAR

STARTCHAR male
ENCODING 9794
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0F80
0180
0280
0480
0880
7C00
8200
8200
8200
8200
8200
8200
7C00
0000
0000
0000
0000
ENDCHAR

STARTCHAR spade
ENCODING 9824
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0800
0800
1C00
3E00
7F00
FF80
FF80
FF80
FF80
6B00
0800
0800
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR club
ENCODING 9827
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1C00
3E00
3E00
1C00
0800
6B00
FF80
FF80
FF80
6B00
0800
0800
3E00
0000
0000
0000
0000
ENDCHAR

STARTCHAR heart
ENCODING 9829
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
6300
F780
FF80
FF80
FF80
FF80
7F00
3E00
1C00
0800
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR diamond
ENCODING 9830
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0800
1C00
3E00
7F00
FF80
7F00
3E00
1C00
0800
0000
0000
0000
0000
0000
0000
ENDCHAR

STARTCHAR musicalnote
ENCODING 9834
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
1F80
1080
1080
1F80
1000
1000
1000
1000
1000
1000
1000
1000
E000
0000
0000
0000
0000
ENDCHAR

STARTCHAR musicalnotedbl
ENCODING 9835
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4100
4100
7F00
4100
4100
4100
4100
4100
4100
4100
4200
8000
0000
0000
0000
0000
ENDCHAR

STARTCHAR uniE0A0
ENCODING 57504
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
7000
7000
7300
7780
7FC0
7300
7300
7300
7300
7300
6300
4700
0E00
1C00
3800
7000
7000
7000
7000
7000
ENDCHAR

STARTCHAR uniE0A1
ENCODING 57505
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
2000
2000
2000
2000
2000
2000
2000
2000
3E00
0000
1100
1100
1900
1500
1500
1300
1100
1100
0000
ENDCHAR

STARTCHAR uniE0A2
ENCODING 57506
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0C00
1E00
1300
3100
3100
3100
3F00
7F80
7380
7380
7B80
7B80
7B80
7B80
7F80
7F80
0000
0000
0000
0000
ENDCHAR

STARTCHAR uniE0B0
ENCODING 57520
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
8000
C000
E000
F000
F800
FC00
FE00
FF00
FF80
FFC0
FFC0
FF80
FF00
FE00
FC00
F800
F000
E000
C000
8000
ENDCHAR

STARTCHAR uniE0B1
ENCODING 57521
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
8000
4000
2000
1000
0800
0400
0200
0100
0080
0040
0040
0080
0100
0200
0400
0800
1000
2000
4000
8000
ENDCHAR

STARTCHAR uniE0B2
ENCODING 57522
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0040
00C0
01C0
03C0
07C0
0FC0
1FC0
3FC0
7FC0
FFC0
FFC0
7FC0
3FC0
1FC0
0FC0
07C0
03C0
01C0
00C0
0040
ENDCHAR

STARTCHAR uniE0B3
ENCODING 57523
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0040
0080
0100
0200
0400
0800
1000
2000
4000
8000
8000
4000
2000
1000
0800
0400
0200
0100
0080
0040
ENDCHAR

STARTCHAR uniF6BE
ENCODING 63166
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
0000
0000
0000
0000
0600
0200
0200
0200
0200
0200
0200
0200
0200
2200
2200
1C00
0000
ENDCHAR

STARTCHAR uniFFFD
ENCODING 65533
SWIDTH 500 0
DWIDTH 10 0
BBX 10 20 0 -4
BITMAP
0000
0000
0000
7F00
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
4100
7F00
0000
0000
0000
0000
ENDCHAR

ENDFONT
