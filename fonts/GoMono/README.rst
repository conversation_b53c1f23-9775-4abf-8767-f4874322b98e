Go Mono for Powerline
=====================

:Font creator: Bigelow & Holmes
:Source: https://go.googlesource.com/image
:Patched by: `<PERSON><PERSON> <https://github.com/AnwarShah>`_

Go monospace font initially developed for testing go user interface toolkit. It 
is being said that go source code looks particularly good when displayed in Go 
fonts, with things like punctuation characters easily distinguishable and 
operators lined up and placed consistently.
