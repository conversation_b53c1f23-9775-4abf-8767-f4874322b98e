ProFont for Powerline
=======================

:Original Font creator:  <PERSON>
:ProFont 2.2 creator: <PERSON>
:Modern ProFont creator:  <PERSON>
:Windows TrueType creator with some Tweak: <PERSON>
:Source: http://tobiasjung.name/profont/
:License: MIT License
:Patched by: `<PERSON><PERSON> <https://github.com/AnwarShah>`_

ProFont is a monospaced font created to be a most readable font for programming. Thus, it has slashed zeros; distinct forms of I, l and 1; and it is designed to look good a really small sizes so you can cram lots of lines of code into the window of your editor.

The original ProFont version was made for the classic Mac OS and contained bitmapped shapes at 7, 9, 10, 12, 14, 18 and 24 points. Using this sizes with anti-aliasing turned off, you get a clean and crisp programming font.
The TrueType versions for Windows contain outline shapes only so they might not look as great as the bitmap version, but still are quite nice nonetheless.