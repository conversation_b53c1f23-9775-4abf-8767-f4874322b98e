Roboto Mono is a monospaced addition to the
`Roboto <https://www.google.com/fonts/specimen/Roboto>`__ type family.
Like the other members of the Roboto family, the fonts are optimized for
readability on screens across a wide variety of devices and reading
environments. While the monospaced version is related to its variable
width cousin, it doesn’t hesitate to change forms to better fit the
constraints of a monospaced environment. For example, narrow glyphs like
‘I’, ‘l’ and ‘i’ have added serifs for more even texture while wider
glyphs are adjusted for weight. Curved caps like ‘C’ and ‘O’ take on the
straighter sides from Roboto Condensed.

Special consideration is given to glyphs important for reading and
writing software source code. Letters with similar shapes are easy to
tell apart. Digit ‘1’, lowercase ‘l’ and capital ‘I’ are easily
differentiated as are zero and the letter ‘O’. Punctuation important for
code has also been considered. For example, the curly braces ‘{ }’ have
exaggerated points to clearly differentiate them from parenthesis ‘( )’
and braces ‘[ ]’. Periods and commas are also exaggerated to identify
them more quickly. The scale and weight of symbols commonly used as
operators have also been optimized.

:Font creator: Google
:Source: https://github.com/google/fonts/tree/master/apache/robotomono
:Patched by: `<PERSON> <https://github.com/aeroevan>`_
