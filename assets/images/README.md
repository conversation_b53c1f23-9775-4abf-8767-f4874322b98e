# 图片资源说明

## 站点设置管理页面所需图标

为了确保站点设置管理页面的正常显示，需要准备以下图标文件：

### 必需的图标文件

1. **search.png** - 搜索图标
   - 尺寸：32rpx × 32rpx
   - 用途：搜索框内的搜索图标
   - 建议：使用灰色或半透明的放大镜图标

2. **info.png** - 信息图标
   - 尺寸：36rpx × 36rpx
   - 用途：使用说明卡片的信息图标
   - 建议：使用蓝色的信息符号或问号图标

3. **arrow-right.png** - 右箭头图标
   - 尺寸：24rpx × 24rpx
   - 用途：管理人员列表项的导航箭头
   - 建议：使用灰色的右指向箭头

4. **site.png** - 站点图标
   - 尺寸：36rpx × 36rpx
   - 用途：设置站点权限按钮的图标
   - 建议：使用建筑物或位置相关的图标

5. **close.png** - 关闭图标
   - 尺寸：24rpx × 24rpx
   - 用途：弹窗的关闭按钮
   - 建议：使用白色的X符号

6. **check.png** - 勾选图标
   - 尺寸：20rpx × 20rpx
   - 用途：复选框的勾选状态
   - 建议：使用白色的对勾符号

7. **empty.png** - 空状态图标
   - 尺寸：120rpx × 120rpx（主要空状态）/ 100rpx × 100rpx（站点空状态）
   - 用途：数据为空时的占位图标
   - 建议：使用简洁的空盒子或暂无数据相关图标

### 图标风格建议

- **设计风格**：简洁、现代化，符合微信小程序设计规范
- **颜色搭配**：与页面主色调（#667eea, #764ba2）协调
- **格式要求**：PNG格式，支持透明背景
- **尺寸要求**：高清图标，支持不同分辨率设备

### 替代方案

如果暂时没有相应的图标文件，可以：

1. **使用Unicode字符**：在WXML中直接使用Unicode符号替代
2. **使用微信小程序内置图标**：参考WeUI等UI库的图标
3. **使用CSS绘制**：对于简单的几何图形可以用CSS绘制
4. **在线图标资源**：从免费图标网站下载，如阿里巴巴矢量图标库

### 图标放置位置

```
assets/images/
├── search.png
├── info.png
├── arrow-right.png
├── site.png
├── close.png
├── check.png
└── empty.png
```

### 使用方式

在WXML文件中通过image标签引用：

```xml
<image class="search-icon" src="/assets/images/search.png" mode="aspectFit"/>
```

注意：路径为绝对路径，从项目根目录开始。 